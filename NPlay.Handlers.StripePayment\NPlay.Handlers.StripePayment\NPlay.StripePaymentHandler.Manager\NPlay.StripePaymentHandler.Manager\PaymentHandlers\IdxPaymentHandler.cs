﻿#region N-Play Copyright Banner
/*******************************************************************\
 * Copyright © N-Play RE LLC. 2013.  All rights reserved.           *
 *                                                                  *
 * This code is property of N-Play and cannot be used without       *
 * explicit permission given by an official N-Play representative.  *
 * For details contact: <EMAIL>                               *
 \******************************************************************/
#endregion

using System;
using System.Linq;
using System.Net.Mail;
using AutoMapper;
using NPlay.Common.Abstract;
using NPlay.Common.Abstract.Emailer;
using NPlay.Common.EchoSign.Services;
using NPlay.Common.EchoSign.EchoSignService;
using NPlay.Common.Models;
using NPlay.Common.Models.Emails;
using NPlay.StripePaymentHandler.Core.Models;
using System.Collections.Generic;
using NPlay.Common.Models.Services;
using NPlay.Common;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.Common.BaseRepository;
using NPlay.Common.Messaging.Abstract;
using Nest;
using NPlay.Common.Models.Emails.Engine;
using NPlay.Common.Messaging.Events;
using NPlay.BusinessLogic.Payment.Abstract;
using NPlay.Common.Messaging;
using NPlay.Common.Models.ExtendedServices.Abstract;
using NPlay.Common.Enums;

namespace NPlay.StripePaymentHandler.Core.PaymentHandlers
{
    public class IdxPaymentHandler : InvoicePaymentHandlerBase
    {
        private readonly IHomeSearchPaymentProcessor HomeSearchPaymentProcessor;
        private readonly IAgentRepository AgentRepository;
        private readonly ISearchRepository SearchRepository;
        private readonly IIdxServiceHelper IdxServiceHelper;
        private readonly IAgentClient AgentClient;
        private readonly IAfordalHelper AfordalHelper;

        static IdxPaymentHandler()
        {
            Mapper.Initialize(x => x.AddProfile<IdxPaymentHandlerMappingProfile>());
        }

        public IdxPaymentHandler(IRepositoryFactory repo,
                                 IHomeSearchPaymentProcessor paymentProcessor,
                                 IEmailer emailer,
                                 IPaymentNotificationManagerOld notificationManager,
                                 ISearchRepository searchRepository,
                                 IEventBroker eventBroker,
                                 IAgentClient agentClient,
                                 IRepository<TransactionLog> transactionLogRepository,
                                 IUnitOfWorkFactory uowFactory,
                                 ITransactionLogManager transactionLogManager,
                                 IEmailGenerator emailGenerator,
                                 ICampaignMonitorSubscriberHandler campaignMonitorSubscriberHandler,
                                 IRepository<SubscriptionsMissingFundingSource> subscriptionsMissingFundingSourceRepository,
                                 IRepository<Membership> membershipRepository,
                                 IIdxServiceHelper idxServiceHelper,
                                 ILogger logger,
                                 IPaymentSystem paymentSystem,
                                 ISubscriptionHelper subscriptionHelper,
                                 IAfordalHelper afordalHelper
            )
            : base(transactionLogRepository,
                   uowFactory,
                   transactionLogManager,
                   paymentProcessor,
                   campaignMonitorSubscriberHandler,
                   subscriptionsMissingFundingSourceRepository,
                   membershipRepository,
                   repo,
                   emailer,
                   eventBroker,
                   logger,
                   notificationManager,
                   emailGenerator,
                   paymentSystem,
                   subscriptionHelper)
        {
            HomeSearchPaymentProcessor = paymentProcessor;
            AgentRepository = Repositories.AgentRepository;
            SearchRepository = searchRepository;
            IdxServiceHelper = idxServiceHelper;
            AgentClient = agentClient;
            AfordalHelper = afordalHelper;
        }

        protected override ProductType ProductType
        {
            get
            {
                return ProductType.IDX;
            }
        }

        protected override TransactionTypes TransactionType
        {
            get
            {
                return TransactionTypes.SearchAllianceSubscription;
            }
        }

        protected override bool DoNotProcessIfAgentIsNull
        {
            get
            {
                return false;
            }
        }

        protected override void PaymentSuccessful(Agent agent, FundingSource fundingSource, InvoicePaymentResponse paymentResult, WebHookHandlerResult result)
        {
            // If this is an IDX with Afordal product, cancel existing IDX subscriptions first
            if (fundingSource?.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.AfordalIncluded ||
                fundingSource?.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded)
            {
                CancelExistingIdxSubscriptions(agent.Id, fundingSource.Id);
            }

            if (agent != null)
            {
                var mls = agent.MultipleListingService;
                var dateRegisteredFilled = false;
                if (mls != null)
                {
                    var fillDateRegistered = agent.HomeSearchRegisteredDateTime == null && ShouldFillHomeSearchRegisteredDate(agent, mls);

                    if (fillDateRegistered)
                    {
                        agent.HomeSearchRegisteredDateTime = DateTime.UtcNow;
                        dateRegisteredFilled = true;
                        //Mark said that we don't need to send service resumed email since we are sending ProductThankYouEmail.
                        UpdateAgentMlsModel(agent, true);
                    }
                }

                agent.ListingsImportedDateTime = agent.ListingsImportedDateTime ?? DateTime.UtcNow;
                agent.IdxDisabledTypeId = null;
                AgentRepository.Save(agent);

                if (dateRegisteredFilled)
                {
                    AddIdxActivatedCSNote(agent.Id);
                    EmailGenerator.SendIDXZapEmail(fundingSource.MembershipId);
                }

                DeleteFailedPaymentSubscriber(agent, Settings.FailedPaymentIDXSubscriberListId);
            }

            fundingSource.SuccessfulPayment();
            FundingSourceRepository.Save(fundingSource);

            EmailGenerator.SendProductThankYouEmail(fundingSource.MembershipId, fundingSource.Id, paymentResult.ChargeAmount);
            if (AfordalHelper.CheckForAfordalSubscription(agent.Id))
            {
                AfordalHelper.AfordalActivated(agent.Id);
            }
        }

        protected override void PaymentFailed(Agent agent, FundingSource fundingSource, InvoicePaymentResponse paymentResult, WebHookHandlerResult result)
        {
            if (agent != null)
            {
                agent.HomeSearchRegisteredDateTime = null;
                agent.IdxDisabledTypeId = (int)IdxDisabledType.PaymentFailed;
                AgentRepository.Save(agent);

                EventBroker.Publish<IDXCancelledEvent, BasicAgent>(new IDXCancelledEvent()
                {
                    Payload = new BasicAgent()
                    {
                        Id = agent.Id
                    }
                });

                UpdateAgentMlsModel(agent, false);

                AddFailedPaymentSubscriber(agent, Settings.FailedPaymentIDXSubscriberListId, paymentResult.AmountDue, result.AttemptCount, fundingSource.ProductPlan);

                //if (result.AttemptCount == 3)  //commented out as part of NPLAY-6468
                //    SendThirdFailedPaymentEmailToCS(agent.Id, fundingSource.Id);

                NotificationManager.Send(NotificationType.IdxPaymentFailed, agent, fundingSource.Id);
            }

            if (fundingSource.Active)
            {
                fundingSource.FailedPayment();
                FundingSourceRepository.Save(fundingSource);
            }
        }

        #region Private Methods

        private MLSAgent GetMlsAgent(Agent agent)
        {
            var criteria = new SearchCriteria<MLSAgent>(1)
            {
                Index = SearchIndex.MLSAgents
            };
            criteria.Filters.Add(Filter<MLSAgent>.Term(x => x.MlsAgentId, agent.MLSAgentId));
            criteria.Filters.Add(Filter<MLSAgent>.Term(x => x.MlsIdParsed, agent.MLSOrgId));
            var mlsAgent = SearchRepository.Get(criteria).Documents.SingleOrDefault();
            return mlsAgent;
        }

        private void UpdateAgentMlsModel(Agent agent, bool paymentSuccess)
        {
            var model = new AgentMLSModel
            {
                AgentId = agent.Id,
                MLSOrgId = agent.MLSOrgId,
                MLSAgentId = agent.MLSAgentId,
                MLSOfficeId = agent.MLSOfficeId,
                HomeSearchRegisteredDateTime = paymentSuccess ? (DateTime?)DateTime.UtcNow : null,
                IdxDisabledTypeId = paymentSuccess ? null : (int?)IdxDisabledType.PaymentFailed,
                ListingsImportedDateTime = agent.ListingsImportedDateTime
            };
            AgentClient.UpdateAgentMls(model);
        }

        private bool ShouldFillHomeSearchRegisteredDate(Agent agent, MultipleListingService mls)
        {
            var fillDateRegistered = false;
            var mlsAgent = GetMlsAgent(agent);
            var mlsType = IdxServiceHelper.GetMlsAuthorizationType(mls, mlsAgent, agent, true);

            switch (mlsType)
            {
                case MlsAuthorizationType.DataVault:
                    {
                        if (agent.ReDataVaultVerify != null && agent.ReDataVaultVerify.Approved)
                            fillDateRegistered = true;
                        break;
                    }
                default:
                    {
                        if (mlsType.HasFlag(MlsAuthorizationType.AgreementWaived) || mlsType.HasFlag(MlsAuthorizationType.None))
                            fillDateRegistered = true;
                        else if (!string.IsNullOrEmpty(agent.DocumentKey))
                        {
                            if (agent.DocumentKey.ToLower() == "override")
                                fillDateRegistered = true;
                            else
                            {
                                var docInfo = DocumentService.GetDocumentInfo(agent.DocumentKey);
                                if (docInfo != null && docInfo.status != null && docInfo.status == AgreementStatus.SIGNED)
                                    fillDateRegistered = true;
                            }
                        }
                        break;
                    }
            }
            return fillDateRegistered;
        }

        private void AddIdxActivatedCSNote(int agentId)
        {
            var csNote = new AgentCsNote
            {
                MembershipId = agentId,
                Note = "IDX Activated",
                DateCreated = DateTime.UtcNow
            };
            Repositories.AgentCsNoteRepository.Save(csNote);
        }

        private void CancelExistingIdxSubscriptions(int agentId, long newlyPaidFundingSourceId)
        {
            try
            {
                // Find all active IDX funding sources for this agent except the newly paid one
                var existingIdxFundingSources = FundingSourceRepository.FundingSources
                    .Where(fs => fs.MembershipId == agentId &&
                                fs.ProductTypeId == (int)ProductType.IDX &&
                                fs.Active == true &&
                                fs.Id != newlyPaidFundingSourceId)
                    .ToList();

                foreach (var existingFundingSource in existingIdxFundingSources)
                {
                    try
                    {
                        // Cancel the subscription in Stripe immediately (not at period end)
                        if (!string.IsNullOrEmpty(existingFundingSource.SubscriptionId))
                        {
                            PaymentSystem.Subscription.CancelSubscription(existingFundingSource.ProviderId, existingFundingSource.SubscriptionId, false);
                            Logger.Info($"Cancelled existing IDX subscription {existingFundingSource.SubscriptionId} immediately for agent {agentId} due to Afordal IDX purchase");
                        }

                        // PaymentSystem.Subscription.CancelSubscription will trigger a webhook that updates the funding source automatically.
                        // Setting inactive here for good measure.
                        existingFundingSource.Active = false;
                        FundingSourceRepository.Save(existingFundingSource);

                        Logger.Info($"Deactivated funding source {existingFundingSource.Id} for agent {agentId} due to Afordal IDX purchase");
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"Error cancelling existing IDX subscription {existingFundingSource.SubscriptionId} for agent {agentId}: {ex.Message}", null, ex, this, "CancelExistingIdxSubscriptions");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in CancelExistingIdxSubscriptions for agent {agentId}: {ex.Message}", null, ex, this, "CancelExistingIdxSubscriptions");
            }
        }

        #endregion
    }

    public class IdxPaymentHandlerMappingProfile : Profile
    {
        public IdxPaymentHandlerMappingProfile()
        {
            CreateMap<Agent, IdxServiceDisabledEmail>()
                .ForMember(dest => dest.AgentImageUrl, opt => opt.MapFrom(a => a.GetProfileImage()))
                .ForMember(dest => dest.AgentName, opt => opt.MapFrom(a => a.Membership.Name()))
                .ForMember(dest => dest.FirstName, opt => opt.MapFrom(a => a.Membership.FirstName))
                .ForMember(dest => dest.BrokerName, opt => opt.MapFrom(a => a.Brokerage == null ? string.Empty : a.Brokerage.BrokerageName))
                .ForMember(dest => dest.DateRegistered, opt => opt.MapFrom(a => a.Membership.DateRegistered == null ? string.Empty : a.Membership.DateRegistered.Value.ToString("d")))
                .ForMember(dest => dest.EmailLink, opt => opt.MapFrom(u => string.Format("mailto:{0}", u.Membership.Email)))
                .ForMember(dest => dest.UnsubscribeUrl, opt => opt.MapFrom(u => u.UnsubscribeUrl.AbsoluteUri))
                .ForMember(dest => dest.To, opt => opt.MapFrom(u => new MailAddressCollection { new MailAddress(!string.IsNullOrEmpty(u.Membership.Email) ? u.Membership.Email : "<EMAIL>") }))
                .ForMember(dest => dest.From, opt => opt.MapFrom(u => new MailAddress(Settings.BillingEmail, "Real Estate Agent Directory")))
                .ForMember(dest => dest.Subject, opt => opt.MapFrom(u => "IDX Home Search Service - renewal failed. We need your help."))
                .ForMember(dest => dest.Bcc, opt => opt.MapFrom(u => new MailAddressCollection { new MailAddress(Settings.BccEmail) }))
                .ForMember(dest => dest.RefundAmount, opt => opt.MapFrom(u => string.Format("${0}", Settings.FailedPaymentRefundAmount)))
                .ForMember(dest => dest.UpdateFundingSourceUrl, opt => opt.MapFrom(u => string.Format("{0}idxfailpayment", Settings.GatewayUrl)));

            CreateMap<InvoicePaymentResponse, IdxPaymentResponse>();
        }
    }
}
