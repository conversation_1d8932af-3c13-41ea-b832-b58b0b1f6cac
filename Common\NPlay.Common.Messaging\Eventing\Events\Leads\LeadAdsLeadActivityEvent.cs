﻿using NPlay.Common.Messaging.Models;
using System.Collections.Generic;
using NPlay.Common.Messaging.Retry;
using NPlay.Common.Models;

namespace NPlay.Common.Messaging.Events
{
    public class LeadAdsLeadActivityEvent : MediumPriorityEvent<LeadAdsLeadActivity>
    {
        public override string Name
        {
            get
            {
                return "LeadAdsLeadActivity202508TEST";
            }
        }

        protected override void RegisterMappings()
        {
            EventMappings.Instance.RegisterMapping(this.GetType(), new List<TargetTypes>() { TargetTypes.GuaranteedDelivery });
        }

        protected override void RegisterRetryPolicy()
        {
            EventMappings.Instance.RegisterRetryPolicy(this.GetType(), new ExponentialBackoffPolicy<LeadAdsLeadActivityEvent>());
        }
    }
}
