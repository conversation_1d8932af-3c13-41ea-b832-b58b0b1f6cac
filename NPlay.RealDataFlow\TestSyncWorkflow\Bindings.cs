﻿using AutoMapper;
using MLS.Synchronization;
using MLS.Synchronization.Abstract;
using MLS.Synchronization.GeoCoding;
using MLS.Synchronization.Images;
using MLS.Synchronization.Listings;
using MLS.Synchronization.Models;
using MLSAccounts;
using MLSAccounts.Abstract;
using Ninject;
using Ninject.Extensions.Factory;
using Ninject.Modules;
using Nplay.Services.Data;
using NPlay.BusinessLogic.AgentListingWebsite;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Compression;
using NPlay.Common.Compression.Abstract;
using NPlay.Common.Logging;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Contextual;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Models.Mapping;
using NPlay.Common.Models.MLS;
using NPlay.Common.Queues;
using NPlay.Common.Repository;
using NPlay.Common.Search;
using NPlay.Common.ServiceAgents.ListingReprocessor;
using NPlay.Common.ServiceAgents.Messaging;
using NPlay.Common.ServiceAgents.MLSQuery;
using NPlay.Common.ServiceAgents.NPlayApi;
using NPlay.Common.ServiceAgents.RETSConnections;
using NPlay.Common.ServiceAgents.RETSMetadata;
using NPlay.Common.Services.Abstract;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.ReadDataFlow.Data;
using NPlay.RealDataFlow.Data;
using NPlay.RealDataFlow.Mapping;
using NPlay.RealDataFlow.Mapping.Abstract;
using NPlay.RealDataFlow.Mapping.Maps;
using NPlay.RealDataFlow.Mapping.Models.Maps;
using NPlay.RealDataFlow.Mapping.Models.Matching;
using NPlay.RealDataFlow.RETSConnector;
using NPlay.RealDataFlow.RETSConnector.Abstract;
using RealData.Simulation;
using RESO.Connector;
using RESO.Connector.Abstract;
using RESO.Synchronization;
using RESO.Synchronization.RESOData;
using RETS.Parsing;
using RETS.Parsing.Abstract;
using RETS.Web;
using RETS.Web.Abstract;
using RETSDataSynchronization;
using RETSDataSynchronization.Images;
using RETSDataSynchronization.MapExecution;
using RETSDataSynchronization.RETSData;

namespace TestSyncWorkflow
{
    public class Bindings : NinjectModule
    {
        public override void Load()
        {
            Bind<IUnitOfWorkFactory>().ToFactory(() => new UnitOfWorkFactory());
            Bind<IDBFactory<MapContext>>().To<DBFactory<MapContext>>().InThreadScope();
            Bind<IUnitOfWork>().To<UnitOfWork<MapContext>>().InThreadScope().Named("Map");
            Bind<IRepository<Map>>().To<EntityRepositoryBase<Map, MapContext>>();
            Bind<IRepository<MapField>>().To<EntityRepositoryBase<MapField, MapContext>>();
            Bind<IRepository<MapNote>>().To<EntityRepositoryBase<MapNote, MapContext>>();
            Bind<IRepository<MapMatch>>().To<EntityRepositoryBase<MapMatch, MapContext>>();
            Bind<IRepository<MapPropertyType>>().To<EntityRepositoryBase<MapPropertyType, MapContext>>();
            Bind<IRepository<Related>>().To<EntityRepositoryBase<Related, MapContext>>();
            Bind<IRepository<MatchJobStatus>>().To<EntityRepositoryBase<MatchJobStatus, MapContext>>();
            Bind<IRepository<MappingListingStatus>>().To<EntityRepositoryBase<MappingListingStatus, MapContext>>();
            Bind<IListingStatusCacher>().To<ListingStatusCacher>();
            Bind<IListingStatusProvider>().To<ListingStatusProvider>();

            Bind<IDBFactory<SettingsContext>>().To<DBFactory<SettingsContext>>().InThreadScope();
            Bind<IUnitOfWork>().To<UnitOfWork<SettingsContext>>();
            Bind<IRepository<Setting>>().To<EntityRepositoryBase<Setting, SettingsContext>>();

            Bind<IRealDataMapper>().To<RealDataMapper>();

            //Mls Context
            Bind<IDBFactory<MlsContext>>().To<DBFactory<MlsContext>>().InThreadScope();
            Bind<IUnitOfWork>().To<UnitOfWork<MlsContext>>().InThreadScope().Named("Mls");
            Bind<IRepository<MultipleListingService>>().To<EntityRepositoryBase<MultipleListingService, MlsContext>>();
            Bind<IRepository<RETSCredential>>().To<EntityRepositoryBase<RETSCredential, MlsContext>>();
            Bind<IRepository<MLSParameter>>().To<EntityRepositoryBase<MLSParameter, MlsContext>>();
            Bind<IRepository<DataFeed>>().To<EntityRepositoryBase<DataFeed, MlsContext>>();
            Bind<IRepository<MLSActiveStatus>>().To<EntityRepositoryBase<MLSActiveStatus, MlsContext>>();
            Bind<IRepository<MLSMaster>>().To<EntityRepositoryBase<MLSMaster, MlsContext>>();
            Bind<IRepository<State>>().To<EntityRepositoryBase<State, MlsContext>>();
            Bind<IRepository<Membership>>().To<EntityRepositoryBase<Membership, PaymentContext>>();
            Bind<IDBFactory<PaymentContext>>().To<DBFactory<PaymentContext>>();
            Bind<IDBFactory<MLSRegionsByMasterContext>>().To<DBFactory<MLSRegionsByMasterContext>>().InThreadScope();
            Bind<IRepository<MLSRegionsByMaster>>().To<EntityRepositoryBase<MLSRegionsByMaster, MLSRegionsByMasterContext>>();
            Bind<IRepository<RESOCredential>>().To<EntityRepositoryBase<RESOCredential, MlsContext>>();

            //MLS_Agents context
            Bind<IDBFactory<MLS_AgentContext>>().To<DBFactory<MLS_AgentContext>>().InThreadScope();
            Bind<IUnitOfWork>().To<UnitOfWork<MLS_AgentContext>>().Named("MlsAgent");
            Bind<IRepository<MLSAgent>>().To<EntityRepositoryBase<MLSAgent, MLS_AgentContext>>();

            Bind<ICachedMLSRepository>().To<MLSCache>().InSingletonScope();
            Bind<IMLSRepository>().To<MLSRepository>();

            // Agent listing site integration
            Bind<IDBFactory<AgentContext>>().To<DBFactory<AgentContext>>();
            Bind<IRepository<Agent>>().To<EntityRepositoryBase<Agent, AgentContext>>();
            Bind<IRepository<ZipCode>>().To<EntityRepositoryBase<ZipCode, AgentContext>>();
            Bind<IAgentListingSiteManager>().To<AgentListingSiteManager>();
            Bind<IListingImageLazyLoadManager>().To<ListingImageLazyLoadManager>().InSingletonScope();

            Bind<IEventBroker>().To<TrackingEventBroker>().InSingletonScope();
            Bind<IEventPrismFactory>().To<RabbitEventPrismFactory>().InSingletonScope();
            Bind<IQueueFactory>().To<RabbitQueueFactory>().InSingletonScope();
            Bind<IContextualEventBroker>().To<ContextualEventBroker>().InSingletonScope();
            Bind<IProcessingContext>().ToMethod(c => new ProcessingContext() { IsReprocessing = true }).InThreadScope();

            Bind<IRETSSearchConnector>().To<RETSSearchConnector>().InThreadScope();
            Bind<IRETSMetadataConnector>().To<RETSMetadataConnector>().InThreadScope();
            Bind<IRETSImagesConnector>().To<RETSImagesConnector>().InThreadScope();
            Bind<IMetadataHandler>().To<MetadataHandler>();
            Bind<IMetadataFileHandler>().To<MetadataFileHandler>();
            Bind<IMetadataCacheHandler>().To<MetadataCacheHandler>();
            Bind<ICompressor>().To<GZipCompressor>();
            Bind<IRETSParser>().To<RETSParser>();
            Bind<IRETSWebClientFactory>().To<RETSWebClientFactory>();
            Bind<IRETSConnectionManagerClient>().To<RETSConnectionManagerClient>();
            Bind<IRETSMetadataManagerClient>().To<RETSMetadataManagerClient>();
            Bind<IMlsSynchronizationClient>().To<MlsSynchronizationClient>();
            Bind<IListingProcessorClient>().To<ListingProcessorClient>();

            //MLS Synchronization Context
            Bind<IMlsSynchronizationContextProvider>().ToMethod(p =>
            {
                MlsSynchronizationContextProvider.Instance.MLSCache = MlsSynchronizationContextProvider.Instance.MLSCache ?? this.Kernel.Get<ICachedMLSRepository>();
                return MlsSynchronizationContextProvider.Instance;
            }).InSingletonScope();

            // RESO 
            Bind<IGetRESOFeedData>().To<GetRESOData>();
            Bind<IBridgeQueryConnector>().To<BridgeQueryConnector>();
            Bind<ITrestleQueryConnector>().To<TrestleQueryConnector>();
            //Bind<ITrestleQueryConnector>().To<ThrottledTrestleQueryConnector>();
            Bind<IUtahRealEstateQueryConnector>().To<UtahRealEstateQueryConnector>();
            Bind<IUtahRealEstateMediaConnector>().To<UtahRealEstateMediaConnector>();
            Bind<IRMLSQueryConnector>().To<RMLSQueryConnector>();
            Bind<IRMLSMediaConnector>().To<RMLSMediaConnector>();
            Bind<ISparkQueryConnector>().To<SparkQueryConnector>();
            Bind<ISparkMediaConnector>().To<SparkMediaConnector>();
            Bind<IParagonQueryConnector>().To<ParagonQueryConnector>();
            Bind<IParagonMediaConnector>().To<ParagonMediaConnector>();
            Bind<IRapattoniMediaConnector>().To<RapattoniMediaConnector>();
            Bind<IRapattoniQueryConnector>().To<RapattoniQueryConnector>();
            Bind<IPerchwellMediaConnector>().To<PerchwellMediaConnector>();
            Bind<IPerchwellQueryConnector>().To<PerchwellQueryConnector>();
            Bind<IOpenMLSQueryConnector>().To<OpenMLSQueryConnector>();
            Bind<IOpenMLSMediaConnector>().To<OpenMLSMediaConnector>();
            Bind<IMlsGridListingQueryConnector>().To<MlsGridListingQueryConnector>();
            Bind<IMapRESOData>().To<MapRESOData>();
            Bind<IGeoCoder>().To<GeoCoder>();
            Bind<IRawListingRepository>().To<RawListingRepository>();
            Bind<IRESOImageDownloader>().To<RESOImageDownloader>();
            Bind<IMlsGridMediaConnector>().To<MlsGridMediaConnector>();
            Bind<IBridgeMediaConnector>().To<BridgeMediaConnector>();
            Bind<ITrestleMediaConnector>().To<TrestleMediaConnector>();
            Bind<IImageStreamDownloader>().To<ImageStreamDownloader>();

            // RETS
            Bind<IGetMLSFeedData>().To<GetRETSData>();
            Bind<IMapRETSData>().To<MapRETSData>();
            Bind<ICanonTranslator>().To<CanonTranslator>();
            Bind<ICanonImageTranslator>().To<CanonImageTranslator>();
            Bind<IRemoveListingService>().To<RemoveListingService>();
            Bind<ICanonAgentSaveToSQL>().To<CanonAgentSaveToSQL>();

            // Data Feeds
            Bind<IDataFeedManager>().To<DataFeedManager>();

            //SearchRepo
            Bind<ISearchRepository>().To<SearchRepository>().InThreadScope();

            Bind<IRETSQueryHandler>().To<RETSQueryHandler>();

            Bind<IRETSImageDownloader>().To<RETSImageDownloader>();

            // Listing Translation to Active
            Bind<IActiveListingService>().To<ActiveListingService>();
            Bind<IListingImageManager>().To<ListingImageManager>();
            Bind<IActiveListingRepository>().To<ActiveListingRepository>();
            Bind<IListingTypeAheadManager>().To<ListingTypeAheadManager>();
            Bind<IListingChangeNotifier>().To<ListingChangeNotifier>();
            Bind<IListingChangeConfigurationProvider>().To<ListingChangeConfigurationProvider>().InSingletonScope();

            // Simulations
            Bind<IDataFeedSynchronizationSimulator>().To<DataFeedSynchronizationSimulator>();

            //Logging
            Bind<ILogger>().To<Log4NetLogger>();

            Bind<IImageUploader>().To<CIFSImageUploader>();

            Bind<IRealDataClient>().To<RealDataClient>();

            Bind<IRemoveRETSData>().To<RemoveRETSData>();
            Bind<IRemoveRESOData>().To<RemoveRESOData>();
            Bind<IRESOQueryConnectorFactory>().To<RESOQueryConnectorFactory>();

            //delayed event client
            Bind<IDelayedEventClient>().To<DelayedEventClient>();

            Bind<IFTPConnector>().To<FTPConnector>();

            Bind<IMissingImagesProcessor>().To<MissingImagesProcessor>();
            Bind<IRemoveImageService>().To<RemoveImageService>();

            Bind<IMLSAccountManager>().To<MLSAccountManager>();

            Bind<IMLSQueryClient>().To<MLSQueryClient>();

            //AutoMapper
            Bind<IAutoMapperConfigurationProvider>().ToMethod(context => {
                var provider = new AutoMapperConfigurationProvider();

                provider.AddProfile<AgentMappingProfile>();
                provider.AddProfile<MatchedAgentMappingProfile>();
                provider.AddProfile<EsignMappingProfile>();
                provider.AddProfile<LeadMappingProfile>();
                provider.AddProfile<MLSPropertyMappingProfile>();
                provider.AddProfile<ServiceMappingProfile>();
                provider.AddProfile<SweepstakesMappingProfile>();
                provider.AddProfile<TransactionLogMappingProfile>();
                provider.AddProfile<MLSPropertyListingMappingProfile>();

                return provider;
            }).InSingletonScope();

            Bind<MapperConfiguration>().ToProvider<IAutoMapperConfigurationProvider>().InSingletonScope();
            Bind<IMapper>().ToMethod(context => context.Kernel.Get<MapperConfiguration>().CreateMapper());
        }
    }
}
