﻿#region N-Play Copyright Banner

/*******************************************************************\
 * Copyright © N-Play RE LLC. 2013.  All rights reserved.           *
 *                                                                  *
 * This code is property of N-Play and cannot be used without       *
 * explicit permission given by an official N-Play representative.  *
 * For details contact: <EMAIL>                               *
 \******************************************************************/

#endregion N-Play Copyright Banner

using NPlay.Common.Abstract;
using NPlay.Common.Enums;
using NPlay.Common.Models.PageEngage;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text.RegularExpressions;

// The Models Namespace.
namespace NPlay.Common.Models
{
    /// <summary>
    /// Class Agent.
    /// </summary>
    public class Agent : IDirectoryMember
    {
        #region Constants

        /// <summary>
        /// The minscorelength
        /// </summary>
        private const int MINSCORELENGTH = 250;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the <see cref="Agent"/> class.
        /// </summary>
        public Agent()
        {
            // Set default display parameters
            AboutYou_Display = true;
            AgentType_Display = true;
            Association_Display = true;
            BrokerPhone_Display = true;
            Credentials_Display = true;
            Disclosures_Display = true;
            DisplayEmail = true;
            DisplayPhone = true;
            DisplayStateLicenseNumber = true;
            Motto_Display = true;
            Interests_Display = true;
            DisplayRealtorIcon = true;
            Languages_Display = true;
            LicenseType_Display = true;
            Services_Display = true;  // Services and Experience are the same thing
            YearLicensed_Display = true;
            Video_Display = true;
            IsNew = false;
        }

        #endregion

        #region Properties

        public DirectoryMemberType? DirectoryMemberType { get; set; }

        [Column(TypeName = "varchar")]
        public string FacebookId { get; set; }

        public string AccessToken { get; set; }

        [NotMapped]
        public bool IsNew { get; set; }

        [NotMapped]
        public List<string> DataShareMlsIds { get; set; }



        public Uri FacebookProfileUrl(bool includeProtocol = false)
        {
            var protocol = includeProtocol ? "https:" : string.Empty;
            var url = string.Format("{0}//www.facebook.com/profile.php?id={1}", protocol, FacebookId);
            return new Uri(url);
        }

        public Uri FacebookProfileImageUrl(int width = 200, int height = 200)
        {
            var link = string.Format(@"https://graph.facebook.com/{0}/picture?width={1}&height={2}", FacebookId, width, height);
            return new Uri(link);
        }

        /// <summary>
        /// Gets or sets the about.
        /// *** NOTE:  This is now called "Professional Experience"
        /// </summary>
        /// <value>The about.</value>
        public string About { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [about you_ display].
        /// </summary>
        /// <value><c>true</c> if [about you_ display]; otherwise, <c>false</c>.</value>
        public bool AboutYou_Display { get; set; }

        /// <summary>
        /// Gets or sets the account specialists.
        /// </summary>
        /// <value>The account specialists.</value>
        public virtual ICollection<AccountSpecialist> AccountSpecialists { get; set; }

        /// <summary>
        /// Gets or sets the agent's account events.
        /// </summary>
        public virtual ICollection<AccountEvent> AccountEvents { get; set; }

        /// <summary>
        /// Gets or sets the agent brokerages.
        /// </summary>
        /// <value>The agent brokerages.</value>
        public virtual AgentByBrokerage AgentBrokerages { get; set; }


        /// <summary>
        /// Gets the app ID for the first Facebook page listed as an "agent directory" page
        /// </summary>
        /// <value>The facebook agent directory's page ID.</value>
        public string AgentDirectoryPageId
        {
            get
            {
                string agentDirectoryPageId = null;
                if (FacebookPages != null)
                {
                    FacebookPage agentDirectoryPage = FacebookPages.Where(a => a.AgentDirectory == true)
                                        .OrderBy(a => a.PageInsertedDate)
                                        .FirstOrDefault();

                    if (agentDirectoryPage != null)
                        agentDirectoryPageId = agentDirectoryPage.Id;
                }
                return agentDirectoryPageId;
            }
        }

        /// <summary>
        /// Gets the app ID for the first Facebook page listed as a "featured listings" page
        /// </summary>
        /// <value>The facebook agent featured listings page ID.</value>
        public string AgentFeaturedListingsPageId
        {
            get
            {
                string agentFeaturedListingsPageId = null;
                if (FacebookPages != null)
                {
                    FacebookPage agentFeaturedListingsPage = FacebookPages.Where(a => a.FeaturedListings == true)
                                        .OrderBy(a => a.PageInsertedDate)
                                        .FirstOrDefault();

                    if (agentFeaturedListingsPage != null)
                        agentFeaturedListingsPageId = agentFeaturedListingsPage.Id;
                }
                return agentFeaturedListingsPageId;
            }
        }

        /// <summary>
        /// Gets the app ID for the first Facebook page listed as an "agent profile" page
        /// </summary>
        /// <value>The facebook agent profile's page ID.</value>
        public string AgentProfilePageId
        {
            get
            {
                string agentProfilePageId = null;
                if (FacebookPages != null)
                {
                    FacebookPage agentProfilePage = FacebookPages.Where(a => a.AgentProfile == true)
                                        .OrderBy(a => a.AgentProfileAddDate)
                                        .FirstOrDefault();

                    if (agentProfilePage != null)
                        agentProfilePageId = agentProfilePage.Id;
                }
                return agentProfilePageId;
            }
        }

        /// <summary>
        /// Gets the app ID for the first Facebook page listed as a "property search" page
        /// </summary>
        /// <value>The facebook property search's page ID.</value>
        public string AgentPropertySearchPageId
        {
            get
            {
                string agentPropertySearchPageId = null;
                if (FacebookPages != null)
                {
                    FacebookPage agentPropertySearchPage = FacebookPages.Where(a => a.PropertySearch == true)
                                        .OrderBy(a => a.PropertySearchAddDate)
                                        .FirstOrDefault();

                    if (agentPropertySearchPage != null)
                        agentPropertySearchPageId = agentPropertySearchPage.Id;
                }
                return agentPropertySearchPageId;
            }
        }

        /// <summary>
        /// Gets or sets the profile agent type (Buyer's Agent, Seller's Agent, Both, etc)
        /// </summary>
        /// <value>The agent type.</value>
        public string AgentType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [AgentType_Display].
        /// </summary>
        /// <value><c>true</c> if [AgentType_Display]; otherwise, <c>false</c>.</value>
        public bool AgentType_Display { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [agent signed].
        /// </summary>
        /// <value><c>true</c> if [agent signed]; otherwise, <c>false</c>.</value>
        public bool AgentSigned { get; set; }

        /// <summary>
        /// Gets or sets the agent video URL.
        /// </summary>
        /// <value>The agent video URL.</value>
        public string AgentVideoURL { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Video_Display].
        /// </summary>
        /// <value><c>true</c> if [Video_Display]; otherwise, <c>false</c>.</value>
        public bool Video_Display { get; set; }

        /// <summary>
        /// Gets or sets the Agent Zip Codes / Service Areas
        /// </summary>
        /// <value>The agent Agent Zip Codes / Service Areas.</value>
        public virtual ICollection<AgentZip> AgentZips { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [AgentZips_ display].
        /// </summary>
        /// <value><c>true</c> if [AgentZips_ display]; otherwise, <c>false</c>.</value>
        public bool AgentZips_Display { get; set; }

        /// <summary>
        /// Gets or sets the Service Areas
        /// </summary>
        /// <value>The agent service areas</value>
        public virtual ICollection<ServiceArea> ServiceAreas { get; set; }

        /// <summary>
        /// Gets or sets collection of agent settings
        /// </summary>
        /// <value>The collection of agent settings</value>
        public virtual ICollection<AgentSetting> AgentSettings { get; set; }

        /// <summary>
        /// Gets or sets the budget
        /// </summary>
        /// <value>The budget</value>
        public decimal? Budget { get; set; }

        /// <summary>
        /// Gets or sets the date the budget was updated
        /// </summary>
        /// <value>The date the budget was updated</value>
        public DateTime? BudgetUpdateDate { get; set; }

        /// <summary>
        /// Gets or sets the asap balance
        /// </summary>
        /// <value>The asap balance</value>
        public virtual AsapBalance AsapBalance { get; set; }

        /// <summary>
        /// Gets or sets the budget type (fixed or monthly)
        /// </summary>
        /// <value>The budget type</value>
        public byte? BudgetType { get; set; }

        /// <summary>
        /// Gets or sets the current status
        /// </summary>
        /// <value>The status</value>
        public byte? ASAPStatus { get; set; }

        /// <summary>
        /// Gets or sets the day of the month when balances renew
        /// </summary>
        /// <value>The day of the month when balances renew</value>
        public byte? BalanceRenewDay { get; set; }

        /// <summary>
        /// Gets or sets the day of the month when balances renew
        /// </summary>
        /// <value>The day of the month when balances renew</value>
        public DateTime? ASAPActivationDate { get; set; }

        /// <summary>
        /// Gets or sets whether to optimize bids
        /// </summary>
        /// <value><c>true</c> if the system should optimize bids; otherwise, false</value>
        public bool OptimizeBids { get; set; }

        /// <summary>
        /// Gets or sets the API key.
        /// </summary>
        /// <value>The API key.</value>
        public Guid? ApiKey { get; set; }

        /// <summary>
        /// Gets or sets the association.
        /// </summary>
        /// <value>The association.</value>
        public virtual Association Association { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [association_ display].
        /// </summary>
        /// <value><c>true</c> if [association_ display]; otherwise, <c>false</c>.</value>
        public bool Association_Display { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [association email sent].
        /// </summary>
        /// <value><c>true</c> if [association email sent]; otherwise, <c>false</c>.</value>
        public bool AssociationEmailSent { get; set; }

        /// <summary>
        /// Gets or sets the association identifier.
        /// </summary>
        /// <value>The association identifier.</value>
        public int? AssociationId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [automatic add tabs].
        /// </summary>
        /// <value><c>true</c> if [automatic add tabs]; otherwise, <c>false</c>.</value>
        public bool AutoAddTabs { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [about you_ display].
        /// </summary>
        /// <value><c>true</c> if [about you_ display]; otherwise, <c>false</c>.</value>
        public bool Awards_Display { get; set; }

        /// <summary>
        /// Gets the brokerage.
        /// </summary>
        /// <value>The brokerage.</value>
        public Brokerage Brokerage
        {
            get
            {
                if (AgentBrokerages == null)
                    return new Brokerage();
                else
                    return AgentBrokerages.Brokerage;
            }
        }


        /// <summary>
        /// Gets or sets a value indicating whether [BrokerPhone_Display display].
        /// </summary>
        /// <value><c>true</c> if [BrokerPhone_Display]; otherwise, <c>false</c>.</value>
        public bool BrokerPhone_Display { get; set; }

        /// <summary>
        /// Gets the comments URL.
        /// </summary>
        /// <value>The comments URL.</value>
        public Uri CommentsUrl
        {
            get
            {
                string url = string.Format("{0}Redirect/ToAgentProfile/{1}?comments=true", Settings.BuyerURL, Id);
                return new Uri(url);
            }
        }

        /// <summary>
        /// Gets or sets the coverage areas ranks.
        /// </summary>
        /// <value>The coverage areas ranks.</value>
        public virtual ICollection<CoverageAreaRank> CoverageAreasRanks { get; set; }

        /// <summary>
        /// Gets or sets the cover photo.
        /// </summary>
        /// <value>The cover photo.</value>
        public virtual UserCoverPhoto CoverPhoto { get; set; }

        /// <summary>
        /// Gets or sets the cover photos.
        /// </summary>
        /// <value>The cover photos.</value>
        public ICollection<ICoverPhoto> CoverPhotos { get; set; }

        /// <summary>
        /// Gets or sets the credentials.
        /// </summary>
        /// <value>The credentials.</value>
        public virtual ICollection<Credential> Credentials { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [credentials_ display].
        /// </summary>
        /// <value><c>true</c> if [credentials_ display]; otherwise, <c>false</c>.</value>
        public bool Credentials_Display { get; set; }

        /// <summary>
        /// Gets or sets the Disclosures.
        /// </summary>
        /// <value>The Disclosures.</value>
        public string Disclosures { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Disclosures_Display].
        /// </summary>
        /// <value><c>true</c> if [Disclosures_display]; otherwise, <c>false</c>.</value>
        public bool Disclosures_Display { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [display phone].
        /// </summary>
        /// <value><c>true</c> if [display phone]; otherwise, <c>false</c>.</value>
        public bool DisplayPhone { get; set; }

        /// <summary>
        /// Gets or sets a value indiciating wether [display email].
        /// </summary>
        /// <value><c>true</c> if [display email; otherwise, <c>false</c>.</value>
        public bool DisplayEmail { get; set; }

        /// <summary>
        /// Gets or sets the document key.
        /// </summary>
        /// <value>The document key.</value>
        public string DocumentKey { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [email changed].
        /// </summary>
        /// <value><c>true</c> if [email changed]; otherwise, <c>false</c>.</value>
        public bool EmailChanged { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Education_ display].
        /// </summary>
        /// <value><c>true</c> if [Education_ display]; otherwise, <c>false</c>.</value>
        public bool Education_Display { get; set; }

        /// <summary>
        /// Gets or sets the expertise.
        /// </summary>
        /// <value>The expertise.</value>
        public virtual ICollection<FocalArea> Expertise { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Expertise_Display].
        /// </summary>
        /// <value><c>true</c> if [association_ display]; otherwise, <c>false</c>.</value>
        public bool Expertise_Display { get; set; }

        /// <summary>
        /// Gets the facebook page.
        /// </summary>
        /// <value>The facebook page.</value>
        public FacebookPage FacebookPage
        {
            get
            {
                if (FacebookPages == null)
                    return null;
                else
                    return FacebookPages.OrderByDescending(a => a.AgentDirectory)
                                        .ThenByDescending(a => a.AgentProfile)
                                        .FirstOrDefault();
            }
        }

        /// <summary>
        /// Gets or sets the facebook pages.
        /// </summary>
        /// <value>The facebook pages.</value>
        public virtual ICollection<FacebookPage> FacebookPages { get; set; }

        /// <summary>
        /// Gets or sets the headline.
        /// </summary>
        /// <value>The headline.</value>
        public string Headline { get; set; }

        /// <summary>
        /// Gets or sets the home search registered date time.
        /// </summary>
        /// <value>The home search registered date time.</value>
        public DateTime? HomeSearchRegisteredDateTime { get; set; }

        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>The identifier.</value>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the index disabled type identifier.
        /// </summary>
        /// <value>The index disabled type identifier.</value>
        public int? IdxDisabledTypeId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [index unsubscribed].
        /// </summary>
        /// <value><c>true</c> if [index unsubscribed]; otherwise, <c>false</c>.</value>
        public bool IDXUnsubscribed { get; set; }

        /// <summary>
        /// Gets or sets the installation source identifier.
        /// </summary>
        /// <value>The installation source identifier.</value>
        public byte InstallationSourceID { get; set; }

        /// <summary>
        /// Gets or sets the Interests.
        /// </summary>
        /// <value>The interests.</value>
        public string Interests { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Interests_Display].
        /// </summary>
        /// <value><c>true</c> if [Interests_display]; otherwise, <c>false</c>.</value>
        public bool Interests_Display { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance is broker home search subscription.
        /// </summary>
        /// <value><c>true</c> if this instance is broker home search subscription; otherwise, <c>false</c>.</value>
        public bool IsBrokerHomeSearchSubscription { get; set; }

        /// <summary>
        /// Gets a value indicating whether this instance is dream sweeps signup completed.
        /// </summary>
        /// <value><c>true</c> if this instance is dream sweeps signup completed; otherwise, <c>false</c>.</value>
        public bool IsDreamSweepsSignupCompleted
        {
            get { return Membership.Signups.Any(s => s.ProductId == ProductType.Sweepstakes && !s.Canceled.HasValue); }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is home search signup completed.
        /// </summary>
        /// <value><c>true</c> if this instance is home search signup completed; otherwise, <c>false</c>.</value>
        public bool IsHomeSearchSignupCompleted
        {
            get { return !(ListingsImportedDateTime == null); }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is low read score.
        /// </summary>
        /// <value><c>true</c> if this instance is low read score; otherwise, <c>false</c>.</value>
        public bool IsLowReadScore
        {
            get
            {
                return !(Score != null && Score > 6);
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether this instance is realtor icon displayed.
        /// </summary>
        /// <value><c>true</c> if this instance is realtor icon displayed; otherwise, <c>false</c>.</value>
        public bool DisplayRealtorIcon { get; set; }

        /// <summary>
        /// Gets a value indicating whether this instance is signup completed.
        /// </summary>
        /// <value><c>true</c> if this instance is signup completed; otherwise, <c>false</c>.</value>
        public bool IsSignupCompleted
        {
            get { return !(SignupCompleted == null); }
        }

        /// <summary>
        /// Gets or sets the expertise.
        /// </summary>
        /// <value>The expertise.</value>
        public virtual ICollection<Language> Languages { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Languages_Display].
        /// </summary>
        /// <value><c>true</c> if [association_ display]; otherwise, <c>false</c>.</value>
        public bool Languages_Display { get; set; }

        /// <summary>
        /// Gets or sets the last update date.
        /// </summary>
        /// <value>The last update date.</value>
        public DateTime? LastUpdateDate { get; set; }

        /// <summary>
        /// Gets or sets the listings.
        /// </summary>
        /// <value>The listings.</value>
        public virtual ICollection<AgentPropertyListing> Listings { get; set; }

        /// <summary>
        /// Gets or sets the profile license type (Broker, Associate Broker, Agent, etc)
        /// </summary>
        /// <value>The profile license type.</value>
        public string LicenseType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [LicenseType_Display].
        /// </summary>
        /// <value><c>true</c> if [LicenseType_Display]; otherwise, <c>false</c>.</value>
        public bool LicenseType_Display { get; set; }

        /// <summary>
        /// Gets or sets the listings imported date time.
        /// </summary>
        /// <value>The listings imported date time.</value>
        public DateTime? ListingsImportedDateTime { get; set; }

        /// <summary>
        /// Gets or sets the membership.
        /// </summary>
        /// <value>The membership.</value>
        public virtual Membership Membership { get; set; }

        /// <summary>
        /// Gets or sets the MLS agent identifier.
        /// </summary>
        /// <value>The MLS agent identifier.</value>
        public string MLSAgentId { get; set; }

        /// <summary>
        /// Gets or sets the MLS office identifier.
        /// </summary>
        /// <value>The MLS office identifier.</value>
        public string MLSOfficeId { get; set; }

        /// <summary>
        /// Gets or sets the MLS org identifier.
        /// </summary>
        /// <value>The MLS org identifier.</value>
        public string MLSOrgId { get; set; }

        /// <summary>
        /// Gets or sets the MLS provider.
        /// </summary>
        /// <value>The MLS provider.</value>
        public string MLSProvider { get; set; }

        /// <summary>
        /// Determines if the current MLS is active
        /// </summary>
        /// <value>Determines if the current MLS is active.</value>
        public bool? MlsActive { get; set; }

        /// <summary>
        /// Gets or sets the motto.
        /// </summary>
        /// <value>The motto.</value>
        public string Motto { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether Motto is displayed.
        /// </summary>
        /// <value><c>true</c> if [Motto_Display]; otherwise, <c>false</c>.</value>
        public bool Motto_Display { get; set; }

        /// <summary>
        /// Gets or sets the multiple listing service.
        /// </summary>
        /// <value>The multiple listing service.</value>
        public virtual MultipleListingService MultipleListingService { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [nplay email whitelisted].
        /// </summary>
        /// <value><c>true</c> if [nplay email whitelisted]; otherwise, <c>false</c>.</value>
        public bool NplayEmailWhitelisted { get; set; }

        /// <summary>
        /// Gets or sets the nrdsid.
        /// </summary>
        /// <value>The nrdsid.</value>
        public string NRDSID { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Organizations_Display].
        /// </summary>
        /// <value><c>true</c> if [Organizations_display]; otherwise, <c>false</c>.</value>
        public bool Organizations_Display { get; set; }

        /// <summary>
        /// Gets or sets the page post history.
        /// </summary>
        /// <value>The page post history.</value>
        public virtual ICollection<PagePostHistory> PagePostHistory { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [pending campaigns unsubscribe].
        /// </summary>
        /// <value><c>true</c> if [pending campaigns unsubscribe]; otherwise, <c>false</c>.</value>
        public bool PendingCampaignsUnsubscribe { get; set; }

        /// <summary>
        /// Gets or sets the phone.
        /// </summary>
        /// <value>The phone.</value>
        public string Phone { get; set; }

        [Obsolete("Use agent.GetProfileImage() instead.")]
        public virtual ProfileImageLink ProfileImage { get; set; }

        /// <summary>
        /// Gets or sets the profile image URL
        /// </summary>
        /// <value>The profile image URL.</value>
        public string ProfileImageUrl { get; set; }


        public string FullProfileImageUrl()
        {
            if (String.IsNullOrWhiteSpace(ProfileImageUrl) || ProfileImageUrl.Contains("//"))
                return ProfileImageUrl;
            else
                return Settings.CDN + "/" + Settings.AgentLogoContainerName + "/" + ProfileImageUrl;
        }

        /// <summary>
        /// Gets or sets the Position/Title.
        /// </summary>
        /// <value>The position/title.</value>
        public string PositionTitle { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Publications_Display].
        /// </summary>
        /// <value><c>true</c> if [Publications_display]; otherwise, <c>false</c>.</value>
        public bool Publications_Display { get; set; }

        /// <summary>
        /// Gets the profile URL.
        /// </summary>
        /// <value>The profile URL.</value>
        public Uri ProfileUrl
        {
            get
            {
                return GetProfileUrl(Id);
            }
        }

        /// <summary>
        /// Gets or sets the custom URL associated with this agent
        /// </summary>
        public string CustomURL { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [property report enabled].
        /// </summary>
        /// <value><c>true</c> if [property report enabled]; otherwise, <c>false</c>.</value>
        public bool PropertyReportEnabled { get; set; }

        /// <summary>
        /// Gets the ranked zips.
        /// </summary>
        /// <value>The ranked zips.</value>
        public IEnumerable<string> RankedZips
        {
            get
            {
                if (CoverageAreasRanks == null || !CoverageAreasRanks.Any())
                    return Zips;

                return CoverageAreasRanks.OrderBy(s => s.Rank).Select(s => s.ZipCode.Zip);
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether [read application request sent].
        /// </summary>
        /// <value><c>true</c> if [read application request sent]; otherwise, <c>false</c>.</value>
        public bool READAppRequestSent { get; set; }

        /// <summary>
        /// Gets or sets the read bonus expiration date.
        /// </summary>
        /// <value>The read bonus expiration date.</value>
        public DateTime? READBonusExpirationDate { get; set; }

        /// <summary>
        /// Gets or sets the read date3 steps completed.
        /// </summary>
        /// <value>The read date3 steps completed.</value>
        public DateTime? READDate3StepsCompleted { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [read optout agent referral email].
        /// </summary>
        /// <value><c>true</c> if [read optout agent referral email]; otherwise, <c>false</c>.</value>
        public bool READOptoutAgentReferralEmail { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [read optput referral email].
        /// </summary>
        /// <value><c>true</c> if [read optput referral email]; otherwise, <c>false</c>.</value>
        public bool READOptputReferralEmail { get; set; }

        /// <summary>
        /// Gets or sets the read referrer fb page identifier.
        /// </summary>
        /// <value>The read referrer fb page identifier.</value>
        public long READReferrerFbPageId { get; set; }

        /// <summary>
        /// Gets or sets the name of the read referrer page.
        /// </summary>
        /// <value>The name of the read referrer page.</value>
        public string READReferrerPageName { get; set; }

        /// <summary>
        /// Gets or sets the read release view date.
        /// </summary>
        /// <value>The read release view date.</value>
        public DateTime? READReleaseViewDate { get; set; }

        /// <summary>
        /// Gets or sets the broker's privacy policy url
        /// </summary>
        /// <value>The url of the privacy policy.</value>
        public string PrivacyPolicyUrl { get; set; }

        /// <summary>
        /// Gets or sets the re data vault verify.
        /// </summary>
        /// <value>The re data vault verify.</value>
        public virtual ReDataVaultVerify ReDataVaultVerify { get; set; }

        /// <summary>
        /// Gets or sets the referrals.
        /// </summary>
        /// <value>The referrals.</value>
        public virtual ICollection<Referral> Referrals { get; set; }

        public virtual ICollection<AgentAward> Awards { get; set; }
        public virtual ICollection<AgentPublication> Publications { get; set; }
        public virtual ICollection<AgentOrganization> Organizations { get; set; }
        public virtual ICollection<AgentEducation> Education { get; set; }

        /// <summary>
        /// Gets or sets the score.
        /// </summary>
        /// <value>The score.</value>
        public int? Score { get; set; }

        /// <summary>
        /// Gets or sets the services.
        /// </summary>
        /// <value>The services.</value>
        public string Services { get; set; }
        /// <summary>
        /// Gets or sets a value indicating whether [Services/Experience_ display].
        /// </summary>
        /// <value><c>true</c> if [Services_display]; otherwise, <c>false</c>.</value>
        public bool Services_Display { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [show admin profile banner].
        /// </summary>
        /// <value><c>true</c> if [show admin profile banner]; otherwise, <c>false</c>.</value>
        public bool ShowAdminProfileBanner { get; set; }

        /// <summary>
        /// Gets or sets the signature.
        /// </summary>
        /// <value>The signature.</value>
        public string Signature { get; set; }

        /// <summary>
        /// Gets or sets the signup completed.
        /// </summary>
        /// <value>The signup completed.</value>
        public DateTime? SignupCompleted { get; set; }
        /// <summary>
        /// Gets or sets the state license NBR.
        /// </summary>
        /// <value>The state license NBR.</value>
        public string StateLicenseNumber { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [state license NBR_ display].
        /// </summary>
        /// <value><c>true</c> if [state license NBR_ display]; otherwise, <c>false</c>.</value>
        public bool DisplayStateLicenseNumber { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [subscribe page engage email].
        /// </summary>
        /// <value><c>true</c> if [subscribe page engage email]; otherwise, <c>false</c>.</value>
        public bool SubscribePageEngageEmail { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [unsubscribe email].
        /// </summary>
        /// <value><c>true</c> if [unsubscribe email]; otherwise, <c>false</c>.</value>
        public bool UnsubscribeEmail { get; set; }

        /// <summary>
        /// Gets the unsubscribe URL.
        /// </summary>
        /// <value>The unsubscribe URL.</value>
        public Uri UnsubscribeUrl
        {
            get
            {
                string url = string.Format("{0}UnsubscribeEmail.aspx?npuid={1}", Settings.AgentDirectoryFBURL, Id);
                return new Uri(url);
            }
        }

        /// <summary>
        /// Gets or sets the Website.
        /// </summary>
        /// <value>The Website.</value>
        public string Website { get; set; }

        /// <summary>
        /// Gets or sets the Instagram URL.
        /// </summary>
        /// <value>The Instagram URL.</value>
        public string InstagramUrl { get; set; }

        /// <summary>
        /// Gets or sets the YouTube URL.
        /// </summary>
        /// <value>The YouTube URL.</value>
        public string YouTubeUrl { get; set; }

        /// <summary>
        /// Gets or sets the Google Business Page URL.
        /// </summary>
        /// <value>The Google Business Page URL.</value>
        public string GoogleUrl { get; set; }

        /// <summary>
        /// Gets or sets the Twitter URL.
        /// </summary>
        /// <value>The Twitter URL.</value>
        public string TwitterUrl { get; set; }

        /// <summary>
        /// Gets or sets the LinkedIn URL.
        /// </summary>
        /// <value>The LinkedIn URL.</value>
        public string LinkedInUrl { get; set; }

        /// <summary>
        /// Gets or sets the Pinterest URL.
        /// </summary>
        /// <value>The Pinterest URL.</value>
        public string PinterestUrl { get; set; }

        /// <summary>
        /// Gets or sets the Facebook URL.
        /// </summary>
        /// <value>The Facebook URL.</value>
        public string FacebookUrl { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [Website_Display].
        /// </summary>
        /// <value><c>true</c> if [Website_display]; otherwise, <c>false</c>.</value>
        public bool Website_Display { get; set; }

        /// <summary>
        /// Gets or sets the year the agent became licensed
        /// </summary>
        public int? YearLicensed { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [YearLicensed_Display].
        /// </summary>
        /// <value><c>true</c> if [YearLicensed_Display]; otherwise, <c>false</c>.</value>
        public bool YearLicensed_Display { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether agent has granted friends permission.
        /// </summary>
        /// <value><c>true</c> if agent has granted friends permission; otherwise, <c>false</c>.</value>
        public bool GrantedFriendsPermission { get; set; }

        /// <summary>
        /// Gets or sets the user schedules.
        /// </summary>
        /// <value>The user schedules.</value>
        public virtual ICollection<UserSchedule> UserSchedules { get; set; }

        /// <summary>
        /// Gets or sets if agent is search alliance member
        /// </summary>
        public bool IsSearchAllianceMember { get; set; }

        /// <summary>
        /// Gets or sets if agent has been invited to be a search alliance member
        /// </summary>
        public bool IsSearchAllianceInvited { get; set; }

        public string[] Zips
        {
            get
            {
                if (AgentZips == null)
                    return null;
                else
                    return AgentZips.Select(c => string.Format("{0,5}", c.Zip)).ToArray();
            }
        }

        /// <summary>
        /// Gets or Sets the SMS Phone Number for the Agent
        /// </summary>
        public string SMSPhone { get; set; }

        /// <summary>
        /// Gets or sets IsSMSPhoneValidated flag
        /// </summary>
        public bool? IsSMSPhoneValidated { get; set; }

        /// <summary>
        /// Gets or sets ValidationCode for SMSPhone
        /// </summary>
        public string SMSValidationCode { get; set; }

        public bool? EnableMyListingsSMS { get; set; }
        public bool? EnableMarketingSMS { get; set; }

        public bool HasEsignError { get; set; }
        public string EsignError { get; set; }
        public string EsignSigningUrl { get; set; }
        public string EsignDocDownloadUrl { get; set; }
        public string EsignWidgetKey { get; set; }
        public bool EsignCreationStarted { get; set; }
        public DateTime? AgentSignedDate { get; set; }
        public Guid? AgentGuid { get; set; }

        public string EsignDelegateEmail { get; set; }
        public bool SaLeadChargeFailed { get; set; }
        public DateTime? AccessTokenExpirationDate { get; set; }
        public DateTime? AppReauthorizeRequiredDate { get; set; }
        public virtual ICollection<AgentAffiliation> AgentAffiliations { get; set; }
        public string AdditionalTextForPosts { get; set; }
        public bool? InspectionDepot { get; set; }
        public List<string> ProductServices { get; set; }
        public virtual ICollection<AgentEmail> AgentEmails { get; set; }
        public string AgentProfilePostMessage { get; set; }
        public bool? HasReferredOtherAgents { get; set; }

        #endregion

        #region Methods

        public void AddZip(string zip)
        {
            try
            {
                var cleanzip = int.Parse(zip.Trim());

                if (!AgentZips.Any(z => z.Zip == cleanzip))
                    AgentZips.Add(new AgentZip
                    {
                        Agent_Id = Id,
                        Zip = cleanzip
                    });

            }
            catch (Exception)
            {
                // Ignore errors (invalid zip codes)
            }

        }

        public string GetProfileImage()
        {
            if (!String.IsNullOrEmpty(ProfileImageUrl))
            {

                if (ProfileImageUrl.Contains("//"))
                {
                    // If the agent uploaded an image with a fully-qualified URL, return it
                    return ProfileImageUrl;
                }
                else
                {
                    // If the agent uploaded an image without a fully-qualified URL, prepend it with the CDN base URL and Container Name
                    return FullProfileImageUrl();
                }
            }
            else
            {
                // If the agent hasn't uploaded a profile image, use their Facebook profile
                return FacebookProfileImageUrl(int.Parse(Settings.AgentProfileImageWidth), int.Parse(Settings.AgentProfileImageHeight)).AbsoluteUri;
            }
        }

        public string GetBrokerageLogoUrl()
        {
            return AgentBrokerages == null || AgentBrokerages.LogoUri == null || AgentBrokerages.LogoUri.AbsoluteUri == null ?
                    string.Empty : AgentBrokerages.LogoUri.AbsoluteUri;
        }

        /// <summary>
        /// Gets the profile URL.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>Uri.</returns>
        public static Uri GetProfileUrl(int id)
        {
            string url = string.Format("{0}Redirect/ToAgentProfile/{1}/", Settings.BuyerURL, id);
            return new Uri(url);
        }

        /// <summary>
        /// Gets the profile URL.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="adId">The ad identifier.</param>
        /// <param name="facebookPageId">The facebook page identifier.</param>
        /// <param name="promoAd">if set to <c>true</c> [promo ad].</param>
        /// <returns>Uri.</returns>
        public static Uri GetProfileUrl(int id, long? adId, long? facebookPageId, bool promoAd = false)
        {
            string url = string.Format("{0}Redirect/ToAgentProfile/{1}?", Settings.BuyerURL, id);

            var routeValues = new Dictionary<string, object>();

            if (adId.HasValue)
                routeValues.Add("adId", adId.Value);

            if (facebookPageId.HasValue)
                routeValues.Add("pageId", facebookPageId.Value);

            routeValues.Add("promoteAds", promoAd);

            var pairs = routeValues.Select(s => string.Format("{0}{1}{2}", s.Key, "=", s.Value.ToString())).ToArray();
            var queryString = string.Join("&", pairs);

            url = string.Format("{0}{1}", url, queryString);

            return new Uri(url);
        }

        /// <summary>
        /// Gets agent's registered services.
        /// </summary>
        /// <returns>List of RegisteredService</returns>
        public List<Service> GetRegisteredServices()
        {
            List<Service> services = new List<Service>();

            if (Membership.FundingSources != null)
            {
                List<FundingSource> activeFundingSources = Membership.FundingSources
                    .Where(f => f.Active && (!f.SubscriptionEndDate.HasValue || f.SubscriptionEndDate.Value > DateTime.Now.AddDays(1)))
                    .ToList();

                foreach (var f in activeFundingSources)
                {
                    switch (f.ProductType)
                    {
                        case ProductType.IDX:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.IDX,
                                ServiceStatus = GetIdxServiceStatus()
                            });
                            if(f.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.IDXPlusIncluded)
                            {
                                services.Add(new Service
                                {
                                    ServiceType = ServiceType.IDXPlus,
                                    ServiceStatus = GetServiceStatus(EventType.AgentSetupIdxPlus)
                                });
                            }
                            break;
                        case ProductType.PageEngage:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.PageEngage,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupPageEngageService)
                            });
                            break;
                        case ProductType.Sweepstakes:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.DreamSweeps,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupDreamSweepsService)
                            });
                            break;
                        case ProductType.Bundle:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.PageManagement,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupTKSService)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.IDX,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupIDXService)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.PageEngage,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupPageEngageService)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.DreamSweeps,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupDreamSweepsService)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.HomeValue,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupHomeValueService)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.FeaturedListings,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupFeaturedListingsService)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.DirectoryPro,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupPageCreateService)
                            });

                            if ((f.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded) ||
                                (f.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded) ||
                                (f.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded))
                            {
                                services.Add(new Service
                                {
                                    ServiceType = ServiceType.InstagramPageEngage,
                                    ServiceStatus = GetServiceStatus(EventType.AgentSetupInstagramPageEngageService)
                                });
                            }
                            break;
                        case ProductType.DirectoryPro:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.DirectoryPro,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupPageCreateService)
                            });
                            break;
                        case ProductType.Ads:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.Ads,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupAdsService)
                            });
                            break;
                        case ProductType.HomeValue:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.HomeValue,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupHomeValueService)
                            });
                            break;
                        case ProductType.FeaturedListings:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.FeaturedListings,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupFeaturedListingsService)
                            });
                            break;
                        case ProductType.ListingLeadAds:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.ListingLeadAds,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupListingLeadAdsService)
                            });
                            break;
                        case ProductType.HomeValueAds:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.HomeValueAds,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupHomeValueAdsService)
                            });
                            break;
                        case ProductType.InstagramPageEngage:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.InstagramPageEngage,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupInstagramPageEngageService)
                            });
                            break;
                        case ProductType.PEInstagramBundle:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.PEInstagramBundle,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupPEInstagramBundle)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.PageEngage,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupPageEngageService)
                            });
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.InstagramPageEngage,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupInstagramPageEngageService)
                            });
                            break;
                        case ProductType.LeadMagnet:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.LeadMagnet,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupLeadMagnet)
                            });
                            break;
                        case ProductType.IDXPlus:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.IDXPlus,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupIdxPlus)
                            });
                            break;
                        case ProductType.Afordal:
                            services.Add(new Service
                            {
                                ServiceType = ServiceType.Afordal,
                                ServiceStatus = GetServiceStatus(EventType.AgentSetupAfordal)
                            });
                            break;
                    }
                }
            }

            return services;
        }

        /// <summary>
        /// Returns registered service abbreviations. This method should be used when Membership.FundingSources and Agent.FacebookPages will be loaded. (Normal situations)
        /// </summary>
        public List<string> GetProductServiceAbbreviations(bool activatedProductsOnly)
        {
            var services = new List<string>();

            if (Membership?.FundingSources != null)
            {
                var productIds = Membership.FundingSources
                    .Where(f => f.Active && (!f.SubscriptionEndDate.HasValue || f.SubscriptionEndDate.Value > DateTime.Now.AddDays(1)))
                    .Select(f => f.ProductTypeId)
                    .ToList();
                bool isInstagramIncludedTKSPlan = false;
                bool isAfordalIncludedPlan = false;
                if (productIds.Contains((int)ProductType.Bundle))
                {
                    var fundingSource = Membership.FundingSources.Where(f => f.ProductTypeId == (int)ProductType.Bundle && f.Active == true).FirstOrDefault();
                    if(fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded || fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded
                        || fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded)
                    {
                        isInstagramIncludedTKSPlan = true;
                    }
                    if (fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalIncluded || fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded)
                    {
                        isAfordalIncludedPlan = true;
                    }
                }
                bool isIDXPlusIncludedPlan = false;

                if (productIds.Contains((int)ProductType.IDX))
                {
                    var fundingSource = Membership.FundingSources.Where(f => f.ProductTypeId == (int)ProductType.IDX && f.Active == true).FirstOrDefault();
                    if (fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.IDXPlusIncluded)
                    {
                        isIDXPlusIncludedPlan = true;
                    }
                    if (fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalIncluded)
                    {
                        isAfordalIncludedPlan = true;
                    }
                }

                var hasPageDsDate = FacebookPages?.Any(p => p.SweepstakesDate != null) ?? false;
                var homeValueOnALP = AgentSettings?.FirstOrDefault(s => s.Key == AgentSettingKeys.HomeValueOnALP);
                var showHomeValueOnALP = homeValueOnALP == null || homeValueOnALP.Value == "true";
                services = GetProductServiceAbbreviations(productIds, HomeSearchRegisteredDateTime, hasPageDsDate, activatedProductsOnly, showHomeValueOnALP, isInstagramIncludedTKSPlan, isIDXPlusIncludedPlan, isAfordalIncludedPlan);
            }

            return services;
        }

        /// <summary>
        /// Returns registered service abbreviations. This method should be used when Membership.FundingSources / Agent.FacebookPages / AgentSettings will not be loaded, for example, in Agent Indexer.
        /// </summary>
        public List<string> GetProductServiceAbbreviations(List<byte> productIds, DateTime? homeSearchRegisteredDateTime, bool hasPageDsDate, bool activatedProductsOnly, bool showHomeValueOnALP, bool isInstagramIncludedTKSPlan, bool hasIDXPlusIncluded, bool isAfordalIncludedPlan)
        {
            var services = new List<string>();
            for (int i = 0; i < productIds.Count; i++)
            {
                var productId = productIds[i];
                switch (productId)
                {
                    case (byte)ProductType.IDX:
                        if (!activatedProductsOnly || homeSearchRegisteredDateTime != null)
                        {
                            services.Add(ServiceType.IDX.GetAbbreviation());
                            if (hasIDXPlusIncluded)
                                services.Add(ServiceType.IDXPlus.GetAbbreviation());
                            if (isAfordalIncludedPlan)
                                services.Add(ServiceType.Afordal.GetAbbreviation());
                        }
                        break;
                    case (byte)ProductType.PageEngage:
                        services.Add(ServiceType.PageEngage.GetAbbreviation());
                        break;
                    case (byte)ProductType.Sweepstakes:
                        services.Add(ServiceType.DreamSweeps.GetAbbreviation());
                        break;
                    case (byte)ProductType.Bundle:
                        services.Add(ServiceType.PageManagement.GetAbbreviation());
                        services.Add(ServiceType.PageEngage.GetAbbreviation());
                        services.Add(ServiceType.FeaturedListings.GetAbbreviation());
                        services.Add(ServiceType.DirectoryPro.GetAbbreviation());
                        if (isInstagramIncludedTKSPlan)
                        {
                            services.Add(ServiceType.InstagramPageEngage.GetAbbreviation());
                        }

                        if (!activatedProductsOnly || homeSearchRegisteredDateTime != null)
                        {
                            services.Add(ServiceType.IDX.GetAbbreviation());

                            if (isAfordalIncludedPlan)
                                services.Add(ServiceType.Afordal.GetAbbreviation());
                        }

                        if (!activatedProductsOnly || hasPageDsDate)
                            services.Add(ServiceType.DreamSweeps.GetAbbreviation());

                        if (!activatedProductsOnly || showHomeValueOnALP)
                            services.Add(ServiceType.HomeValue.GetAbbreviation());

                        break;
                    case (byte)ProductType.DirectoryPro:
                        services.Add(ServiceType.DirectoryPro.GetAbbreviation());
                        break;
                    case (byte)ProductType.Ads:
                        services.Add(ServiceType.Ads.GetAbbreviation());
                        break;
                    case (byte)ProductType.HomeValue:
                        services.Add(ServiceType.HomeValue.GetAbbreviation());
                        break;
                    case (byte)ProductType.FeaturedListings:
                        services.Add(ServiceType.FeaturedListings.GetAbbreviation());
                        break;
                    case (byte)ProductType.ListingLeadAds:
                        services.Add(ServiceType.ListingLeadAds.GetAbbreviation());
                        break;
                    case (byte)ProductType.HomeValueAds:
                        services.Add(ServiceType.HomeValueAds.GetAbbreviation());
                        break;
                    case (byte)ProductType.InstagramPageEngage:
                        services.Add(ServiceType.InstagramPageEngage.GetAbbreviation());
                        break;
                    case (byte)ProductType.PEInstagramBundle:
                        services.Add(ServiceType.InstagramPageEngage.GetAbbreviation());
                        services.Add(ServiceType.PageEngage.GetAbbreviation());
                        break;
                    case (byte)ProductType.LeadMagnet:
                        services.Add(ServiceType.LeadMagnet.GetAbbreviation());
                        break;
                    case (byte)ProductType.IDXPlus:
                        services.Add(ServiceType.IDXPlus.GetAbbreviation());
                        break;
                    // Afordal product type is now used as a misc. product, not for afordal.
                    // Afordal is determined by product plan type id 10 and 11 with product type IDX & TKS
                    // case (byte)ProductType.Afordal:
                    //     services.Add(ServiceType.Afordal.GetAbbreviation());
                    //     break;
                }
            }
            services = services.Distinct().ToList();
            return services;
        }

        /// <summary>
        /// Accounts the specialist first active or default.
        /// </summary>
        /// <returns>AccountSpecialist.</returns>
        public AccountSpecialist AccountSpecialistFirstActiveOrDefault()
        {
            var retVal = AccountSpecialists.FirstOrDefault(s => s.Enabled);
            if (retVal == null)
            {
                retVal = new AccountSpecialist
                {
                    Created = DateTime.Now,
                    Enabled = true,
                    PhoneNumber = Regex.Replace(Settings.CustomerSupportPhone, "[^.0-9]", ""),
                    Membership = new Membership
                    {
                        FirstName = "Customer Support",
                        Email = Settings.OrdersEmail
                    }
                };
            }
            return retVal;
        }

        public bool CompletedBasicProfile()
        {
            return Membership != null &&
                !string.IsNullOrWhiteSpace(Membership.FirstName) &&
                !string.IsNullOrWhiteSpace(Membership.LastName) &&
                !string.IsNullOrWhiteSpace(Membership.Email) &&
                !string.IsNullOrWhiteSpace(Phone) &&
                !string.IsNullOrWhiteSpace(Headline);
        }

        public bool CompletedLicenseInfo()
        {
            return !string.IsNullOrWhiteSpace(StateLicenseNumber) &&
                !string.IsNullOrWhiteSpace(LicenseType) &&
                YearLicensed != null;
        }

        public bool CompletedShortForm()
        {

            return (Membership != null) &&
                   !string.IsNullOrEmpty(Membership.FirstName) &&
                   !string.IsNullOrEmpty(Membership.LastName) &&
                   !string.IsNullOrEmpty(Membership.Email) &&
                   (AgentZips != null) && (AgentZips.Count > 0);
        }


        /// <summary>
        /// Computes the score.
        /// </summary>
        /// <returns>System.Int32.</returns>
        public int ComputeScore()
        {
            int score = 0;

            if (CompletedBasicProfile())
                score++;
            if(CompletedLicenseInfo())
                score++;
            if (!string.IsNullOrWhiteSpace(About) && About.Length >= MINSCORELENGTH)
                score++;
            if (Expertise?.Count > 0)
                score++;
            if (Credentials?.Count > 0)
                score++;
            if (!string.IsNullOrWhiteSpace(AgentVideoURL))
                score++;
            if (!string.IsNullOrWhiteSpace(FacebookId))
                score++;
            if (AgentZips?.Count > 0)
                score++;
            if (!string.IsNullOrWhiteSpace(ProfileImageUrl) && ProfileImageUrl != Settings.AgentDefaultProfileImageUrl)
                score++;
            if (!string.IsNullOrWhiteSpace(MLSOrgId) && !string.IsNullOrWhiteSpace(MLSAgentId))
                score++;
            if (HasReferredOtherAgents == true)
                score += 2;

            return score;
        }

        #endregion

        #region Helpers

        private ServiceStatus GetServiceStatus(EventType setupEvent)
        {
            ServiceStatus status = ServiceStatus.Funded;

            var accountEvent = AccountEvents.Where(a => a.Enabled &&
                a.EventId == (int)setupEvent).FirstOrDefault();
            if (accountEvent != null)
            {
                status = ServiceStatus.SetupComplete;
            }

            return status;
        }

        private ServiceStatus GetIdxServiceStatus()
        {
            ServiceStatus status = ServiceStatus.Funded;
            var mlsEvent = AccountEvents.Where(a => a.Enabled && (a.EventId == (int)EventType.AgentSetupMlsForIDXService || a.EventId == (int)EventType.AgentSetupIDXService))
                .OrderByDescending(a => a.Created).FirstOrDefault();
            if (mlsEvent != null)
            {
                if (mlsEvent.EventId == (int)EventType.AgentSetupMlsForIDXService)
                    status = ServiceStatus.MlsSetupComplete;
                else
                    status = ServiceStatus.SetupComplete;
            }
            return status;
        }

        #region Static methods

        public static bool CustomURLExists(IRepository<Agent> AgentRepository, string CustomURL)
        {
            bool URLInUse = false;

            var agentCount = AgentRepository.GetAll().Where(a => a.CustomURL == CustomURL.ToLower()).Count();

            if (agentCount > 0)
                URLInUse = true;

            return URLInUse;
        }



        public static string GetUniqueCustomURL(IRepository<Agent> AgentRepository, string FirstName, string LastName)
        {
            string value = FirstName.Trim() + LastName.Trim();
            value = RemoveWhitespace(value);

            Regex rgx = new Regex("[^a-zA-Z0-9 -]");
            value = rgx.Replace(value, "");


            bool valueFound = true;
            int counter = 0;
            string tempValue = String.Empty;

            do
            {
                if (counter == 0)
                    tempValue = value;
                else
                    tempValue = value + counter.ToString();
                valueFound = CustomURLExists(AgentRepository, tempValue);
                counter++;
            }
            while (valueFound == true);

            return tempValue.ToLower();
        }

        public static string RemoveWhitespace(string input)
        {
            return new string(input.ToCharArray()
                .Where(c => !Char.IsWhiteSpace(c))
                .ToArray());
        }

        #endregion

        #endregion
    }
}