﻿#region N-Play Copyright Banner
/*******************************************************************\
 * Copyright © N-Play RE LLC. 2018.  All rights reserved.           *
 *                                                                  *
 * This code is property of N-Play and cannot be used without       *
 * explicit permission given by an official N-Play representative.  *
 * For details contact: <EMAIL>                               *
 \******************************************************************/
#endregion

using Newtonsoft.Json;

namespace NPlay.Common.Models.Facebook
{
    public class Leadgen
    {
        [JsonProperty("leadgen_id")]
        public long LeadgenId { get; set; }

        [JsonProperty("page_id")]
        public long PageId { get; set; }

        [JsonProperty("form_id")]
        public long FormId { get; set; }

        [JsonProperty("adgroup_id")]
        public long? AdGroupId { get; set; }

        [JsonProperty("ad_id")]
        public long? AdId { get; set; }

        [JsonProperty("created_time")]
        public string CreatedTime { get; set; }

        /// <summary>
        /// The SHA256 hash of the email associated with the lead. This could be null.
        /// </summary>
        [JsonProperty("email_hash")]
        public string EmailHash { get; set; }
    }
}
