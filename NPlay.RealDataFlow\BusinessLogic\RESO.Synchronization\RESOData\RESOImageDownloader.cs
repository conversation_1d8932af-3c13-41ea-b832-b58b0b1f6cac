﻿using MLS.Synchronization.Abstract;
using MLS.Synchronization.Images;
using MLS.Synchronization.Models;
using MLS.Synchronization.Models.Exceptions;
using NPlay.Common.Abstract;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Contextual;
using NPlay.Common.Messaging.Exceptions;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Models.Canonical;
using RESO.Connector.Abstract;
using RETS.Models.RAWStorage;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace RESO.Synchronization
{
    public class RESOImageDownloader : ImageDownloaderBase, IRESOImageDownloader
    {
        IMlsGridMediaConnector MlsGridMediaConnector;
        IBridgeMediaConnector BridgeMediaConnector;
        IRapattoniMediaConnector RapattoniMediaConnector;
        ITrestleMediaConnector TrestleMediaConnector;
        IUtahRealEstateMediaConnector UtahRealEstateMediaConnector;
        IRMLSMediaConnector RMLSMediaConnector;
        ISparkMediaConnector SparkMediaConnector;
        IParagonMediaConnector ParagonMediaConnector;
        IPerchwellMediaConnector PerchwellMediaConnector;
        IOpenMLSMediaConnector OpenMLSMediaConnector;

        private List<DataProviders> SupportedDataProviders = new List<DataProviders>()
        {
            DataProviders.Bridge,
            DataProviders.MlsGrid,
            DataProviders.Paragon,
            DataProviders.Rapattoni,
            DataProviders.Spark,
            DataProviders.Trestle,
            DataProviders.UtahRealEstate,
            DataProviders.RMLS,
            DataProviders.Perchwell,
            DataProviders.OpenMLS,
        };

        public RESOImageDownloader(IContextualEventBroker broker,
                                   ILogger logger,
                                   IProcessingContext processingContext,
                                   IMLSRepository mlsRepo,
                                   ISearchRepository searchRepo,
                                   IListingStatusProvider listingStatusProvider,
                                   IMlsSynchronizationContextProvider mlsSyncContextProvider,
                                   IListingImageLazyLoadManager listingImageLazyLoadManager,
                                   ICanonImageTranslator canonImageTranslator,
                                   IMlsGridMediaConnector gridMediaConnector,
                                   IBridgeMediaConnector bridgeMediaConnector,
                                   IRapattoniMediaConnector rapattoniMediaConnector,
                                   ITrestleMediaConnector trestleMediaConnector,
                                   IUtahRealEstateMediaConnector utahRealEstateMediaConnector,
                                   IRMLSMediaConnector rmlsMediaConnector,
                                   ISparkMediaConnector sparkMediaConnector,
                                   IParagonMediaConnector paragonMediaConnector,
                                   IPerchwellMediaConnector perchwellMediaConnector,
                                   IOpenMLSMediaConnector openmlsMediaConnector,
                                   IImageUploader imageUploader) : base(broker, mlsRepo, logger, processingContext, listingStatusProvider, canonImageTranslator, searchRepo, imageUploader, mlsSyncContextProvider, listingImageLazyLoadManager)
        {
            MlsGridMediaConnector = gridMediaConnector;
            BridgeMediaConnector = bridgeMediaConnector;
            RapattoniMediaConnector = rapattoniMediaConnector;
            TrestleMediaConnector = trestleMediaConnector;
            UtahRealEstateMediaConnector = utahRealEstateMediaConnector;
            RMLSMediaConnector = rmlsMediaConnector;
            SparkMediaConnector = sparkMediaConnector;
            ParagonMediaConnector = paragonMediaConnector;
            PerchwellMediaConnector = perchwellMediaConnector;
            OpenMLSMediaConnector = openmlsMediaConnector;
        }

        private IMediaConnector GetMediaConnector(MlsSynchronizationContext mlsContext)
        {
            switch (mlsContext.DataProvider)
            {
                case DataProviders.Trestle:
                    return TrestleMediaConnector;
                case DataProviders.Bridge:
                    return BridgeMediaConnector;
                case DataProviders.MlsGrid:
                    return MlsGridMediaConnector;
                case DataProviders.Rapattoni:
                    return RapattoniMediaConnector;
                case DataProviders.UtahRealEstate:
                    return UtahRealEstateMediaConnector;
                case DataProviders.RMLS:
                    return RMLSMediaConnector;
                case DataProviders.Spark:
                    return SparkMediaConnector;
                case DataProviders.Paragon:
                    return ParagonMediaConnector;
                case DataProviders.Perchwell:
                    return PerchwellMediaConnector;
                case DataProviders.OpenMLS:
                    return OpenMLSMediaConnector;
                default:
                    return null;
            }
        }

        protected override Func<Listing, MultipleListingService, RAWRowFlags, ImagesPayloadBase, Task<List<MlsImage>>> DownloadImagesAsync
        {
            get;
            set;
        }

        public async Task ProcessAsync<T>(T payload)
        {
            if (!(payload is RESOImagesPayload))
            {
                throw new BadPayloadException("Cannot process a payload that is not of type RESOImagesPayload");
            }

            RESOImagesPayload resoImagesPayload = payload as RESOImagesPayload;

            MlsSynchronizationContext mlsContext = MlsSynchronizationContextProvider.Get(resoImagesPayload.MlsId);
            if (mlsContext == null)
            {
                Logger.Error($"Unable to download images for this listing.  Missing MlsSynchronizationContext", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, resoImagesPayload.MlsId), new LoggingPair(LoggingKey.PropertyListingId, resoImagesPayload.PropertyListingId) }, this, "Process");
                throw new InvalidMlsContextException($"No context is defined for RESO Synchronization for this MLS: {resoImagesPayload.MlsId}");
            }
            else if (!SupportedDataProviders.Contains(mlsContext.DataProvider))
            {
                Logger.Error($"Unable to download images for this listing.  DataProvider not supported - {mlsContext.DataProvider.ToString()}.", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, resoImagesPayload.MlsId), new LoggingPair(LoggingKey.PropertyListingId, resoImagesPayload.PropertyListingId) }, this, "Process");
                throw new InvalidMlsContextException($"No context is defined for RESO Synchronization for this MLS: {resoImagesPayload.MlsId}");
            }

            DownloadImagesAsync = async (l, mls, f, p) =>
            {
                return await DoDownload(mls, resoImagesPayload, mlsContext);
            };

            await base.ProcessAsync(resoImagesPayload);
        }

        private async Task<List<MlsImage>> DoDownload(MultipleListingService mls, RESOImagesPayload payload, MlsSynchronizationContext mlsContext)
        {
            string query = GetMediaQuery(payload, mlsContext);
            string path = Path.GetDirectoryName(AppDomain.CurrentDomain.BaseDirectory) + "\\Photos\\" + mls.Id + "\\";
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);

            IMediaConnector mediaConnector = GetMediaConnector(mlsContext);

            return await mediaConnector.GetImagesAsync(query, mls.Id, path);
        }

        private string GetMediaQuery(RESOImagesPayload payload, MlsSynchronizationContext mlsContext)
        {
            switch(mlsContext.DataProvider)
            {
                case DataProviders.Trestle:
                    return $"Media/?$filter=ResourceRecordKey eq '{payload.ListingKey}'&$select=MediaURL,ResourceRecordKey,Order,MediaKey,MediaType,LongDescription&$top=200";
                case DataProviders.OpenMLS:
                    return $"Media?$filter=ResourceRecordID eq '{payload.ListingKey}'&$select=MediaURL,ListingId,Order,MediaKey,MediaType,LongDescription&$top=200";
                case DataProviders.MlsGrid:
                case DataProviders.Rapattoni:
                case DataProviders.Bridge:
                case DataProviders.Spark:
                case DataProviders.Paragon:
                case DataProviders.RMLS:
                case DataProviders.UtahRealEstate:
                case DataProviders.Perchwell:
                    return payload.InternalListingId;
                default:
                    throw new InvalidMlsContextException($"Data Provider type {mlsContext.DataProvider.ToString()} is not supported in this context.");
            }
        }
    }
}
