using System;
using System.Collections.Generic;
using System.Fabric;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.ServiceFabric.Services.Communication.Runtime;
using Microsoft.ServiceFabric.Services.Runtime;
using Ninject;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Abstract;
using System.Reflection;
using NPlay.Common.Models;
using NPlay;
using NPlay.Common.Messaging.Events;
using NPlay.BusinessLogic.Leads;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.Common.ServiceAgents.NPlayApi;
using NPlay.Common.Models.Services;
using NPlay.Common;
using AutoMapper;
using NPlay.Common.Models.Leads;
using NPlay.Common.Models.Emails.Engine;
using System.Text;
using NPlay.Common.Models.Services.PushNotification;
using NPlay.Common.Services.Abstract;
using NPlay.Common.Models.ExtendedServices.Abstract;

namespace LeadActivityProcessor
{
    /// <summary>
    /// An instance of this class is created for each service instance by the Service Fabric runtime.
    /// </summary>
    internal sealed class LeadActivityProcessor : StatelessService
    {
        static string ConsumerKey = "LeadActivityProcessor";
        static ushort PrefetchCount = 10;
        static IKernel Kernel;
        static IEventBroker EventBroker;
        static ILogger Logger;
        static ISearchRepository SearchRepository;
        IRepository<Agent> AgentRepository;
        INotificationsClient NotificationsClient;
        IAgentSMSService AgentSMSService;
        IAgentClient AgentClient;
        IMapper _mapper;
        IAfordalHelper AfordalHelper;
        IAfordalClient AfordalClient;

        public LeadActivityProcessor(StatelessServiceContext context)
            : base(context)
        {
            Kernel = new StandardKernel();
            Kernel.Load(Assembly.GetExecutingAssembly());

            Logger = Kernel.Get<ILogger>();
            Logger.Info("Starting the LeadActivityProcessor Service", null, this, "OnStart");

            var settingRepo = Kernel.Get<IRepository<Setting>>();
            Settings.Overrides = settingRepo.GetAll().ToDictionary(s => s.Key, s => s.Value);
            Logger.Info("Loaded settings");

            SearchRepository = Kernel.Get<ISearchRepository>();
            Logger.Info("Loaded Search Repository", null, this, "OnStart");

            EventBroker = Kernel.Get<IEventBroker>();
            AgentRepository = Kernel.Get<IRepository<Agent>>();
            AgentSMSService = Kernel.Get<IAgentSMSService>();
            AgentClient = Kernel.Get<IAgentClient>();
            NotificationsClient = Kernel.Get<INotificationsClient>();
            AfordalHelper = Kernel.Get<IAfordalHelper>();
            AfordalClient = Kernel.Get<IAfordalClient>();

            _mapper = Kernel.Get<IMapper>();
        }

        /// <summary>
        /// Optional override to create listeners (e.g., TCP, HTTP) for this service replica to handle client or user requests.
        /// </summary>
        /// <returns>A collection of listeners.</returns>
        protected override IEnumerable<ServiceInstanceListener> CreateServiceInstanceListeners()
        {
            return new ServiceInstanceListener[0];
        }

        /// <summary>
        /// This is the main entry point for your service instance.
        /// </summary>
        /// <param name="cancellationToken">Canceled when Service Fabric needs to shut down this service instance.</param>
        protected override async Task RunAsync(CancellationToken cancellationToken)
        {
            const string methodName = "RunAsync";
            try
            {
                #region Lead Activity events
                EventBroker.Consume<SearchAllianceLeadActivityEvent, SearchAllianceLeadActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<SweepstakeApplicationEvent, SweepstakeApplication>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<AgentPinEvent, AgentPin>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<IDXPinEvent, IDXPin>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<PromoPinEvent, PromoPin>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<PropertyPinEvent, PropertyPin>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<MessageAgentActivityEvent, MessageAgentActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<RequestDetailMarketAnalysisActivityEvent, RequestDetailMarketAnalysisActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<ScheduleShowingActivityEvent, ScheduleShowingActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<SharePropertyActivityEvent, SharePropertyActivity>(
                (e) =>
                {
                    Task worker = new Task(() =>
                    {
                        try
                        {
                            var logger = Kernel.Get<ILogger>();
                            logger.TrackingGuid = e.TrackingId;

                            ProcessLeadActivityMessage(e.Payload, logger);
                            e.Acknowledge();
                        }
                        catch (Exception ex)
                        {
                            e.Reject();
                            if (e.Payload != null)
                            {
                                Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                            }
                            else
                            {
                                Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                            }
                        }
                    });
                    worker.Start();
                }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<LeadAdsLeadActivityEvent, LeadAdsLeadActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<SelectAgentActivityEvent, SelectAgentActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<AskMyHomeValueEvent, AskMyHomeValueActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<LeadMagnetLeadActivityEvent, LeadMagnetLeadActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<LendingTreeLeadActivityEvent, LendingTreeLeadActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<RequestHomeInspectionQuoteEvent, RequestHomeInspectionQuoteActivity>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessLeadActivityMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process lead activity message of this type.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityType, e.Payload.LeadActivityType) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                #endregion

                EventBroker.Consume<ListingSearchEvent, ListingSearchHistoryBasicModel>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessListingSearchHistoryMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process listing search message.", new LoggingPair[] { new LoggingPair(LoggingKey.ListingSearchHistoryId, e.Payload.Id) }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process listing search message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);

                EventBroker.Consume<ListingTagsChangedEvent, LeadLookup>(
                    (e) =>
                    {
                        Task worker = new Task(() =>
                        {
                            try
                            {
                                var logger = Kernel.Get<ILogger>();
                                logger.TrackingGuid = e.TrackingId;

                                ProcessListingTagsChangedMessage(e.Payload, logger);
                                e.Acknowledge();
                            }
                            catch (Exception ex)
                            {
                                e.Reject();
                                if (e.Payload != null)
                                {
                                    Logger.Error("Could not process listing tags changed message.",
                                        new LoggingPair[] {
                                            new LoggingPair(LoggingKey.AgentId, e.Payload.AgentId),
                                            new LoggingPair(LoggingKey.BuyerId, e.Payload.BuyerId),
                                            new LoggingPair(LoggingKey.CookieId, e.Payload.CookieId)
                                        }, ex, this, methodName);
                                }
                                else
                                {
                                    Logger.Error("Could not process lead activity message. Unknown exception.", null, ex, this, methodName);
                                }
                            }
                        });
                        worker.Start();
                    }, ConsumerKey, PrefetchCount);
            }
            catch (Exception ex)
            {
                if (Logger != null)
                    Logger.Error("Error starting", ex);

                throw ex;
            }

            while (true)
            {
                cancellationToken.ThrowIfCancellationRequested();

                await Task.Delay(TimeSpan.FromSeconds(1), cancellationToken);
            }
        }

        private void ProcessListingTagsChangedMessage(LeadLookup leadLookup, ILogger logger)
        {
            // Get the lead:
            if (leadLookup.AgentId == null)
                throw new ArgumentException("AgentId cannot be null");

            string id = Lead.GetLeadId(leadLookup.AgentId.Value, leadLookup.BuyerId, leadLookup.CookieId);

            var versionedLead = SearchRepository.GetByIdWithVersion<Lead>(SearchIndex.Leads, id);
            if (versionedLead != null && versionedLead.Document != null)
            {
                ILeadProvider leadProvider = Kernel.Get<ILeadProvider>();
                leadProvider.PopulateListingTagData(versionedLead.Document);
                versionedLead.Document.DateModified = DateTime.UtcNow;
                SearchRepository.SaveWithVersionTruncatingDateTimeMilliseconds(versionedLead, SearchIndex.Leads);
            }
        }

        private void ProcessListingSearchHistoryMessage(ListingSearchHistoryBasicModel listingSearchHistory, ILogger logger)
        {
            const string methodName = "ProcessListingSearchHistoryMessage";
            // Get the lead:
            if (listingSearchHistory.AgentId == 0)
                throw new ArgumentException("AgentId cannot be 0");

            logger.Info("Getting lead id from ListingSearchHistoryBasicModel.", new LoggingPair[] { new LoggingPair(LoggingKey.ListingSearchHistoryId, listingSearchHistory.Id) }, this, methodName);
            string id = Lead.GetLeadId(listingSearchHistory.AgentId, listingSearchHistory.BuyerId, listingSearchHistory.CookieId);

            logger.Info("Getting lead from ListingSearchHistoryBasicModel.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);
            var versionedLead = SearchRepository.GetByIdWithVersion<Lead>(SearchIndex.Leads, id);
            if (versionedLead != null && versionedLead.Document != null)
            {
                logger.Info("Updating lead search history data.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);
                ILeadProvider leadProvider = Kernel.Get<ILeadProvider>();
                leadProvider.PopulateListingSearchData(versionedLead.Document);
                versionedLead.Document.DateModified = DateTime.UtcNow;

                logger.Info("Attempting to save lead.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);
                try
                {
                    SearchRepository.SaveWithVersionTruncatingDateTimeMilliseconds(versionedLead, SearchIndex.Leads);
                }
                catch (Exception ex)
                {
                    logger.Error("Failed to update lead search history data.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadId, id) }, ex, this, methodName);
                    throw ex;
                }

            }
        }

        private void ProcessLeadActivityMessage(LeadActivity leadActivity, ILogger logger)
        {
            const string methodName = "ProcessLeadActivityMessage";
            // Attempt to get the BuyerId if it is not present:
            bool? leadIsAgent = null;
            if (leadActivity.CookieId.HasValue)
            {
                logger.Info("Looking up cookie record to check for a buyer id.", new LoggingPair[] { new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId), new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                var cookieRepo = Kernel.Get<IRepository<Cookie>>();
                var cookieRecord = cookieRepo.GetById(leadActivity.CookieId.Value);
                if (cookieRecord != null)
                {
                    if (cookieRecord.BuyerID.HasValue)
                    {
                        logger.Info("Found buyer id from existing cookie record.", new LoggingPair[] { new LoggingPair(LoggingKey.BuyerId, cookieRecord.BuyerID), new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId), new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                        leadActivity.BuyerId = cookieRecord.BuyerID;
                    }

                    leadIsAgent = cookieRecord.AgentID.HasValue;
                }
            }

            Type t = leadActivity.GetType();
            if (t == typeof(SearchAllianceLeadActivity))
            {
                logger.Info("Saving SearchAllianceLeadActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as SearchAllianceLeadActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(SweepstakeApplication))
            {
                logger.Info("Saving SweepstakeApplication to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as SweepstakeApplication, SearchIndex.LeadActivity);
            }
            else if (t == typeof(AgentPin))
            {
                logger.Info("Saving AgentPin to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as AgentPin, SearchIndex.LeadActivity);
            }
            else if (t == typeof(IDXPin))
            {
                logger.Info("Saving IDXPin to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as IDXPin, SearchIndex.LeadActivity);
            }
            else if (t == typeof(PromoPin))
            {
                logger.Info("Saving PromoPin to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as PromoPin, SearchIndex.LeadActivity);
            }
            else if (t == typeof(PropertyPin))
            {
                logger.Info("Saving PropertyPin to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as PropertyPin, SearchIndex.LeadActivity);
            }
            else if (t == typeof(MessageAgentActivity))
            {
                logger.Info("Saving MessageAgentActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as MessageAgentActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(RequestDetailMarketAnalysisActivity))
            {
                logger.Info("Saving RequestDetailMarketAnalysisActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as RequestDetailMarketAnalysisActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(ScheduleShowingActivity))
            {
                logger.Info("Saving ScheduleShowingActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as ScheduleShowingActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(SharePropertyActivity))
            {
                Logger.Info("Saving SharePropertyActivity to elastic.", new LoggingPair[] {
                                            new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                                            new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId),
                                        new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as SharePropertyActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(LeadAdsLeadActivity))
            {
                logger.Info("Saving LeadAdsLeadActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as LeadAdsLeadActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(SelectAgentActivity))
            {
                logger.Info("Saving SelectAgentActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as SelectAgentActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(AskMyHomeValueActivity))
            {
                logger.Info("Saving AskMyHomeValueActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as AskMyHomeValueActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(LeadMagnetLeadActivity))
            {
                logger.Info("Saving LeadMagnetLeadActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as LeadMagnetLeadActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(LendingTreeLeadActivity))
            {
                logger.Info("Saving LendingTreeLeadActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as LendingTreeLeadActivity, SearchIndex.LeadActivity);
            }
            else if (t == typeof(RequestHomeInspectionQuoteActivity))
            {
                logger.Info("Saving RequestHomeInspectionQuoteActivity to elastic.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                SearchRepository.Save(leadActivity as RequestHomeInspectionQuoteActivity, SearchIndex.LeadActivity);
            }

            // wait for 1.5 second prior to creating or updating the lead.  This is to ensure that the document is ready for search in elastic
            Lead lead = null;
            var createOrUpdateTask = Task.Run(async delegate
            {
                await Task.Delay(TimeSpan.FromSeconds(1.5));
                lead = CreateOrUpdateLead(leadActivity, logger, leadIsAgent);
            });
            createOrUpdateTask.Wait();

            if (t == typeof(SearchAllianceLeadActivity))
            {
                var saLeadActivity = leadActivity as SearchAllianceLeadActivity;
                if (saLeadActivity != null)
                {
                    if (lead == null)
                    {
                        Logger.Warning("Unable to publish LeadReadyEvent.  Lead is null.  This is likely due to the lead being an agent, but could be caused by another issue.",
                            new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, saLeadActivity.AgentId),
                                            new LoggingPair(LoggingKey.BuyerId, saLeadActivity.BuyerId),
                                            new LoggingPair(LoggingKey.CookieId, saLeadActivity.CookieId),
                                            new LoggingPair(LoggingKey.LeadActivityId, saLeadActivity.Id),
                                            new LoggingPair(LoggingKey.LeadTrafficId, saLeadActivity.LeadTrafficId)}, this, methodName);
                        return;
                    }

                    EventBroker.Publish<SearchAllianceLeadReadyEvent, LeadQueuePayload>(new SearchAllianceLeadReadyEvent()
                    {
                        Payload = new LeadQueuePayload()
                        {
                            AgentId = saLeadActivity.AgentId,
                            BuyerId = saLeadActivity.BuyerId,
                            LeadTrafficId = saLeadActivity.LeadTrafficId,
                            TransactionId = saLeadActivity.TransactionId,
                            LeadId = lead.Id
                        },
                        TrackingId = Logger.TrackingGuid.Value
                    }, 5000);
                }
            }
            else if (t == typeof(LeadAdsLeadActivity))
            {
                var leadAdsLeadActivity = leadActivity as LeadAdsLeadActivity;
                if (leadAdsLeadActivity != null)
                {
                    var email = new AutoEmail();
                    email.UserId = leadAdsLeadActivity.AgentId;
                    email.Template = Emails.LeadAdsFormFilled.ToString();
                    email.UseAgentContactTime = false;
                    email.Parameters = leadAdsLeadActivity.Id;
                    var autoEmailEvent = new TransactionalAutoEmailEvent { Payload = email, TrackingId = logger.TrackingGuid.Value };
                    EventBroker.Publish<TransactionalAutoEmailEvent, AutoEmail>(autoEmailEvent);

                    //Use properties of LeadActivity to log. If you use Lead's property, it could throw error when Lead is not created (mainly because of buyer's being an agent).
                    Logger.Info("LeadAdsFormFilled email is queued.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, leadAdsLeadActivity.AgentId), new LoggingPair(LoggingKey.BuyerId, leadAdsLeadActivity.BuyerId),
                       new LoggingPair(LoggingKey.LeadActivityId, leadAdsLeadActivity.Id)}, this, methodName);
                }
            }
        }

        private Lead CreateOrUpdateLead(LeadActivity leadActivity, ILogger logger, bool? leadIsAgent = null)
        {
            const string methodName = "CreateOrUpdateLead";
            var messageToAgent = string.Empty;
            // Filter agent-buyers if possible
            if (leadIsAgent.HasValue && leadIsAgent.Value == true)
            {
                logger.Warning("Cannot create a lead for this buyer.  Buyer was identified as an agent from an association with the cookie.", new LoggingPair[] { new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId), new LoggingPair(LoggingKey.AgentId, leadActivity.BuyerId), new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId) }, this, methodName);
                return null;
            }

            if (Settings.FilterChargingForAgents && leadActivity.BuyerId.HasValue && leadActivity.BuyerId > 0)
            {
                var agentClient = Kernel.Get<IAgentClient>();
                try
                {
                    logger.Info("Checking if this buyer is an agent.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, leadActivity.BuyerId), new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId) }, this, methodName);
                    var buyerAsAgent = agentClient.GetAgent(leadActivity.BuyerId.Value);
                    if (buyerAsAgent != null)
                    {
                        logger.Warning("Cannot create a lead for this buyer.  Buyer is an agent.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, leadActivity.BuyerId), new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId) }, this, methodName);
                        return null;
                    }
                }
                catch (NPlayApiException apiEx)
                {
                    if (apiEx.StatusCode != System.Net.HttpStatusCode.NotFound)
                    {
                        logger.Error("Unexpected error.", null, apiEx, this, methodName);
                        throw apiEx;
                    }
                }
            }

            // A lead must be constructed with either a buyerId or a cookieId.  Abort if neither are present.
            if (!leadActivity.CookieId.HasValue && !leadActivity.BuyerId.HasValue)
            {
                logger.Error("Cannot create a lead record for lead activity.  Either a cookieId or buyerId is required.",
                    new LoggingPair[] {
                        new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                        new LoggingPair(LoggingKey.LeadActivityId, leadActivity.Id) }, this, methodName);
                return null;
            }

            // Query to see if there is already a lead associated with this activity:
            string id = Lead.GetLeadId(leadActivity.AgentId, leadActivity.BuyerId, leadActivity.CookieId);

            var loggerParam = new Ninject.Parameters.ConstructorArgument("logger", logger);
            ILeadProvider leadProvider = Kernel.Get<ILeadProvider>(loggerParam);

            var emailLogRepository = Kernel.Get<IRepository<EmailLog>>();
            emailLogRepository.SetCommandTimeout(300);

            Lead result = null;
            VersionedObject<Lead> versionedLead = SearchRepository.GetByIdWithVersion<Lead>(SearchIndex.Leads, id);
            if (versionedLead == null || versionedLead.Document == null)
            {
                logger.Info("Attempting to create new lead.",
                    new LoggingPair[] {
                        new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                        new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId),
                        new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId),
                        new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);

                DateTime now = DateTime.UtcNow;
                string listingId = null;
                result = new Lead()
                {
                    Id = id,
                    AgentId = leadActivity.AgentId,
                    BuyerId = leadActivity.BuyerId,
                    CookieId = leadActivity.CookieId,
                    Phone = leadActivity.Phone,
                    DateCreated = now,
                    DateModified = now,
                };

                if (leadActivity is SearchAllianceLeadActivity)
                {
                    var saLeadActivity = leadActivity as SearchAllianceLeadActivity;
                    result.SearchAllianceSource = getSearchAllianceSourceString(saLeadActivity.Source);
                    result.SearchAllianceChargeAmount = saLeadActivity.ChargeAmount;
                    result.PaymentProcessorChargeId = saLeadActivity.PaymentProcessorChargeId;
                }
                else if (leadActivity is LeadAdsLeadActivity)
                {
                    var leadAdsLeadActivity = leadActivity as LeadAdsLeadActivity;
                    listingId = leadAdsLeadActivity.RetailerItemId;
                    result.Notes = new List<LeadNote>
                    {
                        new LeadNote {
                            DateCreated = leadAdsLeadActivity.TimeStamp,
                            Note = GetLeadAdsLeadActivityNote(leadAdsLeadActivity)
                        }
                    };
                }
                else if (leadActivity is IDXPin)
                {
                    listingId = ((IDXPin)leadActivity).ListingId;
                    if (!string.IsNullOrWhiteSpace(leadActivity.Partner))
                    {
                        result.Partners = new List<string>() { leadActivity.Partner };
                    }
                    if (!string.IsNullOrEmpty(leadActivity.Platform))
                    {
                        result.Platforms = new List<string>() { leadActivity.Platform };
                    }
                }
                else if (leadActivity is AskMyHomeValueActivity)
                {
                    var askMyHomeValueActivity = leadActivity as AskMyHomeValueActivity;
                    result.Notes = new List<LeadNote>
                    {
                        new LeadNote
                        {
                            DateCreated = askMyHomeValueActivity.TimeStamp,
                            Note = GetAskMyHomeValueActivityNote(askMyHomeValueActivity)
                        }
                    };

                    if (string.IsNullOrWhiteSpace(result.Phone))
                        result.Phone = askMyHomeValueActivity.PhoneNumber;
                }
                else if (leadActivity is LeadMagnetLeadActivity)
                {
                    var leadMagnetLeadActivity = leadActivity as LeadMagnetLeadActivity;
                    result.Notes = new List<LeadNote>
                    {
                        new LeadNote
                        {
                            DateCreated = leadMagnetLeadActivity.TimeStamp,
                            Note = GetLeadMagnetLeadActivityNote(leadMagnetLeadActivity)
                        }
                    };
                }
                else if (leadActivity is MessageAgentActivity)
                {
                    messageToAgent = ((MessageAgentActivity)leadActivity).Message;
                }

                logger.Info("Populating lead aggregate data.",
                    new LoggingPair[] {
                        new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                        new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId),
                        new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId),
                        new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);

                leadProvider.Populate(result, leadActivity);

                logger.Info("Saving lead.",
                    new LoggingPair[] {
                        new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                        new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId),
                        new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId),
                        new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);
                SearchRepository.SaveTruncatingDateTimeMilliseconds(result, SearchIndex.Leads);

                logger.Info("Successfully saved lead.",
                    new LoggingPair[] {
                        new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                        new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId),
                        new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId),
                        new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);

                queuePushNotification(leadActivity, result, logger);

                EventBroker.Publish<LeadCreatedEvent, LeadPayload>(new LeadCreatedEvent()
                {
                    Payload = new LeadPayload()
                    {
                        AgentId = leadActivity.AgentId,
                        BuyerId = leadActivity.BuyerId,
                        LeadId = result.Id,
                        ListingId = listingId,
                    },
                    TrackingId = logger.TrackingGuid.Value
                }, 5000);

                // Submit lead to Afordal if agent has Afordal subscription
                SubmitLeadToAfordal(result, leadActivity, logger);

                if (result.BuyerId != null && result.BuyerId != 0)
                {
                    Logger.Info("Calling send SMS since buyer id is available");
                    SendSMS(leadActivity, result, messageToAgent);

                    if (leadActivity is IDXPin)
                    {
                        var sent = CheckIfIdxPinLeadEmailHasBeenSent(emailLogRepository, leadActivity.AgentId, leadActivity.BuyerId.Value);
                        if (!sent)
                        {
                            SendIdxPinLeadEmail(leadActivity, LeadEmailType.Generated);
                            Logger.Info($"Trying to send SMS to agent {leadActivity.AgentId}");
                        }
                    }
                }
            }
            else
            {
                logger.Info("Attempting to update lead.",
                    new LoggingPair[] {
                        new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                        new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId),
                        new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId),
                        new LoggingPair(LoggingKey.LeadId, id) }, this, methodName);

                versionedLead.Document.AgentId = leadActivity.AgentId;
                var convertedFromAnonymousToLoggedIn = (versionedLead.Document.BuyerId == null || versionedLead.Document.BuyerId == 0) && (leadActivity.BuyerId != null && leadActivity.BuyerId != 0);
                versionedLead.Document.BuyerId = leadActivity.BuyerId;
                versionedLead.Document.CookieId = leadActivity.CookieId;
                versionedLead.Document.Phone = String.IsNullOrWhiteSpace(leadActivity.Phone) ? versionedLead.Document.Phone : leadActivity.Phone;

                leadProvider.Populate(versionedLead.Document, leadActivity);
                versionedLead.Document.DateModified = DateTime.UtcNow;

                if (leadActivity is SearchAllianceLeadActivity)
                {
                    var saLeadActivity = leadActivity as SearchAllianceLeadActivity;
                    versionedLead.Document.SearchAllianceSource = getSearchAllianceSourceString(saLeadActivity.Source);
                    versionedLead.Document.SearchAllianceChargeAmount = saLeadActivity.ChargeAmount;
                    versionedLead.Document.PaymentProcessorChargeId = saLeadActivity.PaymentProcessorChargeId;
                }
                else if (leadActivity is LeadAdsLeadActivity)
                {
                    var leadAdsLeadActivity = leadActivity as LeadAdsLeadActivity;
                    var notes = versionedLead.Document.Notes.ToList();
                    var newNote = GetLeadAdsLeadActivityNote(leadAdsLeadActivity);
                    var existingNote = notes.FirstOrDefault(f => f.Note == newNote);
                    if (existingNote == null)
                    {
                        var note = new LeadNote
                        {
                            DateCreated = leadAdsLeadActivity.TimeStamp,
                            Note = leadAdsLeadActivity.Note
                        };
                        notes.Add(note);
                        versionedLead.Document.Notes = notes;
                    }
                }
                else if (leadActivity is AskMyHomeValueActivity)
                {
                    var askMyHomeValueActivity = leadActivity as AskMyHomeValueActivity;
                    var notes = versionedLead.Document.Notes.ToList();
                    var newNote = GetAskMyHomeValueActivityNote(askMyHomeValueActivity);
                    var existingNote = notes.FirstOrDefault(f => f.Note == newNote);
                    if (existingNote == null)
                    {
                        var note = new LeadNote
                        {
                            DateCreated = askMyHomeValueActivity.TimeStamp,
                            Note = newNote
                        };
                        notes.Add(note);
                        versionedLead.Document.Notes = notes;
                    }
                }

                versionedLead.Document.LastActivity = leadActivity.TimeStamp;

                try
                {
                    SearchRepository.SaveWithVersionTruncatingDateTimeMilliseconds(versionedLead, SearchIndex.Leads);
                    logger.Info("Successfully updated lead.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadId, versionedLead.Document.Id) }, this, methodName);
                }
                catch (Exception ex)
                {
                    logger.Error("Failed to update lead data.", new LoggingPair[] { new LoggingPair(LoggingKey.LeadId, id) }, ex, this, methodName);
                    throw ex;
                }

                result = versionedLead.Document;

                EventBroker.Publish<LeadUpdatedEvent, LeadPayload>(new LeadUpdatedEvent()
                {
                    Payload = new LeadPayload()
                    {
                        AgentId = leadActivity.AgentId,
                        BuyerId = leadActivity.BuyerId,
                        LeadId = result.Id
                    },
                    TrackingId = logger.TrackingGuid.Value
                }, 5000);

                // Submit lead to Afordal if converted from anonymous to logged in
                if (convertedFromAnonymousToLoggedIn)
                {
                    SubmitLeadToAfordal(result, leadActivity, logger);
                }

                if (convertedFromAnonymousToLoggedIn)
                {
                    Logger.Info("Calling send SMS since buyer id is available");
                    SendSMS(leadActivity, result, messageToAgent);

                    if (leadActivity is IDXPin)
                    {
                        var sent = CheckIfIdxPinLeadEmailHasBeenSent(emailLogRepository, leadActivity.AgentId, leadActivity.BuyerId.Value);
                        if (!sent)
                        {
                            SendIdxPinLeadEmail(leadActivity, LeadEmailType.Converted);
                        }
                    }
                }
            }

            return result;
        }

        private string getSearchAllianceSourceString(short saSource)
        {
            try
            {
                TransactionSources ts = (TransactionSources)saSource;
                return ts.ToString("G");
            }
            catch
            {
                return "None";
            }
        }

        private void queuePushNotification(LeadActivity leadActivity, Lead lead, ILogger logger)
        {
            var request = GetPushNotificationRequest(leadActivity, lead, logger);
            if (request != null)
            {
                var pushNotificationEvent = new SendPushNotificationEvent { Payload = request };
                if (logger.TrackingGuid.Value != Guid.Empty)
                    pushNotificationEvent.TrackingId = logger.TrackingGuid.Value;
                EventBroker.Publish<SendPushNotificationEvent, PushNotificationRequest>(pushNotificationEvent);

                logger.Info("SendPushNotificationEvent was fired successfully.",
                    new LoggingPair[] { new LoggingPair(LoggingKey.LeadId, lead.Id),
                    new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                    new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId),
                    new LoggingPair(LoggingKey.CookieId, leadActivity.CookieId)
                    }, this, "queuePushNotification");
            }
        }

        private bool SendSMS(LeadActivity leadActivity, Lead lead, string messageToAgent)
        {
           // AgentModel agent = null;
            var buyerRepository = Kernel.Get<IRepository<Buyer>>();
            var buyer = buyerRepository.GetById(lead.BuyerId);
            var buyerName = string.IsNullOrWhiteSpace(lead.FirstName) ? buyer?.Membership?.Name() : $"{lead.FirstName} {lead.LastName}";
            var buyerEmail = string.IsNullOrWhiteSpace(lead.Email) ? buyer?.Membership?.Email : lead.Email;
            var buyerPhone = string.IsNullOrWhiteSpace(lead.Phone) ? buyer?.Phone : lead.Phone;
            AgentModel agent = AgentClient.GetAgent(leadActivity.AgentId);
            var methodName = "LeadActivityProcessor.SendSMS";
            Logger.Info($"Trying to send SMS phone {agent?.SMSPhone} for agent {agent?.Id}");
            try
            {
                if (agent.EnableSMS == true)
                {
                    var content = $"New lead from Home ASAP ";
                    var hasContent = false;
                    if (lead.Sources.Count() > 0)
                    {
                        content += $"\r\nSource: {lead.Sources.First().Type}";
                    }
                    if (!string.IsNullOrWhiteSpace(buyerName))
                    {
                        content += $"\r\nName: {buyerName}";
                        hasContent = true;
                    }
                    if (!string.IsNullOrWhiteSpace(buyerEmail))
                    {
                        content += $"\r\nEmail: {buyerEmail}";
                        hasContent = true;
                    }
                    if (!string.IsNullOrWhiteSpace(buyerPhone))
                    {
                        content += $"\r\nEmail: {buyerPhone}";
                        hasContent = true;
                    }
                    if (!string.IsNullOrWhiteSpace(messageToAgent))
                    {
                        content += $"\r\n{messageToAgent}";
                    }

                    Logger.Info($"The value of hasContent is {hasContent}");

                    if (hasContent == true)
                    {
                        string hashCode = $"NewLead{agent.Id}{lead.BuyerId}".GetHashCode().ToString();
                        if (NotificationsClient.ShouldNotify(agent.Id.ToString(), hashCode))
                        {
                            try
                            {
                                bool completed = NotificationsClient.TrySetNotificationComplete(agent.Id.ToString(), hashCode);

                                if (completed)
                                {
                                    var sms = AgentSMSService.SendText(content, agent?.SMSPhone);
                                    Logger.Info($"SMS sent to phone {agent.SMSPhone} for agent {agent.Id} since hasContent is {hasContent}");
                                }
                            }
                            catch(Exception ex)
                            {
                                Logger.Warning($"Unable to send the SMS for this lead at this time",
                                new LoggingPair[]
                                {
                                    new LoggingPair(LoggingKey.AgentId, agent.Id),
                                    new LoggingPair(LoggingKey.BuyerId, lead.BuyerId),
                                    new LoggingPair(LoggingKey.LeadId, lead.Id)
                                },
                                this,
                                "SendSMS");
                            }
                        }
                        else
                        {
                            Logger.Info("Skipping SMS notification because it has already been sent for this lead",
                                new LoggingPair[]
                                {
                                    new LoggingPair(LoggingKey.AgentId, agent.Id),
                                    new LoggingPair(LoggingKey.BuyerId, lead.BuyerId),
                                    new LoggingPair(LoggingKey.LeadId, lead.Id)
                                },
                                this,
                                "SendSMS");
                        }
                    }
                    else
                    {
                        Logger.Info($"Message is empty. Did not send SMS to phone {agent.SMSPhone} for agent {agent.Id}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Could not send sms to phone {agent?.SMSPhone} for agent {agent.Id}", null, ex, this, methodName);
                return false;
            }

            return true;
        }

        private PushNotificationRequest GetPushNotificationRequest(LeadActivity leadActivity, Lead lead, ILogger logger)
        {
            Type t = leadActivity.GetType();
            var contents = GetPushNotificationContents(lead, t);

            var buyerRepository = Kernel.Get<IRepository<Buyer>>();
            var buyer = buyerRepository.GetById(lead.BuyerId);

            var request = new PushNotificationRequest
            {
                MembershipId = lead.AgentId,
                IOSBadgeType = PushNotificationIOSBadgeType.Increase,
                IOSBadgeCount = 1,
                Type = PushNotificationType.NewLead,
                Headings = GetPushNotificationHeading(lead),
                Contents = contents,
                IOSSound = GetIosSound(contents),
                Data = _mapper.Map<Lead, LeadModel>(lead),
                BuyerName =  string.IsNullOrWhiteSpace(lead.FirstName) ? buyer?.Membership?.Name() : $"{lead.FirstName} {lead.LastName}",
                BuyerEmail = string.IsNullOrWhiteSpace(lead.Email) ? buyer?.Membership?.Email : lead.Email,
                BuyerPhone = string.IsNullOrWhiteSpace(lead.Phone) ? buyer?.Phone : lead.Phone,
                Sources =  lead.GetSourcesToDisplay()

            };
            return request;
        }

        private string GetPushNotificationContents(Lead lead, Type t)
        {
            string contents = null;
            if (lead.BuyerId != null && lead.BuyerId != 0)
            {
                var buyerRepository = Kernel.Get<IRepository<Buyer>>();
                var buyer = buyerRepository.GetById(lead.BuyerId);
                if (buyer != null && buyer.Membership != null)
                {
                    var buyerName = buyer.Membership.Name() ?? "someone";
                    if (t == typeof(SearchAllianceLeadActivity))
                        contents = string.Format("{0} is searching for homes in your area.", buyerName);
                    else if (t == typeof(SweepstakeApplication))
                        contents = string.Format("{0} has entered your Dream Sweeps Contest.", buyerName);
                }
            }
            return contents;
        }

        private string GetPushNotificationHeading(Lead lead)
        {
            return lead.BuyerId != null && lead.BuyerId != 0 ? "New Lead" : null;
        }

        private string GetIosSound(string contents)
        {
            string iosSound = null;
            if (!string.IsNullOrWhiteSpace(contents))
                iosSound = "doorbell.caf";
            return iosSound;
        }

        private string GetAskMyHomeValueActivityNote(AskMyHomeValueActivity activity)
        {
            var sb = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(activity.PropertyAddress))
                sb.AppendLine(string.Format("Property address: {0}", activity.PropertyAddress));
            if (!string.IsNullOrWhiteSpace(activity.PhoneNumber))
                sb.AppendLine(string.Format("Phone number: {0}", activity.PhoneNumber));
            if (!string.IsNullOrWhiteSpace(activity.WhenToSell))
                sb.AppendLine(string.Format("When do you want to sell?: {0}", activity.WhenToSell));
            return sb.ToString();
        }

        private string GetLeadMagnetLeadActivityNote(LeadMagnetLeadActivity activity)
        {
            var sb = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(activity.Phone))
                sb.AppendLine(string.Format("Phone number: {0}", activity.Phone));
            if (!string.IsNullOrWhiteSpace(activity.SourceUrl))
                sb.AppendLine(string.Format("Source website: {0}", activity.SourceUrl));
            return sb.ToString();
        }

        private string GetLeadAdsLeadActivityNote(LeadAdsLeadActivity activity)
        {
            var sb = new StringBuilder();
            sb.Append(activity.Note);//note already has line break when lead activity is created

            if (!string.IsNullOrWhiteSpace(activity.FormName))
                sb.AppendLine(string.Format("form name: {0}", activity.FormName));
            if (!string.IsNullOrWhiteSpace(activity.Platform))
            {
                var meaningfulPlatform = activity.MeaningfulPlatform();
                sb.AppendLine(string.Format("platform: {0}", meaningfulPlatform));
            }
            if (!string.IsNullOrWhiteSpace(activity.RetailerItemId))
                sb.AppendLine(string.Format("MLS #: {0}", activity.RetailerItemId));
            if (activity.Address != null)
                sb.AppendLine($"Address: {activity.GetAddress()}");

            return sb.ToString();
        }

        private void SendIdxPinLeadEmail(LeadActivity leadActivity, LeadEmailType emailType)
        {
            const string methodName = "SendIdxPinLeadEmail";
            var email = new AutoEmail
            {
                UserId = leadActivity.AgentId,
                Template = Emails.IdxPinLeadEmail.ToString(),
                UseAgentContactTime = false
            };

            email.SetParameters(new
            {
                leadActivityId = leadActivity.Id,
                leadEmailTypeId = (int)emailType
            });

            var autoEmailEvent = new TransactionalAutoEmailEvent { Payload = email };
            EventBroker.Publish<TransactionalAutoEmailEvent, AutoEmail>(autoEmailEvent);
            Logger.Info("IdxPinLeademail has been queued.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId), new LoggingPair(LoggingKey.BuyerId, leadActivity.BuyerId) }, this, methodName);
        }

        private bool CheckIfIdxPinLeadEmailHasBeenSent(IRepository<EmailLog> emailLogRepository, int agentId, int buyerId)
        {
            const string methodName = "CheckIfIdxPinLeadEmailHasBeenSent";
            var template = Emails.IdxPinLeadEmail.ToString();

            var sent = true;
            try
            {
                sent = emailLogRepository.GetAll().Any(l => l.BuyerId == buyerId && l.MembershipId == agentId && l.Template == template);
            }
            catch (Exception e)
            {
                if (e.InnerException != null)
                    Logger.Error($"Failed to retrieve EmailLog. Inner Message: {e.InnerException.Message}, Inner StackTrace: {e.InnerException.StackTrace}, StackTrace: {e.StackTrace}",
                        new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agentId), new LoggingPair(LoggingKey.BuyerId, buyerId) }, e, this, methodName);
                else
                    Logger.Error($"Failed to retrieve EmailLog. Message: {e.Message}, StackTrace: {e.StackTrace}", new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agentId), new LoggingPair(LoggingKey.BuyerId, buyerId) }, e, this, methodName);
            }
            return sent;
        }

        private void SubmitLeadToAfordal(Lead lead, LeadActivity leadActivity, ILogger logger)
        {
            const string methodName = "SubmitLeadToAfordal";

            try
            {
                // Check if agent has Afordal subscription
                if (!AfordalHelper.CheckForAfordalSubscription(leadActivity.AgentId))
                {
                    logger.Info("Agent does not have Afordal subscription, skipping Afordal lead submission.",
                        new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId) }, this, methodName);
                    return;
                }

                // Get buyer information to fill in missing lead data (similar to SMS method)
                var buyerRepository = Kernel.Get<IRepository<Buyer>>();
                var buyer = buyerRepository.GetById(lead.BuyerId);

                // Use lead data first, fall back to buyer data if lead data is missing
                var firstName = !string.IsNullOrWhiteSpace(lead.FirstName) ? lead.FirstName : buyer?.Membership?.FirstName;
                var lastName = !string.IsNullOrWhiteSpace(lead.LastName) ? lead.LastName : buyer?.Membership?.LastName;
                var email = !string.IsNullOrWhiteSpace(lead.Email) ? lead.Email : buyer?.Membership?.Email;
                var phone = !string.IsNullOrWhiteSpace(lead.Phone) ? lead.Phone : buyer?.Phone;
                var agent = AgentRepository.GetById(leadActivity.AgentId);
                var autoAccessToken = Convert.ToString(agent?.Membership?.AutoAccessToken);

                // Validate required fields after attempting to get data from buyer
                if (string.IsNullOrWhiteSpace(firstName) || string.IsNullOrWhiteSpace(lastName) || string.IsNullOrWhiteSpace(email))
                {
                    logger.Warning("Lead missing required fields for Afordal submission (FirstName, LastName, or Email) even after checking buyer data.",
                        new LoggingPair[] {
                            new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                            new LoggingPair(LoggingKey.LeadId, lead.Id),
                            new LoggingPair(LoggingKey.BuyerId, lead.BuyerId)
                        }, this, methodName);
                    return;
                }

                // Parse address from SearchLocation if available
                string propStreetAddressComplete = null;
                string propCity = null;
                string propState = null;
                string propZip = null;

                if (!string.IsNullOrWhiteSpace(lead.SearchLocation))
                {
                    ParseSearchLocationForAddress(lead.SearchLocation, out propStreetAddressComplete, out propCity, out propState, out propZip);
                }

                // Submit lead to Afordal
                bool success = AfordalClient.SubmitLead(
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    autoAccessToken: autoAccessToken,
                    phone: phone,
                    propStreetAddressComplete: propStreetAddressComplete,
                    propCity: propCity,
                    propState: propState,
                    propZip: propZip,
                    homeASAP_agentID: leadActivity.AgentId
                );

                if (success)
                {
                    logger.Info("Successfully submitted lead to Afordal.",
                        new LoggingPair[] {
                            new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                            new LoggingPair(LoggingKey.LeadId, lead.Id)
                        }, this, methodName);
                }
                else
                {
                    logger.Warning("Failed to submit lead to Afordal.",
                        new LoggingPair[] {
                            new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                            new LoggingPair(LoggingKey.LeadId, lead.Id)
                        }, this, methodName);
                }
            }
            catch (Exception ex)
            {
                logger.Error("Error occurred while submitting lead to Afordal.",
                    new LoggingPair[] {
                        new LoggingPair(LoggingKey.AgentId, leadActivity.AgentId),
                        new LoggingPair(LoggingKey.LeadId, lead.Id)
                    }, ex, this, methodName);
            }
        }

        private void ParseSearchLocationForAddress(string searchLocation, out string streetAddress, out string city, out string state, out string zip)
        {
            streetAddress = null;
            city = null;
            state = null;
            zip = null;

            if (string.IsNullOrWhiteSpace(searchLocation))
                return;

            try
            {
                // Simple parsing logic for common address formats
                // This is a basic implementation - could be enhanced with more sophisticated parsing
                var parts = searchLocation.Split(',').Select(p => p.Trim()).ToArray();

                if (parts.Length >= 2)
                {
                    // Last part might contain state and zip
                    var lastPart = parts[parts.Length - 1];
                    var stateZipMatch = System.Text.RegularExpressions.Regex.Match(lastPart, @"([A-Z]{2})\s*(\d{5}(?:-\d{4})?)");

                    if (stateZipMatch.Success)
                    {
                        state = stateZipMatch.Groups[1].Value;
                        zip = stateZipMatch.Groups[2].Value;

                        // City is the second to last part
                        if (parts.Length >= 3)
                        {
                            city = parts[parts.Length - 2];
                            // Street address is everything before city
                            if (parts.Length >= 4)
                            {
                                streetAddress = string.Join(", ", parts.Take(parts.Length - 2));
                            }
                        }
                        else
                        {
                            city = parts[0];
                        }
                    }
                    else
                    {
                        // If no state/zip pattern found, assume last part is city
                        city = lastPart;
                        if (parts.Length >= 3)
                        {
                            streetAddress = string.Join(", ", parts.Take(parts.Length - 1));
                        }
                    }
                }
                else
                {
                    // Single part - assume it's a city
                    city = searchLocation;
                }
            }
            catch
            {
                // If parsing fails, just use the original search location as city
                city = searchLocation;
            }
        }
    }
}
