﻿#region N-Play Copyright Banner
/*******************************************************************\
 * Copyright © N-Play RE LLC. 2013.  All rights reserved.           *
 *                                                                  *
 * This code is property of N-Play and cannot be used without       *
 * explicit permission given by an official N-Play representative.  *
 * For details contact: <EMAIL>                               *
 \******************************************************************/
#endregion

using System.Collections.Generic;

using NPlay.Common.Abstract;
using NPlay.Common.Logging;
using NPlay.Common.Models;
using NPlay.Common.BaseRepository;
using NPlay.Common.Models.Payments;
using System;
using NPlay.StripePaymentHandler.Core.Models;
using System.Linq;
using System.Text;
using NPlay.Common;
using NPlay.Common.Abstract.Emailer;
using NPlay.Common.Models.Emails.Engine;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Messaging.Abstract;
using NPlay.BusinessLogic.Payment;
using NPlay.BusinessLogic.Payment.Abstract;
using NPlay.Common.Enums;

namespace NPlay.StripePaymentHandler.Core.PaymentHandlers
{
    /// <summary>
    /// PaymentHandlerBase adds the supported plan ids list to help
    /// control which plan ids a given payment handler can process.
    /// This is not a part of the interface as other implementations may
    /// want to handle CanProcess differently.
    /// </summary>
    public abstract class InvoicePaymentHandlerBase : PaymentHandlerBase, IPaymentHandlerOld
    {

        #region Private Fields

        /// <summary>
        /// A list of plan ids that a given processor can support.
        /// </summary>
        private readonly List<string> _supportedPlanIds = new List<string>();

        /// <summary>
        /// The _enabled flag determines if this handler is enabled or not.
        /// </summary>
        private bool _enabled = true;

        private readonly IRepository<SubscriptionsMissingFundingSource> SubscriptionsMissingFundingSourceRepository;
        private readonly IRepository<Membership> MembershipRepository;

        protected readonly IPaymentNotificationManagerOld NotificationManager;
        protected readonly IEmailGenerator EmailGenerator;
        protected readonly IRepositoryFactory Repositories;
        protected readonly IProductPlanRepository ProductPlanRepository;
        protected readonly IFundingSourceRepository FundingSourceRepository;
        protected readonly IEmailer Emailer;
        protected readonly IEventBroker EventBroker;
        protected readonly IPaymentSystem PaymentSystem;
        protected readonly ISubscriptionHelper SubscriptionHelper;
        protected string CSEmail = Settings.CustomerSupportEmailMain;

        #endregion

        protected InvoicePaymentHandlerBase(IRepository<TransactionLog> transactionLogRepository,
            IUnitOfWorkFactory uowFactory,
            ITransactionLogManager transactionLogManager,
            IPaymentProcessor paymentProcessor,
            ICampaignMonitorSubscriberHandler campaignMonitorSubscriptionHandler,
            IRepository<SubscriptionsMissingFundingSource> subscriptionsMissingFundingSourceRepository,
            IRepository<Membership> membershipRepository,
            IRepositoryFactory repo,
            IEmailer emailer,
            IEventBroker eventBroker,
            ILogger logger,
            IPaymentNotificationManagerOld notificationManager,
            IEmailGenerator emailGenerator,
            IPaymentSystem paymentSystem,
            ISubscriptionHelper subscriptionHelper)
            : base(campaignMonitorSubscriptionHandler,
                  logger,
                  transactionLogManager,
                  transactionLogRepository,
                  paymentProcessor)
        {
            SubscriptionsMissingFundingSourceRepository = subscriptionsMissingFundingSourceRepository;
            MembershipRepository = membershipRepository;
            Repositories = repo;
            Emailer = emailer;
            EventBroker = eventBroker;
            ProductPlanRepository = repo.ProductPlanRepository;
            FundingSourceRepository = repo.FundingSourceRepository;
            NotificationManager = notificationManager;
            EmailGenerator = emailGenerator;
            PaymentSystem = paymentSystem;
            SubscriptionHelper = subscriptionHelper;
        }

        #region Public Methods

        /// <summary>
        /// Adds the plan id to the list of supported plan ids for a given
        /// processor.
        /// </summary>
        /// <param name="planId">The plan id.</param>
        public void AddPlanId(string planId)
        {
            if (!_supportedPlanIds.Contains(planId))
            {
                Logger.Debug(string.Format("Added PlanId: {0} for {1}", planId, GetType().Name));

                _supportedPlanIds.Add(planId);
            }
        }

        /// <summary>
        /// Allows the addition of multiple plan ids in a single call.
        /// </summary>
        /// <param name="planIds">The plan ids.</param>
        public void AddPlanId(IList<string> planIds)
        {
            if (planIds != null)
                foreach (var planId in planIds)
                    AddPlanId(planId);
        }

        /// <summary>
        /// Determines whether this instance can process the specified response.
        /// </summary>
        /// <param name="responseType">Type of the response.</param>
        /// <param name="planId">The plan identifier.</param>
        /// <returns><c>true</c> if this instance can process the specified response; otherwise, <c>false</c>.</returns>
        public virtual bool CanProcess(string responseType, string planId, Dictionary<string, string> metaData, bool? isSubscription)
        {
            if (_supportedPlanIds.Count == 0)
                Load();

            return SupportedPlanIds.Contains(planId) && EventTypes != null && EventTypes.Contains(responseType);
        }

        protected abstract void PaymentSuccessful(Agent agent, FundingSource fundingSource, InvoicePaymentResponse paymentResult, WebHookHandlerResult result);
        protected abstract void PaymentFailed(Agent agent, FundingSource fundingSource, InvoicePaymentResponse paymentResult, WebHookHandlerResult result);

        /// <summary>
        /// Load is used to populate the plan ids that a given processor can support.
        /// </summary>
        public virtual void Load()
        {
            Logger.Info($"Loading supported plan for product {ProductType.ToString()}");

            var plans = ProductPlanRepository.ProductPlans.Where(pp => pp.ProductId == (byte)ProductType).ToList();

            foreach (var productPlan in plans)
                AddPlanId(productPlan.PlanId);

            EventTypes = new List<string> { Models.EventType.InvoicePaymentSucceededType, Models.EventType.InvoicePaymentFailedType };
        }

        public virtual WebHookHandlerResult ProcessResponse(object response, string stripeEventType)
        {
            Logger.Debug(string.Format("Entered {0} Payment Handler ProcessResponse method...", ProductType.ToString()));

            var paymentResult = (InvoicePaymentResponse)response;
            var result = GetWebhookHandlerResult(paymentResult);
            //response.SubscriptionId is null for invoice payment succeeded/failed event
            var subscriptionId = paymentResult.Data.InvoiceDetails.SubscriptionId;
            Logger.Debug($"Inside InvoicePaymentHandler for subscription - {subscriptionId}", null, this, "InvoicePaymentHandlerBase.ProcessResponse");

            var fundingSources = FundingSourceRepository.FundingSources.Where(f => f.SubscriptionId == subscriptionId).ToList();

            if (fundingSources.Count == 1)
            {
                var fundingSource = fundingSources[0];

                var agent = Repositories.AgentRepository.Agents.Where(a => a.Id == fundingSource.MembershipId).FirstOrDefault();
                if (agent == null)
                {
                    SaveSubscriptionMissingFundingSource(paymentResult, SubscriptionsMissingType.AgentNotFound);

                    if (DoNotProcessIfAgentIsNull)
                    {
                        var errorMessage = string.Format("Cannot process request for unknown member {0}. {1}", fundingSource.MembershipId, ProductType.ToString());
                        result.SetException(new Exception(errorMessage));
                        return result;
                    }
                }

                if (paymentResult.Type == Models.EventType.InvoicePaymentFailedType)
                {
                    if (agent != null)
                        SaveTransactionLog(agent.Id, paymentResult, TransactionType, TransactionStatuses.Declined);

                    PaymentFailed(agent, fundingSource, paymentResult, result);
                }
                else if (paymentResult.Type == Models.EventType.InvoicePaymentSucceededType)
                {
                    if (result.ChargeAmount > 0 && agent != null)
                        SaveTransactionLog(agent.Id, paymentResult, TransactionType, TransactionStatuses.Charged);

                    if (result.ChargeAmount > 0 || !DoNotProcessIfAmountIs0OrLessForPaymentSuccess)
                        PaymentSuccessful(agent, fundingSource, paymentResult, result);
                    else
                        Logger.Debug(string.Format("$0.00 charge received for {0}, ignoring processing for FundingSourceID: {1}", ProductType.ToString(), fundingSource.Id)); 
                }
            }
            else if (fundingSources.Count == 0)
            {
                Membership membership = null;
                Customer customer = null;
                if (!string.IsNullOrWhiteSpace(paymentResult?.Data?.InvoiceDetails?.Customer))
                {
                    membership = MembershipRepository.GetAll().Where(m => m.FundingAccountId == paymentResult.Data.InvoiceDetails.Customer).FirstOrDefault();
                    if (membership == null)
                    {
                        try
                        {
                            customer = PaymentSystem.Customer.GetCustomer(paymentResult?.Data?.InvoiceDetails?.Customer);
                        }
                        catch(Exception ex)
                        {
                            Logger.Error($"Could not get customer details from stripe for id {paymentResult?.Data?.InvoiceDetails?.Customer}", null, this, "InvoicePaymentHandlerBase.ProcessResponse");
                        }
                        if (customer != null)
                        {
                            membership = MembershipRepository.GetAll().Where(m => m.Email == customer.Email).FirstOrDefault();
                        }
                    }
                }
                List<FundingSource> agentFundingSources = null;
                byte? productId = null;

                Agent agent = null;
                if (membership != null)
                {
                    agent =  Repositories.AgentRepository.Agents.Where(a => a.Id == membership.Id).FirstOrDefault();
                }
                if (membership != null && membership.FundingAccountId == null)
                {
                    
                    Logger.Info($"Membership record customer account is null. So updating it to {paymentResult?.Data?.InvoiceDetails?.Customer}", null, this, "InvoicePaymentHandlerBase.ProcessResponse");
                    var subscription = PaymentSystem.Subscription.GetSubscription(subscriptionId);
                   

                    if (subscription != null)
                    {
                        try
                        {
                            SubscriptionHelper.FillProductPlanDetails(subscription);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Error filling in product details for subscription id {subscription.Id}", null, ex, this, "InvoicePaymentHandlerBase.SaveFundingSource");
                        }
                        productId = subscription?.ProductPlan?.ProductId;
                        agentFundingSources = FundingSourceRepository.FundingSources.Where(f => f.MembershipId == membership.Id && f.ProductTypeId == productId && f.Active == true).ToList();
                    }
                    if (agentFundingSources?.Count() > 0 && !ShouldBypassDuplicateSubscriptionCheck(productId, subscription))
                    {

                        SaveSubscriptionMissingFundingSource(paymentResult, SubscriptionsMissingType.SubscriptionAlreadyExists);
                    }
                    else
                    {
                        membership.FundingAccountId = paymentResult.Data.InvoiceDetails.Customer;
                        MembershipRepository.Update(membership);
                        MembershipRepository.SaveChanges();
                        var fundingSource = SaveFundingSource(membership, paymentResult?.Data?.InvoiceDetails?.Customer, subscriptionId);
                        Logger.Info($"Created fundingsource for agent {membership.Id} for subscription {subscriptionId} adding the customer id to membership record", null, this, "InvoicePaymentHandlerBase.ProcessResponse");

                        if (paymentResult.Type == Models.EventType.InvoicePaymentSucceededType)
                        {
                            if (result.ChargeAmount > 0 && membership != null)
                                SaveTransactionLog(membership.Id, paymentResult, TransactionType, TransactionStatuses.Charged);

                            if (agent != null)
                            {

                                if (result.ChargeAmount > 0 || !DoNotProcessIfAmountIs0OrLessForPaymentSuccess)
                                    PaymentSuccessful(agent, fundingSource, paymentResult, result);
                                else
                                    Logger.Debug(string.Format("$0.00 charge received for {0}, ignoring processing for FundingSourceID: {1}", ProductType.ToString(), fundingSource.Id));
                            }
                        }
                    }
                }
                else if(membership != null && membership.FundingAccountId != paymentResult?.Data?.InvoiceDetails?.Customer)
                {
                    Logger.Info($"Customer account differs from the one in membership record, {membership.FundingAccountId} and {paymentResult?.Data?.InvoiceDetails?.Customer}", null, this, "InvoicePaymentHandlerBase.ProcessResponse");
                    var subscription = PaymentSystem.Subscription.GetSubscription(subscriptionId);
                    if (subscription != null)
                    {
                        try
                        {
                            SubscriptionHelper.FillProductPlanDetails(subscription);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Error filling in product details for subscription id {subscription.Id}", null, ex, this, "InvoicePaymentHandlerBase.SaveFundingSource");
                        }
                        productId = subscription?.ProductPlan?.ProductId;
                        agentFundingSources = FundingSourceRepository.FundingSources.Where(f => f.MembershipId == membership.Id && f.ProductTypeId == productId && f.Active == true).ToList();
                    }
                    if (agentFundingSources?.Count() > 0 && !ShouldBypassDuplicateSubscriptionCheck(productId, subscription))
                    {

                        SaveSubscriptionMissingFundingSource(paymentResult, SubscriptionsMissingType.SubscriptionAlreadyExists);
                    }
                    else
                    {
                        var movedSubscription = SubscriptionHelper.MoveSubscription(membership.FundingAccountId, subscriptionId, membership.Id, 0);
                        Logger.Info($"Moved subscription from {subscriptionId} to {movedSubscription?.Subscription?.Id}", null, this, "InvoicePaymentHandlerBase.ProcessResponse");
                    }
                }
                else if(membership != null && membership.FundingAccountId == paymentResult?.Data?.InvoiceDetails?.Customer)
                {
                    Logger.Info($"Membership record customer account matches with {paymentResult?.Data?.InvoiceDetails?.Customer}", null, this, "InvoicePaymentHandlerBase.ProcessResponse");
                    var subscription = PaymentSystem.Subscription.GetSubscription(paymentResult.Data.InvoiceDetails.Customer, subscriptionId);
                    if (subscription != null)
                    {
                        try
                        {
                            SubscriptionHelper.FillProductPlanDetails(subscription);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"Error filling in product details for subscription id {subscription.Id}", null, ex, this, "InvoicePaymentHandlerBase.SaveFundingSource");
                        }
                        productId = subscription?.ProductPlan?.ProductId;
                        agentFundingSources = FundingSourceRepository.FundingSources.Where(f => f.MembershipId == membership.Id && f.ProductTypeId == productId && f.Active == true).ToList();
                    }
                    if (agentFundingSources?.Count() > 0 && !ShouldBypassDuplicateSubscriptionCheck(productId, subscription))
                    {

                        SaveSubscriptionMissingFundingSource(paymentResult, SubscriptionsMissingType.SubscriptionAlreadyExists);
                    }
                    else
                    {
                        var fundingSource = SaveFundingSource(membership, membership.FundingAccountId, subscriptionId);
                        Logger.Info($"Created fundingsource for agent {membership.Id} for subscription {subscriptionId} with matching customer id", null, this, "InvoicePaymentHandlerBase.ProcessResponse");
                        if (paymentResult.Type == Models.EventType.InvoicePaymentSucceededType)
                        {
                            if (result.ChargeAmount > 0 && membership != null)
                                SaveTransactionLog(membership.Id, paymentResult, TransactionType, TransactionStatuses.Charged);

                            if (agent != null)
                            {
                                if (result.ChargeAmount > 0 || !DoNotProcessIfAmountIs0OrLessForPaymentSuccess)
                                    PaymentSuccessful(agent, fundingSource, paymentResult, result);
                                else
                                    Logger.Debug(string.Format("$0.00 charge received for {0}, ignoring processing for FundingSourceID: {1}", ProductType.ToString(), fundingSource.Id));
                            }
                        }
                    }
                }
                else
                {
                    SaveSubscriptionMissingFundingSource(paymentResult, SubscriptionsMissingType.FundingSourceNotFound);
                    var errorMessage = string.Format("Funding soure not found for {0}. {1}", subscriptionId, ProductType.ToString());
                    result.SetException(new Exception(errorMessage));
                }
            }
            else if (fundingSources.Count > 1)
            {
                SaveSubscriptionMissingFundingSource(paymentResult, SubscriptionsMissingType.MultipleFundingSources);
                var errorMessage = string.Format("Multiple funding sources found for {0}. {1}", subscriptionId, ProductType.ToString());
                result.SetException(new Exception(errorMessage));
            }

            return result;
        }

        protected void CheckIfActiveSubscriptionExists()
        {

        }

        protected void SendThirdFailedPaymentEmailToCS(int agentId, long fundingSourceId)
        {
            var email = new AutoEmail();
            email.UserId = agentId;
            email.Template = Emails.ThirdFailedPaymentCS.ToString();
            email.Parameters = fundingSourceId;
            email.UseAgentContactTime = false;
            var autoEmailEvent = new TransactionalAutoEmailEvent { Payload = email };
            EventBroker.Publish<TransactionalAutoEmailEvent, AutoEmail>(autoEmailEvent);
        }

        protected FundingSource SaveFundingSource(Membership membership, string customerId, string subscriptionId)
        {
            Logger.Info($"Inside SaveFundingSource method", null, this, "SaveFundingSource");
            var fundingSources = FundingSourceRepository.FundingSources.Where(f => f.SubscriptionId == subscriptionId && f.Active == true).ToList();
            if(fundingSources != null && fundingSources.Count > 0)
            {
                Logger.Info($"Found a funding source record already available. So not saving fundingsource again for subscription {subscriptionId}", null, this, "SaveFundingSource");
                return fundingSources.First();
            }
            var subscription = PaymentSystem.Subscription.GetSubscription(subscriptionId);
            if (subscription != null)
            {
                try
                {
                    SubscriptionHelper.FillProductPlanDetails(subscription);
                }
                catch(Exception ex)
                {
                    Logger.Error($"Error filling in product details for subscription id {subscription.Id}", null, ex, this, "InvoicePaymentHandlerBase.SaveFundingSource");
                }
            }
            var card = PaymentSystem.Card.GetDefaultCard(membership.Id);
            var fundingSource = new FundingSource()
            {
                ProviderId = customerId,
                ProductPlanId = Convert.ToInt32(subscription.ProductPlan.Id),
                ProductType = subscription.ProductPlan.ProductType,
                MembershipId = membership.Id,
                SubscriptionId = subscription.Id,
                TrialEnd = subscription.TrialEnd,
                DateCreated = DateTime.UtcNow,
                Active = true,
                CCExpirationMonth = Convert.ToInt32(card?.ExpirationMonth),
                CCLast4 = card?.Last4,
                CCExpirationYear = Convert.ToInt32(card?.ExpirationYear),
                CCType = card?.Type,
                CreatedBy = Enum.GetName(typeof(FundingSourceCreatedByType), FundingSourceCreatedByType.PaymentHandler)

            };
            var result = PaymentSystem.Local.SaveFundingSource(fundingSource);
            return result;
        }
        protected void SaveSubscriptionMissingFundingSource(InvoicePaymentResponse paymentResult, SubscriptionsMissingType missingType)
        {
            var cancelledSub = false;
            var subscriptionId = paymentResult.Data.InvoiceDetails.SubscriptionId;
            var membership = MembershipRepository.GetAll().Where(m => m.FundingAccountId == paymentResult.Data.InvoiceDetails.Customer).FirstOrDefault();
            var subscription = PaymentSystem.Subscription.GetSubscription(paymentResult.Data.InvoiceDetails.Customer, subscriptionId);
            if (subscription != null)
            {
                try
                {
                    SubscriptionHelper.FillProductPlanDetails(subscription);
                }
                catch(Exception ex)
                {
                    Logger.Error($"Could not fill in product details for subscription {subscription.Id}", null, ex, this, "SaveSubscriptionMissingFundingSource");
                }
            }
            var subscriptionMissingFundingSource = SubscriptionsMissingFundingSourceRepository.GetById(subscriptionId);
            var isNew = false;
            Customer customer = null;
            try
            {
                customer = PaymentSystem.Customer.GetCustomer(paymentResult?.Data?.InvoiceDetails?.Customer);
            }
            catch(Exception ex)
            {
                Logger.Error($"Unable to get customer from stripe for id {paymentResult?.Data?.InvoiceDetails?.Customer}", null, this, "SaveSubscriptionMissingFundingSource");
            }

            if (subscriptionMissingFundingSource == null)
            {
                subscriptionMissingFundingSource = new SubscriptionsMissingFundingSource()
                {
                    SubscriptionId = subscriptionId,
                    CreatedDate = DateTime.UtcNow
                };
                isNew = true;
            }

            subscriptionMissingFundingSource.CustomerId = paymentResult.Data.InvoiceDetails.Customer;
            subscriptionMissingFundingSource.InvoiceId = paymentResult.Data.InvoiceDetails.Id;
            subscriptionMissingFundingSource.EventId = paymentResult.Id;
            subscriptionMissingFundingSource.IsCancelled = cancelledSub;
            subscriptionMissingFundingSource.MembershipId = membership == null ? 0 : membership.Id;
            subscriptionMissingFundingSource.SubscriptionsMissingTypeId = (byte)missingType;
            subscriptionMissingFundingSource.Reason = null;
            subscriptionMissingFundingSource.Confirmed = null;
            subscriptionMissingFundingSource.ConfirmedBy = null;
            subscriptionMissingFundingSource.ConfirmedDate = null;
            subscriptionMissingFundingSource.Notes = null;
            subscriptionMissingFundingSource.Email = customer?.Email;
            subscriptionMissingFundingSource.Name = customer?.Description;
            subscriptionMissingFundingSource.ProductId = subscription?.ProductPlan?.ProductId;
            subscriptionMissingFundingSource.ProductType = subscription?.ProductPlan?.ProductType.GetDescription();
            subscriptionMissingFundingSource.ProductPlanId = subscription?.ProductPlan?.Id;
            subscriptionMissingFundingSource.ProductPlanDescription = subscription?.ProductPlan?.PlanId;
            
            if (isNew)
                SubscriptionsMissingFundingSourceRepository.Add(subscriptionMissingFundingSource);
            else
                subscriptionMissingFundingSource.UpdatedDate = DateTime.UtcNow;

            SubscriptionsMissingFundingSourceRepository.SaveChanges();
        }

        /// <summary>
        /// Determines if the duplicate subscription check should be bypassed for the given product and subscription.
        /// </summary>
        /// <param name="productId">The product ID</param>
        /// <param name="subscription">The subscription details</param>
        /// <returns>True if duplicate check should be bypassed, false otherwise</returns>
        private bool ShouldBypassDuplicateSubscriptionCheck(byte? productId, Subscription subscription)
        {
            // PageCreate products always bypass the duplicate check
            if (productId == (byte)ProductType.PageCreate)
                return true;

            // Afordal IDX products should bypass the duplicate check
            bool isAfordalIdxProduct = productId == (byte)ProductType.IDX && 
                                     subscription?.ProductPlan?.ProductPlanTypeId != null &&
                                     (subscription.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalIncluded ||
                                      subscription.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded);

            return isAfordalIdxProduct;
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets a value indicating whether the current instance of <see cref="InvoicePaymentHandlerBase"/> is enabled.
        /// </summary>
        /// <value><c>true</c> if enabled; otherwise, <c>false</c>.</value>
        public bool Enabled
        {
            get { return _enabled; }
            set { _enabled = value; }
        }

        /// <summary>
        /// Gets the supported plan ids.
        /// </summary>
        /// <value>The supported plan ids.</value>
        public List<string> SupportedPlanIds
        {
            get
            {
                return _supportedPlanIds;
            }
        }

        /// <summary>
        /// Gets or sets the event types.
        /// </summary>
        /// <value>The event types.</value>
        public List<string> EventTypes { get; set; }

        #endregion

        #region Protected Properties

        /// <summary>
        /// Currently only IDX and Page Engage are false
        /// </summary>
        protected virtual bool DoNotProcessIfAgentIsNull
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// Currently only TKS is false
        /// </summary>
        protected virtual bool DoNotProcessIfAmountIs0OrLessForPaymentSuccess
        {
            get
            {
                return true;
            }
        }

        protected virtual ProductType ProductType
        {
            get
            {
                return ProductType.Legacy;
            }
        }

        protected virtual TransactionTypes TransactionType
        {
            get
            {
                return TransactionTypes.Other;
            }
        }

        protected const string EmailFromDisplayName = "Real Estate Agent Directory";

        #endregion
    }


}
