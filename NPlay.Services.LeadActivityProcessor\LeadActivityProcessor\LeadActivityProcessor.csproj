﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProjectGuid>{A7505BDB-31C7-400C-A7F6-8F751CD3F177}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LeadActivityProcessor</RootNamespace>
    <AssemblyName>LeadActivityProcessor</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
    <IsServiceFabricServiceProject>True</IsServiceFabricServiceProject>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <AdditionalFileItemNames>$(AdditionalFileItemNames);None</AdditionalFileItemNames>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|x64'">
    <OutputPath>bin\x64\Test\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Label="SlowCheetah">
    <SlowCheetahToolsPath>$([System.IO.Path]::GetFullPath( $(MSBuildProjectDirectory)\..\packages\SlowCheetah.2.5.15\tools\))</SlowCheetahToolsPath>
    <SlowCheetah_EnableImportFromNuGet Condition=" '$(SlowCheetah_EnableImportFromNuGet)'=='' ">true</SlowCheetah_EnableImportFromNuGet>
    <SlowCheetah_NuGetImportPath Condition=" '$(SlowCheetah_NuGetImportPath)'=='' ">$([System.IO.Path]::GetFullPath( $(MSBuildProjectDirectory)\Properties\SlowCheetah\SlowCheetah.Transforms.targets ))</SlowCheetah_NuGetImportPath>
    <SlowCheetahTargets Condition=" '$(SlowCheetah_EnableImportFromNuGet)'=='true' and Exists('$(SlowCheetah_NuGetImportPath)') ">$(SlowCheetah_NuGetImportPath)</SlowCheetahTargets>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=5.2.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.5.2.0\lib\net45\AutoMapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.ServiceFabric.Data, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.Data.2.8.232\lib\net45\Microsoft.ServiceFabric.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ServiceFabric.Data.Interfaces, Version=5.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.Data.2.8.232\lib\net45\Microsoft.ServiceFabric.Data.Interfaces.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ServiceFabric.Diagnostics, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.Diagnostics.Internal.2.8.232\lib\net45\Microsoft.ServiceFabric.Diagnostics.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ServiceFabric.Internal, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.6.0.232\lib\net45\Microsoft.ServiceFabric.Internal.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ServiceFabric.Internal.Strings, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.6.0.232\lib\net45\Microsoft.ServiceFabric.Internal.Strings.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ServiceFabric.Preview, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.6.0.232\lib\net45\Microsoft.ServiceFabric.Preview.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ServiceFabric.Services, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.Services.2.8.232\lib\net45\Microsoft.ServiceFabric.Services.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Ninject, Version=3.3.4.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.3.3.4\lib\net45\Ninject.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Fabric, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.6.0.232\lib\net45\System.Fabric.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Fabric.Management.ServiceModel, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.6.0.232\lib\net45\System.Fabric.Management.ServiceModel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Fabric.Management.ServiceModel.XmlSerializers, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ServiceFabric.6.0.232\lib\net45\System.Fabric.Management.ServiceModel.XmlSerializers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Fabric.Strings, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=AMD64">
      <HintPath>..\packages\Microsoft.ServiceFabric.6.0.232\lib\net45\System.Fabric.Strings.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Bindings.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="LeadActivityProcessor.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="ServiceEventSource.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Debug.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="App.Release.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="App.Test.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="PackageRoot\Config\Settings.xml">
      <TransformOnBuild>true</TransformOnBuild>
    </None>
    <None Include="PackageRoot\ServiceManifest.xml" />
    <None Include="App.config">
      <TransformOnBuild>true</TransformOnBuild>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\SlowCheetah\SlowCheetah.Transforms.targets" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\NPlay.Common.AgentSMS\NPlay.Common.AgentSMS.csproj">
      <Project>{8ecf79e3-931f-40af-a08c-65f65f745334}</Project>
      <Name>NPlay.Common.AgentSMS</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.BaseRepository\NPlay.Common.BaseRepository.csproj">
      <Project>{a8bf49bd-d362-4bd4-a7c9-61a21599a3c9}</Project>
      <Name>NPlay.Common.BaseRepository</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Logging\NPlay.Common.Logging\NPlay.Common.Logging.csproj">
      <Project>{c01dcec7-5c83-45f2-95bb-c410363b64c0}</Project>
      <Name>NPlay.Common.Logging</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Messaging.Abstract\NPlay.Common.Messaging.Abstract.csproj">
      <Project>{81039773-69ea-4a8e-ae16-147b47f3dd96}</Project>
      <Name>NPlay.Common.Messaging.Abstract</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Messaging\NPlay.Common.Messaging.csproj">
      <Project>{F5086483-F4F6-4C2F-A309-0EF282658C3F}</Project>
      <Name>NPlay.Common.Messaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models.ExtendedServices\NPlay.Common.Models.ExtendedServices.csproj">
      <Project>{c8969695-2c5b-4276-98b6-5ac770a4e1b5}</Project>
      <Name>NPlay.Common.Models.ExtendedServices</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models.Mapping\NPlay.Common.Models.Mapping.csproj">
      <Project>{c610bb6a-ab29-43f3-aac3-cf1a37056116}</Project>
      <Name>NPlay.Common.Models.Mapping</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models.Services\NPlay.Common.Models.Services.csproj">
      <Project>{dcb7fefc-327d-4340-9094-585f7a17a02c}</Project>
      <Name>NPlay.Common.Models.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models\NPlay.Common.Models.csproj">
      <Project>{abe52228-12d2-4c13-a8d8-9ca9bda61b7d}</Project>
      <Name>NPlay.Common.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Repository\NPlay.Common.Repository.csproj">
      <Project>{1542547d-87c3-4e4c-96b0-e9694c0c427a}</Project>
      <Name>NPlay.Common.Repository</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Search\NPlay.Common.Search.csproj">
      <Project>{a6e96fd0-3b8a-4a3d-86b6-91817d7f0721}</Project>
      <Name>NPlay.Common.Search</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.ServiceAgents\NPlay.Common.ServiceAgents.csproj">
      <Project>{6291ee8e-ded7-49af-ad28-3f08d66017f4}</Project>
      <Name>NPlay.Common.ServiceAgents</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Services.Abstract\NPlay.Common.Services.Abstract.csproj">
      <Project>{f06efa62-ef3a-4e42-8bf0-e6ce50d23907}</Project>
      <Name>NPlay.Common.Services.Abstract</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common\NPlay.Common.csproj">
      <Project>{931003bd-1c89-4e22-a66a-461ec1e845c2}</Project>
      <Name>NPlay.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\Nplay.Services.Data\Nplay.Services.Data.csproj">
      <Project>{51068129-4c26-4da4-a55e-c7267768287b}</Project>
      <Name>Nplay.Services.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\NPlay.Services.NPlayApi\NPlay.BusinessLogic.Leads\NPlay.BusinessLogic.Leads.csproj">
      <Project>{6f8d528f-e5c5-4918-90f8-72d2d38431d8}</Project>
      <Name>NPlay.BusinessLogic.Leads</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="PackageRoot\Config\Settings.Debug.xml">
      <DependentUpon>Settings.xml</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="PackageRoot\Config\Settings.Release.xml">
      <DependentUpon>Settings.xml</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="PackageRoot\Config\Settings.Test.xml">
      <DependentUpon>Settings.xml</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SlowCheetahTargets)" Condition="Exists('$(SlowCheetahTargets)')" Label="SlowCheetah" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>