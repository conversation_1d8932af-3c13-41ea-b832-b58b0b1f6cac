﻿using MLS.Synchronization.Abstract;
using Ninject;
using NPlay.Common.Abstract;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Models;
using NPlay.Common.Services.Abstract;
using NPlay.Tasks.Data.RatePlugExport.Abstract;
using RealEstateListingApp;
using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;

namespace NPlay.Tasks.Data.RatePlugExport
{
    class Program
    {
        static IKernel kernel;

        static void Main(string[] args)
        {
            try
            {
                kernel = new StandardKernel();
                kernel.Load(Assembly.GetExecutingAssembly());

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

                var settingRepo = kernel.Get<IRepository<Setting>>();
                Settings.Overrides = settingRepo.GetAll().ToDictionary(s => s.Key, s => s.Value);

                IRatePlugExporter exporter = kernel.Get<IRatePlugExporter>();
                DateTime start = DateTime.UtcNow;
                if (!Directory.Exists(@".\results"))
                {
                    Directory.CreateDirectory(@".\results");
                }

                string mlsId = null;
                if (args.Length > 1 && args[1].ToLower().Contains("/mlsid:"))
                {
                    mlsId = args[1].Split(new char[] { ':' })[1];
                }

                if (args.Length > 0 && args[0] == "/import")
                {
                    exporter.Import(args[1], args[2]);
                }
                else if (args.Length > 0 && args[0] == "/special_finance_update")
                {
                    var bulkUpdateTask = Task.Run(async () =>
                    {
                        await BulkSpecialFinanceUpdate.UpdateSpecialFinancingForAllListings();
                    });

                    Task.WaitAll(bulkUpdateTask);
                }
                else if (args.Length > 0 && args[0] == "/normalize_all_addresses")
                {
                    IActiveListingRepository activeListingRepository = kernel.Get<IActiveListingRepository>();
                    IEventBroker eventBroker = kernel.Get<IEventBroker>();
                    var bulkUpdateTask = Task.Run(async () =>
                    {
                        await BulkSpecialFinanceUpdate.UpdateNormalizedAddressesForAllListings(activeListingRepository, eventBroker, mlsId);
                    });

                    Task.WaitAll(bulkUpdateTask);
                }
                else if (args.Length > 0 && args[0] == "/special_finance_assumables_update")
                {
                    IRatePlugClient ratePlugClient = kernel.Get<IRatePlugClient>();
                    IActiveListingRepository activeListingRepository = kernel.Get<IActiveListingRepository>();
                    IEventBroker eventBroker = kernel.Get<IEventBroker>();
                    var bulkUpdateTask = Task.Run(async () =>
                    {
                        await BulkSpecialFinanceUpdate.UpdateAssumableStatusForAllListings(ratePlugClient, activeListingRepository, eventBroker, mlsId);
                    });

                    Task.WaitAll(bulkUpdateTask);
                }
                else if (args.Length > 0 && args[0] == "/special_finance_fhacondo_update")
                {
                    IRatePlugClient ratePlugClient = kernel.Get<IRatePlugClient>();
                    IActiveListingRepository activeListingRepository = kernel.Get<IActiveListingRepository>();
                    IEventBroker eventBroker = kernel.Get<IEventBroker>();
                    var bulkUpdateTask = Task.Run(async () =>
                    {
                        await BulkSpecialFinanceUpdate.UpdateFHACondoStatusForAllListings(ratePlugClient, activeListingRepository, eventBroker, mlsId);
                    });

                    Task.WaitAll(bulkUpdateTask);
                }
                else if (args.Length > 0 && args[0] == "/special_finance_usda_update")
                {
                    IRatePlugClient ratePlugClient = kernel.Get<IRatePlugClient>();
                    IActiveListingRepository activeListingRepository = kernel.Get<IActiveListingRepository>();
                    IEventBroker eventBroker = kernel.Get<IEventBroker>();
                    var bulkUpdateTask = Task.Run(async () =>
                    {
                        await BulkSpecialFinanceUpdate.UpdateUSDAEligibilityForAllListings(ratePlugClient, activeListingRepository, eventBroker, mlsId);
                    });

                    Task.WaitAll(bulkUpdateTask);
                }
                else
                {
                    exporter.Export(@".\results\HomeASAP_Listings.txt", @".\results\HomeASAP_Agents.txt", @".\results\HomeASAP_AgentOffices.txt");
                }

                DateTime end = DateTime.UtcNow;
                Console.WriteLine($"Started at {start}.");
                Console.WriteLine($"Ended at   {end}.");
                Console.WriteLine($"Total time {(end - start).TotalSeconds}");
            }
            catch(Exception ex)
            {
                Console.WriteLine(ex);
                Environment.Exit(ex.GetHashCode());
            }
        }
    }
}
