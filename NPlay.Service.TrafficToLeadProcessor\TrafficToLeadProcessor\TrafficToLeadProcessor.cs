using System;
using System.Collections.Generic;
using System.Fabric;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.ServiceFabric.Services.Communication.Runtime;
using Microsoft.ServiceFabric.Services.Runtime;
using Ninject;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Abstract;
using System.Reflection;
using NPlay;
using NPlay.Common.Models;
using NPlay.Common.Messaging;
using NPlay.BusinessLogic.Asap.Models;
using NPlay.Common.Identity.Abstract;
using NPlay.BusinessLogic.Asap;
using NPlay.Common.Models.Mapping;
using AutoMapper;
using NPlay.Common;
using NPlay.Common.Models.Services;
using NPlay.BusinessLogic.LoginRegistration;
using NPlay.Common.Messaging.Events;

namespace TrafficToLeadProcessor
{
    /// <summary>
    /// An instance of this class is created for each service instance by the Service Fabric runtime.
    /// </summary>
    internal sealed class TrafficToLeadProcessor : StatelessService
    {
        static string ConsumerKey = "TrafficToLeadConvertorService";
        static IKernel Kernel;
        static IEventBroker EventBroker;
        static ILogger Logger;
        static IBuyerSignupEmailHandler BuyerSignupEmailHandler;

        public TrafficToLeadProcessor(StatelessServiceContext context)
            : base(context)
        {
            Kernel = new StandardKernel();
            Kernel.Load(Assembly.GetExecutingAssembly());

            Logger = Kernel.Get<ILogger>();
            Logger.Info("Starting the TrafficToLeadConvertor Service", null, this, "OnStart");

            var settingRepo = Kernel.Get<IRepository<Setting>>();
            Settings.Overrides = settingRepo.GetAll().ToDictionary(s => s.Key, s => s.Value);
            Logger.Info("Loaded settings");

            EventBroker = Kernel.Get<IEventBroker>();
            BuyerSignupEmailHandler = Kernel.Get<IBuyerSignupEmailHandler>();

            Mapper.Initialize(x => x.AddProfile<LeadMappingProfile>());
        }

        /// <summary>
        /// Optional override to create listeners (e.g., TCP, HTTP) for this service replica to handle client or user requests.
        /// </summary>
        /// <returns>A collection of listeners.</returns>
        protected override IEnumerable<ServiceInstanceListener> CreateServiceInstanceListeners()
        {
            return new ServiceInstanceListener[0];
        }

        /// <summary>
        /// This is the main entry point for your service instance.
        /// </summary>
        /// <param name="cancellationToken">Canceled when Service Fabric needs to shut down this service instance.</param>
        protected override async Task RunAsync(CancellationToken cancellationToken)
        {
            EventBroker.Consume<BuyerLoginEvent, BuyerLogin>(
            (e) =>
            {
                Task worker = new Task(() =>
                {
                    try
                    {
                        ProcessMessage(e);
                        Logger.Info("Processed lead to traffic convertor", null, "TrafficToLeadConvertorService", "RunAsync");
                        e.Acknowledge();

                    }
                    catch (Exception ex)
                    {
                        Logger.Error("Unable to process message.", null, ex, "TrafficToLeadConvertorService", "RunAsync");
                        Console.WriteLine("rejected");
                        e.Reject();
                    }
                });
                worker.Start();
            }, ConsumerKey);

            while (true)
            {
                cancellationToken.ThrowIfCancellationRequested();

                await Task.Delay(TimeSpan.FromSeconds(1), cancellationToken);
            }
        }

        private void ProcessMessage(BuyerLoginEvent e)
        {
            const string methodName = "ProcessMessage";
            var logger = Kernel.Get<ILogger>();
            logger.TrackingGuid = e.TrackingId;
            logger.Info("Processing BuyerLoginEvent.",
                new LoggingPair[] {
                    new LoggingPair(LoggingKey.BuyerId, e.Payload.BuyerId),
                    new LoggingPair(LoggingKey.CookieId, e.Payload.CookieId),
                    new LoggingPair(LoggingKey.Email, e.Payload.Email),
                    new LoggingPair(LoggingKey.IsAgent, e.Payload.IsAgent)
                }, this, methodName);

            var payload = e.Payload;

            if (payload.BuyerId != null)
            {
                if (FacebookProfileImageProcessor.ShouldUploadImage(payload.ProfileImageUrl, FacebookProfileImageType.Buyer))
                {
                    var request = new FacebookProfileImageRequest
                    {
                        MembershipId = payload.BuyerId.Value,
                        ImageType = FacebookProfileImageType.Buyer
                    };
                    var imageEvent = new FacebookProfileImageEvent { Payload = request };
                    EventBroker.Publish<FacebookProfileImageEvent, FacebookProfileImageRequest>(imageEvent);
                }
            }

            if (!payload.IsAgent)
            {
                if (payload.SendWelcomeEmail && payload.FromLeadAds && payload.AgentIdForWelcomeEmail != null)
                {
                    BuyerSignupEmailHandler.Send(e.Payload, payload.AgentIdForWelcomeEmail.Value);
                }
                //if this is not from lead ads, we send welcome email in leadGeneratorService.ConvertToLead(payload).
                //the reason why we send email here when payload.FromLeadAds is true is because for lead ads webhook, we needed to call buyer auth before creating
                //lead activity to create Buyer, so there will be no lead yet.

                var leadGeneratorService = Kernel.Get<ILeadGeneratorService>();
                leadGeneratorService.ConvertToLead(payload);

                IServiceIdentityProvider identityProvider = (IServiceIdentityProvider)Kernel.Get<INPlayIdentityProvider>();
                identityProvider.BuyerId = payload.BuyerId;
                identityProvider.CookieId = payload.CookieId;
                identityProvider.Email = payload.Email;
                identityProvider.IsAgent = payload.IsAgent;
                identityProvider.RequestIpAddress = payload.RequestIpAddress;

                ILeadTrafficDelivery leadTrafficDelivery = Kernel.Get<ILeadTrafficDelivery>();
                leadTrafficDelivery.DeliverRetroLeads(payload);
            }
            else
            {
                Logger.Info("Did not create a lead since this buyer is also an agent.", new LoggingPair[] {
                    new LoggingPair(LoggingKey.BuyerId, e.Payload.BuyerId),
                    new LoggingPair(LoggingKey.CookieId, e.Payload.CookieId),
                    new LoggingPair(LoggingKey.Email, e.Payload.Email),
                    new LoggingPair(LoggingKey.IsAgent, e.Payload.IsAgent)
                }, this, methodName);

                if (payload.SendWelcomeEmail)
                {
                    //We want to show buyer himself as agent
                    BuyerSignupEmailHandler.Send(e.Payload, e.Payload.BuyerId.Value);
                }
            }
        }
    }
}
