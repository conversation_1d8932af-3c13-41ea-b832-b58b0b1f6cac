﻿namespace MLS.Synchronization.Models
{
    public enum DataFeedScheduleStatus
    {
        UnScheduled = 1,
        Scheduled = 2
    }

    public enum DataFeedProtocol
    {
        RETS = 1,
        RESOWebApi = 2
    }

    public enum DataFeedStatus
    {
        Active = 1,
        Paused = 2
    }

    public enum DataFeedState
    {
        Idle = 1,
        Pending = 2,
        Running = 3,
        Error = 4,
        Retry = 5
    }

    public enum DataFeedType
    {
        Agent = 1,
        Office = 2,
        Property = 3,
        OpenHouse = 4
    }

    public enum MappingStatus
    {
        NotStarted = 1,
        InProgress = 2,
        ReadyForQA = 3,
        Approved = 4
    }

    public enum IntervalType
    {
        Minutes = 1,
        Hours = 2
    }

    public enum DataProviders
    {
        Trestle,
        Matrix,
        CoreLogic,
        Paragon,
        MlsGrid,
        Bridge,
        UtahRealEstate,
        RMLS,
        Rapattoni,
        Spark,
        ConnectMLS,
        MlsCustom,
        Perchwell,
        OpenMLS,
        NotSet
    }
}
