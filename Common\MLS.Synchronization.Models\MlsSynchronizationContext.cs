﻿using System;

namespace MLS.Synchronization.Models
{
    public class MlsSynchronizationContext
    {
        public static string DefaultCountry = "USA";
        public string MlsId { get; set; }
        public string DataProviderMlsId { get; set; }
        public string MediaFeedKeyFieldName { get; set; }
        public string MediaFeedClassName { get; set; } = "Media";
        public bool UseOwnershipForCondoMapping { get; set; } = false;
        public bool UseCumulativeDaysOnMarket { get; set; } = false;

        public string modificationTimestampFieldName = null;
        public string ModificationTimestampFieldName
        {
            get
            {
                if (modificationTimestampFieldName != null)
                    return modificationTimestampFieldName;

                switch (DataProvider)
                {
                    case DataProviders.Paragon:
                        return "APIModificationTimestamp";
                    case DataProviders.Bridge:
                        return "BridgeModificationTimestamp";
                    default:
                        return "ModificationTimestamp";
                }
            }
            set
            {
                modificationTimestampFieldName = value;
            }
        }
        public string DataProviderMlsIdFieldName
        {
            get
            {
                switch (DataProvider)
                {
                    case DataProviders.MlsGrid:
                        return "OriginatingSystemName";
                    case DataProviders.Bridge:
                        return "OriginatingSystemID";
                    case DataProviders.Trestle:
                        return "OriginatingSystemName";
                    default:
                        return "mlsId";
                }
            }
        }

        public bool ParticipatesInMissingDataJobs
        {
            get
            {
                switch (DataProvider)
                {
                    case DataProviders.MlsGrid:
                        return false;
                    default:
                        return true;
                }
            }
        }

        public bool ParticipatesInRemoveDataJobs
        {
            get;
            set;
        } = true;

        public string DataProviderListingIdFieldName
        {
            get
            {
                switch (DataProvider)
                {
                    case DataProviders.Bridge:
                    case DataProviders.MlsGrid:
                    case DataProviders.Rapattoni:
                    case DataProviders.Spark:
                    case DataProviders.Trestle:
                    case DataProviders.Perchwell:
                    case DataProviders.OpenMLS:
                        return "ListingId";
                    default:
                        return "propertyListingId";
                }
            }
        }

        public string DataProviderMediaItemListingIdFieldName
        {
            get
            {
                switch (DataProvider)
                {
                    case DataProviders.MlsGrid:
                        return "ResourceRecordID";
                    default:
                        return "ResourceRecordID";
                }
            }
        }

        public MediaQueryListingIdField MediaQueryListingIdField
        {
            get
            {
                switch (DataProvider)
                {
                    case DataProviders.MlsGrid:
                        return MediaQueryListingIdField.ListingId;
                    case DataProviders.Bridge:
                        return MediaQueryListingIdField.ListingId;
                    case DataProviders.Trestle:
                        return MediaQueryListingIdField.ListingKey;
                    default:
                        return MediaQueryListingIdField.ListingId;
                }
            }
        }

        public bool ProvidesImageCount { get; set; } = true;
        public string Country { get; set; } = DefaultCountry;
        public RateLimitingConfiguration RateLimitingConfiguration { get; set; }
        public MediaMetadataInfo MediaMetadataInfo { get; set; } = new MediaMetadataInfo();
        public bool RequiresXMLFormatInMetadataQuery { get; set; } = false;
        public string DataProviderListingPrefix { get; set; } = null;
        public DataFeedProtocol ProtocolType { get; set; } = DataFeedProtocol.RETS;  // Default to the RETS protocol
        public DataProviders DataProvider { get; set; } = DataProviders.NotSet;
        public string AttributionContactFieldName { get; set; } = "AttributionContact"; 
        public MlsDataRestrictions DataRestrictions { get; set; } = MlsDataRestrictions.None;
        public AuthenticationActionResponseStyle AuthenticationActionResponseStyle { get; set; } = AuthenticationActionResponseStyle.RETS;
        public Func<string, bool> ValidateAuthenticationAction = null;
        public string PhotoDownloadType { get; set; }
        public bool IncludesPrivatePhotos { get; set; } = false;

        public bool ShowFeaturedListingsForIDXOnly { get; set; }

        public bool UseSquareFootRange { get; set; } = false;

        public string SquareFootRangeFieldName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a high limit is needed in media queries - otherwise the data feed is paged and only returns a small number of available images.
        /// </summary>
        public bool IncludeLimitInMediaQuery { get; set; } = false;

        /// <summary>
        /// Gets or sets a value indicating whether the MLS provides a ListOnInternet value in the data feed
        /// </summary>
        public bool ProvidesListOnInternet { get; set; } = true;

        public ImageLocationType ImageLocationType { get; set; }

        public MetadataDownloadStyle MetadataDownloadStyle { get; set; } = MetadataDownloadStyle.Http;
        public string MetadataTypeParameterName { get; set; } = null;

        public string GetPropertyListingId(string sourcePropertyListingId)
        {
            if (!String.IsNullOrWhiteSpace(DataProviderListingPrefix))
            {
                if (sourcePropertyListingId?.StartsWith(DataProviderListingPrefix, StringComparison.InvariantCultureIgnoreCase) == true)
                {
                    return sourcePropertyListingId.Remove(0, DataProviderListingPrefix.Length);
                }
            }

            return sourcePropertyListingId;
        }
    }
}
