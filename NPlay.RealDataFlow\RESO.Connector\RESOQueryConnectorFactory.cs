﻿using MLS.Synchronization.Models;
using RESO.Connector.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RESO.Connector
{
    public class RESOQueryConnectorFactory : IRESOQueryConnectorFactory
    {
        IBridgeQueryConnector BridgeQueryConnector;
        ITrestleQueryConnector TrestleQueryConnector;
        IMlsGridListingQueryConnector MlsGridListingQueryConnector;
        IRapattoniQueryConnector RapattoniQueryConnector;
        IUtahRealEstateQueryConnector UtahRealEstateQueryConnector;
        IRMLSQueryConnector RMLSQueryConnector;
        ISparkQueryConnector SparkQueryConnector;
        IParagonQueryConnector ParagonQueryConnector;
        IPerchwellQueryConnector PerchwellQueryConnector;
        IOpenMLSQueryConnector OpenMLSQueryConnector;

        public RESOQueryConnectorFactory(IBridgeQueryConnector bridgeQueryConnector,
            ITrestleQueryConnector trestleQueryConnector,
            IMlsGridListingQueryConnector mlsGridListingQueryConnector,
            IRapattoniQueryConnector rapattoniQueryConnector,
            IUtahRealEstateQueryConnector utahRealEstateQueryConnector,
            IRMLSQueryConnector rmlsQueryConnector,
            ISparkQueryConnector sparkQueryConnector,
            IParagonQueryConnector paragonQueryConnector,
            IPerchwellQueryConnector perchwellQueryConnector,
            IOpenMLSQueryConnector openmlsQueryConnector)
        {
            BridgeQueryConnector = bridgeQueryConnector;
            TrestleQueryConnector = trestleQueryConnector;
            MlsGridListingQueryConnector = mlsGridListingQueryConnector;
            RapattoniQueryConnector = rapattoniQueryConnector;
            UtahRealEstateQueryConnector = utahRealEstateQueryConnector;
            RMLSQueryConnector = rmlsQueryConnector;
            SparkQueryConnector = sparkQueryConnector;
            ParagonQueryConnector = paragonQueryConnector;
            PerchwellQueryConnector = perchwellQueryConnector;
            OpenMLSQueryConnector = openmlsQueryConnector;
        }

        public IQueryConnector GetQueryConnector(MlsSynchronizationContext mlsContext, DataFeed feed)
        {
            switch (mlsContext.DataProvider)
            {
                case DataProviders.Trestle:
                    return TrestleQueryConnector;
                case DataProviders.Bridge:
                    return BridgeQueryConnector;
                case DataProviders.MlsGrid:
                    return MlsGridListingQueryConnector;
                case DataProviders.Rapattoni:
                    return RapattoniQueryConnector;
                case DataProviders.UtahRealEstate:
                    return UtahRealEstateQueryConnector;
                case DataProviders.RMLS:
                    return RMLSQueryConnector;
                case DataProviders.Spark:
                    return SparkQueryConnector;
                case DataProviders.Paragon:
                    return ParagonQueryConnector;
                case DataProviders.Perchwell:
                    return PerchwellQueryConnector;
                case DataProviders.OpenMLS:
                    return OpenMLSQueryConnector;
                default:
                    return null;
            }
        }
    }
}
