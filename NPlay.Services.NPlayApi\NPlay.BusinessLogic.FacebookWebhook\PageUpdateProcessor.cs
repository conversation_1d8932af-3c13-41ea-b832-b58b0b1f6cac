﻿using Newtonsoft.Json;
using NPlay.BusinessLogic.Ads.Abstract;
using NPlay.BusinessLogic.Authentication.Abstract;
using NPlay.BusinessLogic.LoginRegistration.Abstract;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.Abstract.Facebook;
using NPlay.Common.BaseRepository;
using NPlay.Common.Enums;
using NPlay.Common.FacebookModels;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Models;
using NPlay.Common.Models.Facebook;
using NPlay.Common.Models.Services;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.Common.Web.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace NPlay.BusinessLogic.FacebookWebhook
{
    public class PageUpdateProcessor : IFacebookWebhookPageUpdateProcessor
    {
        private readonly IRepository<Agent> AgentRepository;
        private readonly IFacebookAgent FacebookAgent;
        private readonly IUnitOfWork UnitOfWork;
        private readonly IFacebookAdsService FacebookAdsService;
        private readonly ILogger Logger;
        private readonly IRepository<Common.Models.FacebookPage> FacebookPageRepository;
        private readonly IBuyerRegistrationHandler BuyerRegistrationHandler;
        private readonly IBuyerAuthHandler BuyerAuthHandler;
        private readonly IEventBroker EventBroker;
        private readonly IAgentMatchService AgentMatchService;
        private readonly IRepository<UnprocessedLeadAdsLeadgen> UnprocessedLeadgenRepository;
        private readonly IFacebookAdsClient FacebookAdsClient;
        private readonly IListingSearchHistoryClient ListingSearchHistoryClient;
        private readonly IListingTagClient ListingTagClient;
        private readonly IListingClient ListingClient;

        public PageUpdateProcessor(IRepository<Agent> agentRepository,
            IFacebookAgent facebookAgent,
            IUnitOfWorkFactory uowFactory,
            IFacebookAdsService facebookAdsService,
            ILogger logger,
            IRepository<Common.Models.FacebookPage> facebookPageRepository,
            IBuyerRegistrationHandler buyerRegistrationHandler,
            IBuyerAuthHandler buyerAuthHandler,
            IEventBroker eventBroker,
            IAgentMatchService agentMatchService,
            IRepository<UnprocessedLeadAdsLeadgen> unprocessedLeadgenRepository,
            IFacebookAdsClient facebookAdsClient,
            IListingSearchHistoryClient listingSearchHistoryClient,
            IListingTagClient listingTagClient,
            IListingClient listingClient)
        {
            AgentRepository = agentRepository;
            FacebookAgent = facebookAgent;
            FacebookAdsService = facebookAdsService;
            Logger = logger;
            FacebookPageRepository = facebookPageRepository;
            BuyerRegistrationHandler = buyerRegistrationHandler;
            BuyerAuthHandler = buyerAuthHandler;
            EventBroker = eventBroker;
            AgentMatchService = agentMatchService;
            UnprocessedLeadgenRepository = unprocessedLeadgenRepository;
            FacebookAdsClient = facebookAdsClient;
            ListingSearchHistoryClient = listingSearchHistoryClient;
            UnitOfWork = uowFactory.Create("Agent");
            ListingTagClient = listingTagClient;
            ListingClient = listingClient;
        }

        public void ProcessPageUpdates(ChangeNotification notification)
        {
            foreach (var entry in notification.entry)
            {
                if (entry.changes.Length > 0)
                {
                    foreach (var change in entry.changes)
                    {
                        var dictionary = change as Dictionary<string, object>;
                        if (dictionary != null)
                        {
                            if (dictionary.ContainsKey("field") && dictionary["field"].ToString().Equals("leadgen"))
                            {
                                if (dictionary.ContainsKey("value"))
                                {
                                    var changeDictionary = dictionary["value"] as Dictionary<string, object>;
                                    if (changeDictionary != null)
                                        ProcessLeadAdsLead(changeDictionary);
                                }
                            }
                        }
                    }
                }
            }
        }

        #region Private Methods

        private void ProcessLeadAdsLead(Dictionary<string, object> changeDictionary)
        {
            const string methodName = "ProcessLeadAdsLead";
            var json = JsonConvert.SerializeObject(changeDictionary);
            var leadgen = JsonConvert.DeserializeObject<Leadgen>(json);

            if (leadgen.LeadgenId != 0 && leadgen.PageId != 0)
            {
                int? agentIdForUnprocessed = null;
                var errorMessageForUnprocessed = string.Empty;

                var page = FacebookPageRepository.GetAll().SingleOrDefault(p => p.Id == leadgen.PageId.ToString());
                if (page != null)
                {
                    agentIdForUnprocessed = page.UserId;

                    var agent = AgentRepository.GetById(page.UserId);
                    if (agent != null)
                    {
                        var services = agent.GetProductServiceAbbreviations(false);
                        var hasIDXAdPlan = false;
                        if (services.Contains(ServiceType.IDX.GetAbbreviation()))
                        {
                            var fundingSources = agent.Membership?.FundingSources;
                            if (fundingSources != null) {
                                hasIDXAdPlan = fundingSources.Any(fs =>
                                    fs.ProductTypeId == (byte)ProductType.IDX &&
                                    (
                                        fs.ProductPlanId == 277 || //Temporarily hardcoding IDX Ad Plan Id. Remove this after adding logic to get leads generated from our facebook business
                                        (fs.ProductPlan != null && fs.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalIncluded) // IDX Afordal plan users should receive leadgen leads
                                    ) &&
                                    fs.Active == true
                                );
                            }
                        }
                        if (services.Contains(ServiceType.PageManagement.GetAbbreviation()) || services.Contains(ServiceType.Ads.GetAbbreviation()) || services.Contains(ServiceType.ListingLeadAds.GetAbbreviation()) || services.Contains(ServiceType.HomeValueAds.GetAbbreviation()) || hasIDXAdPlan)
                        {
                            if (!string.IsNullOrWhiteSpace(page.AccessToken))
                            {
                                UserLeadGenInfo userLeadGenInfo = null;
                                try
                                {
                                    userLeadGenInfo = FacebookAdsService.GetUserLeadGenInfo(leadgen.LeadgenId, page.AccessToken, page.Id);
                                }
                                catch (FacebookException fbExc)
                                {
                                    errorMessageForUnprocessed = string.Format("{0}. Error code: {1}, message: {2}", fbExc.ExceptionType.ToString(), fbExc.ErrorCode, fbExc.ErrorMessage);
                                    Logger.Error(string.Format("Failed to retrieve UserLeadGenInfo for leadgenId {0} with {1}", leadgen.LeadgenId, errorMessageForUnprocessed),
                                        new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id) }, fbExc, this, methodName);

                                    //Try to use user page access token since business manager access token often fails because of facebook bug
                                    var accountRetrievalError = false;
                                    List<FacebookAccount> accounts = null;
                                    try
                                    {
                                        accounts = FacebookAgent.GetAccounts(agent.FacebookId, agent.AccessToken);
                                    }
                                    catch (Exception ex)
                                    {
                                        accountRetrievalError = true;
                                        LogAccountsRetrievalError(agent.Id, ex);
                                    }

                                    if (accounts != null)
                                    {
                                        var account = accounts.SingleOrDefault(p => p.Id == page.Id);
                                        if (account != null)
                                        {
                                            try
                                            {
                                                //use user page access token
                                                userLeadGenInfo = FacebookAdsService.GetUserLeadGenInfo(leadgen.LeadgenId, account.AccessToken, account.Id);
                                            }
                                            catch (FacebookException fbEx)
                                            {
                                                errorMessageForUnprocessed = string.Format("{0}. Error code: {1}, message: {2}", fbEx.ExceptionType.ToString(), fbEx.ErrorCode, fbEx.ErrorMessage);
                                                Logger.Error(string.Format("Failed to retrieve UserLeadGenInfo for leadgenId {0} using user page access token. {1}", leadgen.LeadgenId, errorMessageForUnprocessed),
                                                    new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id) }, fbEx, this, methodName);
                                            }
                                            catch (Exception excep)
                                            {
                                                errorMessageForUnprocessed = string.Format("Message: {0}", excep.Message);
                                                Logger.Error(string.Format("Failed to retrieve UserLeadGenInfo for leadgenId {0} using user page access token. {1}", leadgen.LeadgenId, errorMessageForUnprocessed),
                                                    new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id) }, excep, this, methodName);
                                            }
                                        }
                                        else
                                            LogAccountNotFoundError(page.Id, agent.Id);
                                    }
                                    else if (!accountRetrievalError)
                                        LogAccountsNullError(agent.Id);
                                }
                                catch (Exception exc)
                                {
                                    errorMessageForUnprocessed = string.Format("Message: {0}", exc.Message);
                                    Logger.Error(string.Format("Failed to retrieve UserLeadGenInfo for leadgenId {0}. {1}", leadgen.LeadgenId, errorMessageForUnprocessed),
                                        new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id) }, exc, this, methodName);
                                }

                                if (userLeadGenInfo != null)
                                {
                                    var createRequest = new CreateLeadAdsLeadActivityRequest
                                    {
                                        Agent = agent,
                                        UserLeadGenInfo = userLeadGenInfo,
                                        Leadgen = leadgen,
                                        Json = json,
                                        AccessToken = page.AccessToken,
                                        PageId =page.Id
                                    };
                                    var buyerId = CreateLeadAdsLeadActivity(createRequest);
                                    var retailerItemId = userLeadGenInfo.RetailerItemId;

                                    Logger.Info("Getting details from retaileritemid to create ListingSearchHistory", new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.BuyerId, buyerId.Value) }, this, methodName);

                                    BasicListingModel listing = null;
                                    try
                                    {
                                        listing = ListingClient.GetListingsByMlsListingId(retailerItemId).Results.FirstOrDefault();
                                    }
                                    catch(Exception ex)
                                    {
                                        Logger.Error($"Could not get listing details from mlsid {retailerItemId}",
                                        new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.BuyerId, buyerId.Value), new LoggingPair(LoggingKey.FacebookAdId, leadgen.AdId) }, ex, this, methodName);
                                    }
                                    double latitude = 0;
                                    double longitude = 0;

                                    if (listing != null)
                                    {
                                        latitude = listing.Location.Lat;
                                        longitude = listing.Location.Lon;
                                    }
                                    try
                                    {
                                        ListingSearchHistoryClient.SaveSystem(new ListingSearchHistoryViewModel()
                                        {
                                            AgentId = agent.Id,
                                            BuyerId = buyerId.Value,
                                            LastSearchDate = DateTime.UtcNow,
                                            Latitude = latitude,
                                            Longitude = longitude,
                                            Distance = "30",
                                        });
                                        Logger.Info("Saving to ListingSearchHistory", new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.BuyerId, buyerId.Value) }, this, methodName);
                                    }
                                    catch (Exception ex)
                                    {
                                        Logger.Error(string.Format("{0} LeadgenId: {1}", errorMessageForUnprocessed, leadgen.LeadgenId),
                                        new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.BuyerId, buyerId.Value) }, ex, this, methodName);
                                    }

                                    Logger.Info("Getting details from retaileritemid to create ListingTag", new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.BuyerId, buyerId.Value) }, this, methodName);
                                    try
                                    {
                                        ListingTagModel tagModel = new ListingTagModel()
                                        {
                                            AgentId = agent.Id,
                                            BuyerId = buyerId.Value,
                                            Latitude = latitude,
                                            Longitude = longitude,
                                            Tags = new List<string>() { "favorite" },
                                            ListingId = listing.Id,
                                        };
                                        var tagModelList = new List<ListingTagModel>() { tagModel };
                                        ListingTagClient.UpdateListingTag(agent.Id, tagModelList, buyerId.Value);
                                        Logger.Info("Saving to ListingTag", new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.BuyerId, buyerId.Value) }, this, methodName);
                                    }
                                    catch(Exception ex )
                                    {
                                        Logger.Error($"Error saving to tagged Listing", 
                                        new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.BuyerId, buyerId.Value) }, ex, this, methodName);
                                    }
                                }
                                else if (string.IsNullOrWhiteSpace(errorMessageForUnprocessed))
                                {
                                    errorMessageForUnprocessed = "UserLeadGenInfo is null.";
                                    Logger.Error(string.Format("{0} LeadgenId: {1}", errorMessageForUnprocessed, leadgen.LeadgenId),
                                        new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id) }, this, methodName);
                                }
                            }
                            else
                            {
                                errorMessageForUnprocessed = "Page accesstoken is null or empty.";
                                Logger.Error(string.Format("{0} LeadgenId: {1}, PageId: {2}", errorMessageForUnprocessed, leadgen.LeadgenId, leadgen.PageId),
                                    new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id) }, this, methodName);
                            }
                        }
                        else
                            Logger.Info("Not creating lead ads lead acitivity since agent doesn't have TKS / Ads / Listing Lead Ads / Home Value Ads.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, agent.Id) }, this, methodName);
                    }
                }
                else
                {
                    errorMessageForUnprocessed = "Page doesn't exist in FaceBookPages table.";
                    Logger.Error(string.Format("{0} LeadgenId: {1}, PageId: {2}", errorMessageForUnprocessed, leadgen.LeadgenId, leadgen.PageId), null, this, methodName);
                }

                if (!string.IsNullOrWhiteSpace(errorMessageForUnprocessed))
                {
                    var unprocessedLeadgen = new UnprocessedLeadAdsLeadgen
                    {
                        LeadgenId = leadgen.LeadgenId,
                        AgentId = agentIdForUnprocessed,
                        PageId = leadgen.PageId.ToString(),
                        JsonText = json,
                        ErrorMessage = errorMessageForUnprocessed,
                        DateInserted = DateTime.UtcNow
                    };
                    InsertUnprocessedLeadgen(unprocessedLeadgen);
                }
            }
            else
                Logger.Error(string.Format("LeadgenId or PageId is 0. LeadgenId: {0}, PageId: {1}", leadgen.LeadgenId, leadgen.PageId), null, this, methodName);
        }

        private void InsertUnprocessedLeadgen(UnprocessedLeadAdsLeadgen leadgen)
        {
            var existingLeadgen = UnprocessedLeadgenRepository.GetAll().SingleOrDefault(l => l.LeadgenId == leadgen.LeadgenId);
            if (existingLeadgen == null)
            {
                UnprocessedLeadgenRepository.Add(leadgen);
                UnitOfWork.Commit();
            }
            else
                Logger.Info(string.Format("Did not save UnprocessedLeadAdsLeadgen becaues it already exists. LeadgenId: {0}, PageId: {1}", leadgen.LeadgenId, leadgen.PageId), null, this, "InsertUnprocessedLeadgen");
        }

        private int? CreateLeadAdsLeadActivity(CreateLeadAdsLeadActivityRequest createRequest)
        {
            const string methodName = "CreateLeadAdsLeadActivity";
            var agent = createRequest.Agent;
            var userLeadGenInfo = createRequest.UserLeadGenInfo;
            var leadgen = createRequest.Leadgen;
            int? buyerId = null;

            var fieldDataList = userLeadGenInfo.FieldData;
            if (fieldDataList != null && fieldDataList.Count > 0)
            {
                var request = GetRegistrationRequest(fieldDataList);

                BuyerAutoRegisterResult result = null;
                try
                {
                    result = BuyerRegistrationHandler.AutoRegisterForThirdPartyForm(request);
                }
                catch (ArgumentException ex)
                {
                    var errorMessage = string.Format("Neither email nor phone is valid. LeadgenId: {0}, Email: {1}, PhoneNumber: {2}, FirstName: {3}, LastName: {4}",
                        userLeadGenInfo.Id, request.Email, request.PhoneNumber, request.FirstName, request.LastName);
                    Logger.Error(errorMessage, new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, agent.Id) }, ex, methodName);

                    var unprocessedLeadgen = new UnprocessedLeadAdsLeadgen
                    {
                        LeadgenId = leadgen.LeadgenId,
                        AgentId = agent.Id,
                        PageId = leadgen.PageId.ToString(),
                        JsonText = createRequest.Json,
                        ErrorMessage = errorMessage,
                        DateInserted = DateTime.UtcNow
                    };
                    InsertUnprocessedLeadgen(unprocessedLeadgen);
                }

                if (result != null)
                {
                    buyerId = result.BuyerId;
                    var accessToken = result.AccessToken;

                    if (buyerId == null && !string.IsNullOrWhiteSpace(accessToken))
                    {
                        var getBuyerIdRequest = new GetBuyerIdFromAccessTokenRequest
                        {
                            AccessToken = accessToken,
                            FromLeadAds = true,
                            SendWelcomeEmailIfNew = true,
                            AgentId = agent.Id
                        };
                        buyerId = BuyerRegistrationHandler.GetBuyerIdFromAccessToken(getBuyerIdRequest);
                    }

                    if (buyerId != null && buyerId != 0)
                    {
                        LeadgenForm form = null;
                        try
                        {
                            form = FacebookAdsService.GetLeadgenForm(leadgen.FormId, createRequest.AccessToken, createRequest.PageId);
                        }
                        catch (FacebookException fbExc)
                        {
                            var errorMessage = string.Format("{0}. Error code: {1}, message: {2}", fbExc.ExceptionType.ToString(), fbExc.ErrorCode, fbExc.ErrorMessage);
                            Logger.Error(string.Format("Failed to retrieve LeadgenForm for leadgenId {0} with {1}", leadgen.LeadgenId, errorMessage), null, this, methodName);
                        }
                        catch (Exception exc)
                        {
                            var errorMessage = string.Format("Message: {0}", exc.Message);
                            Logger.Error(string.Format("Failed to retrieve LeadgenForm for leadgenId {0}. {1}", leadgen.LeadgenId, errorMessage), null, this, methodName);
                        }

                        var note = GetLeadAdsLeadActivityNote(fieldDataList);
                        var leadActivity = new LeadAdsLeadActivity
                        {
                            AgentId = agent.Id,
                            BuyerId = buyerId.Value,
                            AdId = leadgen.AdId ?? 0,
                            AdGroupId = leadgen.AdGroupId ?? 0,
                            Active = true,
                            Email = request.Email,
                            Phone = request.PhoneNumber,
                            FormId = leadgen.FormId,
                            Id = userLeadGenInfo.Id.ToString(),
                            Name = string.Format("{0} {1}", request.FirstName, request.LastName),
                            PageId = leadgen.PageId,
                            Note = note,
                            TimeStamp = DateTime.UtcNow,
                            LeadActivityType = LeadActivityType.LeadAdsLeadActivity,
                            RetailerItemId = userLeadGenInfo.RetailerItemId,
                            FormName = form != null ? form.Name : null,
                            Platform = userLeadGenInfo.Platform,
                            Address = userLeadGenInfo.HomeListing?.address
                        };
                        var leadActivityJson = JsonConvert.SerializeObject(leadActivity);
                        Logger.Info($"LeadActivity JSON: {leadActivityJson}");
                        Logger.Info($"Address for listing is {userLeadGenInfo.HomeListing?.address}");
                        var eve = new LeadAdsLeadActivityEvent { Payload = leadActivity, TrackingId = Logger.TrackingGuid.Value };
                        EventBroker.Publish<LeadAdsLeadActivityEvent, LeadAdsLeadActivity>(eve);

                       

                       
                    }
                    else
                    {
                        Logger.Info(string.Format("Failed to retrive / create Buyer. LeadgenId: {0}, PageId: {1}", leadgen.LeadgenId, leadgen.PageId),
                            new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, agent.Id) }, this, methodName);
                    }
                }
            }
            else
                Logger.Error(string.Format("userLeadGenInfo.FieldData is empty. LeadgenId: {0}", userLeadGenInfo.Id), null, this, methodName);

            return buyerId;
        }

        private RegistrationRequest GetRegistrationRequest(List<NameValues> fieldDataList)
        {
            var email = string.Empty;
            var firstName = string.Empty;
            var lastName = string.Empty;
            var phone = string.Empty;

            var emailPair = fieldDataList.SingleOrDefault(f => f.Name == "email");
            if (emailPair != null)
                email = emailPair.Values[0];

            var firstNamePair = fieldDataList.SingleOrDefault(f => f.Name == "first_name");
            if (firstNamePair != null)
                firstName = firstNamePair.Values[0];

            var lastNamePair = fieldDataList.SingleOrDefault(f => f.Name == "last_name");
            if (lastNamePair != null)
                lastName = lastNamePair.Values[0];

            var phonePair = fieldDataList.SingleOrDefault(f => f.Name == "phone_number");
            if (phonePair != null)
                phone = phonePair.Values[0];

            if (string.IsNullOrWhiteSpace(firstName) && string.IsNullOrWhiteSpace(lastName))
            {
                var fullNamePair = fieldDataList.SingleOrDefault(f => f.Name == "full_name" || f.Name == "full name");
                if (fullNamePair != null)
                {
                    var fullName = fullNamePair.Values[0];
                    if (!string.IsNullOrWhiteSpace(fullName))
                    {
                        var arr = fullName.Split(' ');
                        if (arr != null)
                        {
                            firstName = arr[0];
                            if (arr.Length > 1)
                            {
                                for (int i = 1; i < arr.Length; i++)
                                    lastName += arr[i] + " ";
                                lastName = lastName.Trim();
                            }
                        }
                    }
                }
            }

            var request = new RegistrationRequest
            {
                Email = email,
                FirstName = firstName,
                LastName = lastName,
                PhoneNumber = phone
            };

            return request;
        }

        private string GetLeadAdsLeadActivityNote(List<NameValues> fieldDataList)
        {
            var sb = new StringBuilder();
            for (int i = 0; i < fieldDataList.Count; i++)
            {
                var fieldData = fieldDataList[i];
                sb.Append(fieldData.Name.Replace('_', ' '));
                sb.Append(": ");
                sb.AppendLine(fieldData.Values[0]);
            }
            return sb.ToString();
        }

        private void LogAccountsRetrievalError(int agentId, Exception ex)
        {
            const string methodName = "LogAccountsRetrievalError";
            Logger.Error(string.Format("An error occurred while retrieving accounts. Message: {0}. StackTrace: {1}", ex.Message, ex.StackTrace),
                new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agentId) }, this, methodName);
        }

        private void LogAccountsNullError(int agentId)
        {
            const string methodName = "LogAccountsNullError";
            Logger.Error("FacebookOAuthException was thrown while retrieving accounts.", new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agentId) }, this, methodName);
        }

        private void LogAccountNotFoundError(string pageId, int agentId)
        {
            const string methodName = "LogAccoutNotFoundError";
            Logger.Error(string.Format("Tried to get user page access token but account not returned. Account id: {0}", pageId),
                new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agentId) }, this, methodName);
        }

        #endregion
    }

    public class CreateLeadAdsLeadActivityRequest
    {
        public Agent Agent { get; set; }
        public UserLeadGenInfo UserLeadGenInfo { get; set; }
        public Leadgen Leadgen { get; set; }
        public string Json { get; set; }
        public string AccessToken { get; set; }
        public string PageId { get; set; }
    }
}
