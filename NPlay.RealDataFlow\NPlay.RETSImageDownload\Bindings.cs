﻿using Ninject.Modules;
using Nplay.Services.Data;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Logging;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Models.MLS;
using NPlay.Common.Queues;
using NPlay.Common.Repository;
using NPlay.Common.Search;
using NPlay.Common.ServiceAgents.Messaging;
using NPlay.Common.ServiceAgents.RETSConnections;
using NPlay.Common.Services.Abstract;
using NPlay.ReadDataFlow.Data;
using NPlay.RealDataFlow.Mapping.Abstract;
using NPlay.RealDataFlow.Mapping.Models.Maps;
using NPlay.RealDataFlow.RETSConnector;
using NPlay.RealDataFlow.RETSConnector.Abstract;
using RETS.Parsing;
using RETS.Parsing.Abstract;
using RETS.Web;
using RETS.Web.Abstract;
using RETSDataSynchronization.Images;
using RETSDataSynchronization.RETSData;
using NPlay.Common.ServiceAgents.RETSMetadata;
using MLS.Synchronization.Abstract;
using MLS.Synchronization.Models;
using RESO.Synchronization;
using RESO.Connector.Abstract;
using RESO.Connector;
using MLS.Synchronization.Images;
using MLS.Synchronization;
using NPlay.Common.Messaging.Contextual;
using NPlay.RealDataFlow.Mapping;
using MLS.Synchronization.Listings;
using Ninject.Extensions.NamedScope;
using Ninject;

namespace NPlay.ImageDownloadService
{
    public class Bindings : NinjectModule
    {
        public override void Load()
        {
            // Setting context
            Bind<IDBFactory<SettingsContext>>().To<DBFactory<SettingsContext>>();
            Bind<IRepository<Setting>>().To<EntityRepositoryBase<Setting, SettingsContext>>();

            // Map Context
            Bind<IDBFactory<MapContext>>().To<DBFactory<MapContext>>();
            Bind<IRepository<Map>>().To<EntityRepositoryBase<Map, MapContext>>();
            Bind<IRepository<MapField>>().To<EntityRepositoryBase<MapField, MapContext>>();
            Bind<IRepository<MapNote>>().To<EntityRepositoryBase<MapNote, MapContext>>();
            Bind<IRepository<MapMatch>>().To<EntityRepositoryBase<MapMatch, MapContext>>();
            Bind<IRepository<MapPropertyType>>().To<EntityRepositoryBase<MapPropertyType, MapContext>>();
            Bind<IRepository<Related>>().To<EntityRepositoryBase<Related, MapContext>>();
            Bind<IRepository<MappingListingStatus>>().To<EntityRepositoryBase<MappingListingStatus, MapContext>>();

            // Mls Context
            Bind<IDBFactory<MlsContext>>().To<DBFactory<MlsContext>>();
            Bind<IRepository<MultipleListingService>>().To<EntityRepositoryBase<MultipleListingService, MlsContext>>();
            Bind<IRepository<RETSCredential>>().To<EntityRepositoryBase<RETSCredential, MlsContext>>();
            Bind<IRepository<RESOCredential>>().To<EntityRepositoryBase<RESOCredential, MlsContext>>();
            Bind<IRepository<MLSParameter>>().To<EntityRepositoryBase<MLSParameter, MlsContext>>();
            Bind<IRepository<DataFeed>>().To<EntityRepositoryBase<DataFeed, MlsContext>>();
            Bind<IRepository<MLSMaster>>().To<EntityRepositoryBase<MLSMaster, MlsContext>>();
            Bind<IMLSRepository>().To<MLSRepository>();
            Bind<ICachedMLSRepository>().To<MLSCache>().InSingletonScope();
            Bind<IRepository<MLSActiveStatus>>().To<EntityRepositoryBase<MLSActiveStatus, MlsContext>>();
            Bind<IListingStatusProvider>().To<ListingStatusProvider>();

            // Event broker
            Bind<IEventBroker>().To<PersistentEventBroker>().InSingletonScope();
            Bind<IEventPrismFactory>().To<RabbitEventPrismFactory>().InSingletonScope();
            Bind<IQueueFactory>().To<RabbitQueueFactory>().InSingletonScope();

            // NAMED SCOPE CONFIGURATIONS
            // All instances of ContextualEventBroker and ProcessingContext will be a shared single instance
            //   when injected into anything that is underneath the root processing objects (RETSImageDownloader and RESOImageDownloader).
            // All other instances use the default binding context as defined in "WhenNoAncestorMatches" bindings.
            // In other words, when an instance is created of either of the root processing objects, a single
            //   instance of all the below objects is created and shared through the entire object graph.
            // The purpose of this is to ensure that a single logger/processing context is persisted throughout the
            //   entire lifetime of the message processing context.  This is different than ThreadScope or SingletonScope.
            // See Ninject documentation for details about NamedScope.
            string PROCESSING_CONTEXT_NAME = "ProcessingContext";
            Bind<IRETSImageDownloader>().To<RETSImageDownloader>().DefinesNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IRESOImageDownloader>().To<RESOImageDownloader>().DefinesNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IContextualEventBroker>().To<ContextualEventBroker>().WhenAnyAncestorMatches(c =>
                {
                    return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
                })
                .InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IContextualEventBroker>().To<ContextualEventBroker>().WhenNoAncestorMatches(c =>
                {
                    return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
                })
                .InSingletonScope();
            Bind<IProcessingContext>().To<ProcessingContext>().WhenAnyAncestorMatches(c =>
                {
                    return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
                })
                .InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IProcessingContext>().To<ProcessingContext>().WhenNoAncestorMatches(c =>
                {
                    return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
                })
                .InTransientScope();
            Bind<ILogger>().To<Log4NetLogger>().WhenAnyAncestorMatches(c =>
                {
                    return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
                })
                .InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<ILogger>().To<Log4NetLogger>().WhenNoAncestorMatches(c =>
                {
                    return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
                })
                .InTransientScope();
            Bind<IRETSImagesConnector>().To<RETSImagesConnector>().InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IListingImageLazyLoadManager>().To<ListingImageLazyLoadManager>().InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IImageStreamDownloader>().To<ImageStreamDownloader>().InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<ICanonImageTranslator>().To<CanonImageTranslator>().InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IImageUploader>().To<CIFSImageUploader>().InNamedScope(PROCESSING_CONTEXT_NAME);

            // MLS Synchronization Context
            Bind<IMlsSynchronizationContextProvider>().ToMethod(p =>
            {
                MlsSynchronizationContextProvider.Instance.MLSCache = MlsSynchronizationContextProvider.Instance.MLSCache ?? this.Kernel.Get<ICachedMLSRepository>();
                return MlsSynchronizationContextProvider.Instance;
            }).InSingletonScope();

            // Status Cacher
            Bind<IListingStatusCacher>().To<ListingStatusCacher>();

            // RESO downloads
            Bind<IMlsGridMediaConnector>().To<MlsGridMediaConnector>();
            Bind<IBridgeMediaConnector>().To<BridgeMediaConnector>();
            Bind<ITrestleMediaConnector>().To<TrestleMediaConnector>();
            Bind<IRapattoniMediaConnector>().To<RapattoniMediaConnector>();
            Bind<IUtahRealEstateMediaConnector>().To<UtahRealEstateMediaConnector>();
            Bind<IRMLSMediaConnector>().To<RMLSMediaConnector>();
            Bind<ISparkMediaConnector>().To<SparkMediaConnector>();
            Bind<IParagonMediaConnector>().To<ParagonMediaConnector>();
            Bind<IPerchwellMediaConnector>().To<PerchwellMediaConnector>();
            Bind<IOpenMLSMediaConnector>().To<OpenMLSMediaConnector>();

            // RETS downloads
            Bind<IRETSParser>().To<RETSParser>();
            Bind<IRETSWebClientFactory>().To<RETSWebClientFactory>();
            Bind<IRETSConnectionManagerClient>().To<RETSConnectionManagerClient>();
            Bind<IRETSMetadataManagerClient>().To<RETSMetadataManagerClient>();

            // FTP downloads
            Bind<IFTPConnector>().To<FTPConnector>();

            // Search
            Bind<ISearchRepository>().To<SearchRepository>();

            // Delayed event client
            Bind<IDelayedEventClient>().To<DelayedEventClient>();
        }
    }
}
