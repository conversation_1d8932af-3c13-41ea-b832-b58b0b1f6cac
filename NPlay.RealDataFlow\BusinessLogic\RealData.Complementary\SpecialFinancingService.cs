﻿using MLS.Synchronization.Abstract;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.Messaging.Contextual;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Messaging.Exceptions;
using NPlay.Common.Messaging.Payloads;
using NPlay.Common.Models;
using NPlay.Common.ServiceAgents.ListingReprocessor;
using NPlay.Common.Services.Abstract;
using System;
using System.CodeDom;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RealData.Complementary
{
    public class SpecialFinancingService : ISpecialFinancingService
    {
        private static string SpecialFinancingTagCategory = "SpecialFinancing";
        private static string RatePlugTagCategory = "RatePlug";
        IActiveListingRepository ActiveListingRepository;
        ISearchRepository SearchRepository;
        IRatePlugClient RatePlugClient;
        IContextualEventBroker ContextualEventBroker;

        public ILogger Logger { get; protected set; }
        public IProcessingContext ProcessingContext { get; protected set; }

        public SpecialFinancingService(IActiveListingRepository activeListingRepository,
                                       ISearchRepository searchRepository,
                                       IRatePlugClient ratePlugClient,
                                       IContextualEventBroker contextualEventBroker,
                                       ILogger logger,
                                       IProcessingContext processingContext)
        {
            ActiveListingRepository = activeListingRepository;
            SearchRepository = searchRepository;
            RatePlugClient = ratePlugClient;
            ContextualEventBroker = contextualEventBroker;
            Logger = logger;
            ProcessingContext = processingContext;
        }

        public async Task ProcessAsync<T>(T payload)
        {
            string methodName = "ProcessAsync";

            if (!(payload is SpecialFinancePayload))
                throw new NotImplementedException();

            var specialFinancePayload = payload as SpecialFinancePayload;

            var existingListing = await ActiveListingRepository.GetListingByIdAsync(specialFinancePayload.MlsId, specialFinancePayload.InternalId);
            if (existingListing == null)
            {
                Logger.Warning("Unable to get listing with this id for this MLS.", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, specialFinancePayload.MlsId), new LoggingPair(LoggingKey.IndexId, specialFinancePayload.InternalId) }, this, methodName);
                return;
            }

            var listing = MLSPropertyListing.CloneForUpsert(existingListing);
            listing.Tags = existingListing.Tags;

            // Look up the special finance data to see if we already have it in the special finance index
            var specialFinancingResult = await SearchRepository.GetByIdAsync<SpecialFinancingResponse>(SearchIndex.SpecialFinancing, listing.Id);

            string specialFinanceProductKey;
            string specialFinanceProductTag;
            NPlay.Common.Models.Listing.Tag existingTag;
            if (specialFinancePayload is AddressNormalizationPayload)
            {
                if (specialFinancePayload.ForceUpdate ||
                    specialFinancingResult == null ||
                    String.IsNullOrWhiteSpace(specialFinancingResult.StreetAddressCompleteStandard))
                {
                    specialFinancingResult = await RatePlugClient.GetNormalizedAddress(existingListing);
                    if (!String.IsNullOrWhiteSpace(specialFinancingResult?.StreetAddressCompleteStandard))
                    {
                        await SearchRepository.SavePartialAsync<SpecialFinancingResponse>(specialFinancingResult.Id, specialFinancingResult, SearchIndex.SpecialFinancing);
                    }
                    else
                    {
                        Logger.Warning("Unable to get a normalized address", new LoggingPair[]
                                                                                {
                                                                                    new LoggingPair(LoggingKey.MlsId, existingListing.MLSSource),
                                                                                    new LoggingPair(LoggingKey.IndexId, existingListing.Id)
                                                                                },
                                                                                this,
                                                                                "ProcessAsync");
                    }
                }

                if (!String.IsNullOrWhiteSpace(specialFinancingResult?.StreetAddressCompleteStandard))
                {
                    listing.Address.StreetAddressNormalized = specialFinancingResult.StreetAddressCompleteStandard;

                    await ActiveListingRepository.UpdateListingAsync(listing, listing.MLSSource);

                    // Wait for elastic
                    if (!(specialFinancePayload as AddressNormalizationPayload).NormalizeAddressOnly)
                    {
                        await Task.Delay(1000);
                    }
                }

                // This is to allow bulk processing where we only do address normalization without kicking off further special finance events
                if ((specialFinancePayload as AddressNormalizationPayload).NormalizeAddressOnly) return;

                ContextualEventBroker.Publish<USDADataEvent, USDAPayload>(new USDADataEvent()
                {
                    Payload = new USDAPayload()
                    {
                        InternalId = specialFinancePayload.InternalId,
                        MlsId = specialFinancePayload.MlsId,
                        PropertyListingID = specialFinancePayload.PropertyListingID
                    }
                });

                ContextualEventBroker.Publish<AssumableMortgageDataEvent, AssumableMortgagePayload>(new AssumableMortgageDataEvent()
                {
                    Payload = new AssumableMortgagePayload()
                    {
                        InternalId = specialFinancePayload.InternalId,
                        MlsId = specialFinancePayload.MlsId,
                        PropertyListingID = specialFinancePayload.PropertyListingID
                    }
                });

                ContextualEventBroker.Publish<FHACondoDataEvent, FHACondoPayload>(new FHACondoDataEvent()
                {
                    Payload = new FHACondoPayload()
                    {
                        InternalId = specialFinancePayload.InternalId,
                        MlsId = specialFinancePayload.MlsId,
                        PropertyListingID = specialFinancePayload.PropertyListingID
                    }
                });
            }
            else if (specialFinancePayload is USDAPayload)
            {
                if (specialFinancePayload.ForceUpdate ||
                    specialFinancingResult == null || 
                    specialFinancingResult.USDAEligible == null)
                {
                    specialFinancingResult = await RatePlugClient.GetUSDAEligibility(existingListing);
                    await SearchRepository.SavePartialAsync<SpecialFinancingResponse>(specialFinancingResult.Id, specialFinancingResult, SearchIndex.SpecialFinancing);
                }

                specialFinanceProductKey = $"{SpecialFinancingTagCategory}__USDAEligible";
                specialFinanceProductTag = $"{specialFinanceProductKey}__{specialFinancingResult.USDAEligible}";
                existingTag = listing.Tags.FirstOrDefault(t => t.Name.StartsWith(specialFinanceProductKey));
                if (existingTag != null)
                {
                    existingTag.Name = specialFinanceProductTag;
                }
                else if (specialFinancingResult.USDAEligible != null)
                {
                    listing.Tags.Add(
                        new NPlay.Common.Models.Listing.Tag()
                        {
                            Category = SpecialFinancingTagCategory,
                            Name = specialFinanceProductTag
                        });
                }

                await ActiveListingRepository.UpdateListingAsync(listing, listing.MLSSource);
            }
            else if (specialFinancePayload is FHACondoPayload)
            {
                if (specialFinancingResult == null) throw new BadPayloadException($"Listing {specialFinancePayload.InternalId} does not have a special financing record with a normalized address");

                if (specialFinancePayload.ForceUpdate ||
                    specialFinancingResult.FHACondoInfo == null)
                {
                    var result = await RatePlugClient.GetFHACondoEligibility(existingListing);
                    if (result == null) return;

                    specialFinancingResult.FHACondoInfo = new FHACondoInfo(result);
                    await SearchRepository.SavePartialAsync<SpecialFinancingResponse>(specialFinancingResult.Id, specialFinancingResult, SearchIndex.SpecialFinancing);
                }

                specialFinanceProductKey = $"{SpecialFinancingTagCategory}__FHACondoEligible";
                specialFinanceProductTag = $"{specialFinanceProductKey}__{specialFinancingResult.FHACondoInfo.IsFHACondoEligible}";
                existingTag = listing.Tags.FirstOrDefault(t => t.Name.StartsWith(specialFinanceProductKey));
                if (existingTag != null)
                {
                    existingTag.Name = specialFinanceProductTag;
                }
                else if (specialFinancingResult?.FHACondoInfo?.IsFHACondoEligible == true)
                {
                    listing.Tags.Add(
                        new NPlay.Common.Models.Listing.Tag()
                        {
                            Category = SpecialFinancingTagCategory,
                            Name = specialFinanceProductTag
                        });
                }

                specialFinanceProductKey = $"{RatePlugTagCategory}__FC_PropertyID";
                specialFinanceProductTag = $"{specialFinanceProductKey}__{specialFinancingResult.FHACondoInfo.PropertyId}";
                existingTag = listing.Tags.FirstOrDefault(t => t.Name.StartsWith(specialFinanceProductKey));
                if (existingTag != null)
                {
                    existingTag.Name = specialFinanceProductTag;
                }
                else if (specialFinancingResult?.FHACondoInfo?.IsFHACondoEligible == true)
                {
                    listing.Tags.Add(
                        new NPlay.Common.Models.Listing.Tag()
                        {
                            Category = RatePlugTagCategory,
                            Name = specialFinanceProductTag
                        });
                }

                await ActiveListingRepository.UpdateListingAsync(listing, listing.MLSSource);
            }
            else if (specialFinancePayload is AssumableMortgagePayload)
            {
                if (specialFinancingResult == null) throw new BadPayloadException($"Listing {specialFinancePayload.InternalId} does not have a special financing record with a normalized address");

                if (specialFinancePayload.ForceUpdate ||
                    specialFinancingResult.AssumableMortgageInfo == null)
                {
                    var result = await RatePlugClient.GetAssumableEligibility(existingListing);
                    if (result == null) return;

                    specialFinancingResult.AssumableMortgageInfo = new AssumableMortgageInfo(result);
                    await SearchRepository.SavePartialAsync<SpecialFinancingResponse>(specialFinancingResult.Id, specialFinancingResult, SearchIndex.SpecialFinancing);
                }

                specialFinanceProductKey = $"{SpecialFinancingTagCategory}__AssumableEligible";
                specialFinanceProductTag = $"{specialFinanceProductKey}__{specialFinancingResult.AssumableMortgageInfo.IsAssumable}";
                existingTag = listing.Tags.FirstOrDefault(t => t.Name.StartsWith(specialFinanceProductKey));
                if (existingTag != null)
                {
                    existingTag.Name = specialFinanceProductTag;
                }
                else if (specialFinancingResult.AssumableMortgageInfo != null)
                {
                    listing.Tags.Add(
                        new NPlay.Common.Models.Listing.Tag()
                        {
                            Category = SpecialFinancingTagCategory,
                            Name = specialFinanceProductTag
                        });
                }

                specialFinanceProductKey = $"{RatePlugTagCategory}__AS_PropertyID";
                specialFinanceProductTag = $"{specialFinanceProductKey}__{specialFinancingResult.AssumableMortgageInfo.PropertyId}";
                existingTag = listing.Tags.FirstOrDefault(t => t.Name.StartsWith(specialFinanceProductKey));
                if (existingTag != null)
                {
                    existingTag.Name = specialFinanceProductTag;
                }
                else if (specialFinancingResult.AssumableMortgageInfo != null)
                {
                    listing.Tags.Add(
                        new NPlay.Common.Models.Listing.Tag()
                        {
                            Category = RatePlugTagCategory,
                            Name = specialFinanceProductTag
                        });
                }

                specialFinanceProductKey = $"{RatePlugTagCategory}__AssumableMonthlyPayment";
                specialFinanceProductTag = $"{specialFinanceProductKey}__{specialFinancingResult.AssumableMortgageInfo.MonthlyPayment}";
                existingTag = listing.Tags.FirstOrDefault(t => t.Name.StartsWith(specialFinanceProductKey));
                if (existingTag != null)
                {
                    existingTag.Name = specialFinanceProductTag;
                }
                else if (specialFinancingResult.AssumableMortgageInfo != null)
                {
                    listing.Tags.Add(
                        new NPlay.Common.Models.Listing.Tag()
                        {
                            Category = RatePlugTagCategory,
                            Name = specialFinanceProductTag
                        });
                }

                await ActiveListingRepository.UpdateListingAsync(listing, listing.MLSSource);
            }
            else
            {
                throw new NotImplementedException();
            }
        }
    }
}
