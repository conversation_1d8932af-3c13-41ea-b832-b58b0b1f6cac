﻿using MLS.Synchronization.Models;
using NPlay.Common.Abstract;
using System.Collections.Generic;
using System.Linq;
using RESO.Models;
using RESO.Connector.Abstract;
using MLS.Synchronization.Abstract;
using System;

namespace RESO.Connector
{
    public class OpenMLSQueryConnector : QueryConnector, IOpenMLSQueryConnector
    {
        public OpenMLSQueryConnector(IMlsSynchronizationContextProvider mlsSynchronizationContextProvider,
                                     ILogger logger,
                                     IRepository<RESOCredential> resoCredsRepo) : base(mlsSynchronizationContextProvider, resoCredsRepo, logger) { }

        protected override IEnumerable<RESORecord> AccumulateResults(IEnumerable<dynamic> sourceResults, string mlsId)
        {
            return sourceResults.Select(r => new RESORecord()
            {
                Id = GetRecordId(r),
                MlsId = mlsId,
                PropertyListingId = GetPropertyListingId(r, mlsId),
                Record = r,
                CanView = GetVisibility(r)
            });
        }

        protected override string GetRecordId(dynamic listingResult)
        {
            return listingResult.ContainsKey("ListingId") ? $"{Credentials.WebApiUri}/Property({listingResult["ListingId"]})" : null;
        }

        protected override string GetPropertyListingId(dynamic listingResult, string mlsId)
        {
            return listingResult.ContainsKey("ListingId") ? listingResult["ListingId"] : null;
        }

        protected override bool? GetVisibility(dynamic listingResult)
        {
            return true;
        }
    }
}
