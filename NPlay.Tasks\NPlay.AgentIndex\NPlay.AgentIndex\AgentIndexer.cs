﻿using AutoMapper;
using Nplay.Services.Data;
using NPlay.Common.Abstract;
using NPlay.Common.Models;
using NPlay.Common.Models.Services;
using NPlay.Common.Search;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Nest;
using NPlay.Common.Geometry;
using NPlay.Common;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.Common.Models.ExtendedServices;
using NPlay.Common.Enums;

namespace NPlay.AgentIndex
{
    public class AgentIndexer
    {
        private static readonly int BULK_AGENT_SAVE_BATCH_SIZE = 100;

        // Repositories
        IRepository<Agent> AgentRepository;
        ISearchRepository SearchRepository;
        IRepository<ZipCode> ZipCodeRepository;
        IRepository<State> StateRepository;
        IRepository<FacebookPage> FacebookPage;
        IRepository<AgentZip> AgentZip;
        IRepository<AgentAward> AwardRepository;
        IRepository<Credential> CredentialRepository;
        IRepository<AgentEducation> EducationRepository;
        IRepository<FocalArea> ExpertiseRepository;
        IRepository<Common.Models.Language> LanguagesRepository;
        IRepository<Neighborhood> NeighborhoodRepository;
        IRepository<AgentOrganization> OrganizationsRepository;
        IRepository<AgentPublication> PublicationsRepository;
        IRepository<MultipleListingService> MlsRepository;
        IListingClient ListingClient;
        IRepository<NotificationPreference> NotificationPreferenceRepository;
        IRepository<AgentSetting> AgentSettingRepository;
        IRepository<FundingSource> FundingSourceRepository;
        IRepository<AgentEmail> AgentEmailRepository;
        IAgentLocations AgentLocations;
        ILogger Logger;

        // Dictionary lookups
        private IDictionary<int, ZipCode> zipDict;
        private IDictionary<int, State> stateDict;
        private IDictionary<int, Neighborhood> neighborhoodDict;

        private ILookup<int, AgentAward> awardDict;
        private ILookup<int, AgentEducation> educationDict;
        private ILookup<int, AgentOrganization> organizationsDict;
        private ILookup<int, AgentPublication> publicationsDict;

        private Dictionary<int, List<FacebookPageIndexer>> pages;
        private Dictionary<int, List<AgentZip>> agentZipLocations;
        private Dictionary<int, bool> AgentSMSDict;
        private Dictionary<int, bool> AgentHomeValueOnALPDict;
        private Dictionary<int, List<byte>> AgentProductsDict;
        private Dictionary<int, List<AgentEmail>> AgentEmailDict;

        private IDictionary<string, MultipleListingService> mlsDict;

        private IQueryable<MLSOffice> agentOfficeLocations;
        private IMapper Mapper;


        private class FacebookPageIndexer
        {
            public string Id;
            public int UserId;
            public bool HS;
            public bool Profile;
            public bool Directory;
            public bool DSDate;
        }

        public AgentIndexer(IRepository<Agent> agentRepository,
                                    ISearchRepository searchRepository,
                                    IRepository<ZipCode> zipCodeRepository,
                                    IRepository<State> stateRepository,
                                    IRepository<FacebookPage> pageRepository,
                                    IRepository<AgentZip> agentZip,
                                    IRepository<AgentAward> awardRepository,
                                    IRepository<Credential> credentialRepository,
                                    IRepository<AgentEducation> educationRepository,
                                    IRepository<FocalArea> expertiseRepository,
                                    IRepository<Common.Models.Language> languagesRepository,
                                    IRepository<Neighborhood> neighborhoodRepository,
                                    IRepository<AgentOrganization> organizationsRepository,
                                    IRepository<AgentPublication> publicationsRepository,
                                    IRepository<MultipleListingService> mlsRepository,
                                    IListingClient listingClient,
                                    IRepository<NotificationPreference> notificationPreferenceRepository,
                                    IRepository<FundingSource> fundingSourceRepository,
                                    IRepository<AgentSetting> agentSettingRepository,
                                    IRepository<AgentEmail> agentEmailRepository,
                                    IMapper mapper,
                                    ILogger logger,
                                    AgentLocations agentLocations
                                )
        {
            AgentRepository = agentRepository;
            SearchRepository = searchRepository;
            ZipCodeRepository = zipCodeRepository;
            StateRepository = stateRepository;
            FacebookPage = pageRepository;
            AgentZip = agentZip;
            AwardRepository = awardRepository;
            CredentialRepository = credentialRepository;
            EducationRepository = educationRepository;
            ExpertiseRepository = expertiseRepository;
            LanguagesRepository = languagesRepository;
            NeighborhoodRepository = neighborhoodRepository;
            OrganizationsRepository = organizationsRepository;
            PublicationsRepository = publicationsRepository;
            MlsRepository = mlsRepository;
            ListingClient = listingClient;
            NotificationPreferenceRepository = notificationPreferenceRepository;
            AgentSettingRepository = agentSettingRepository;
            AgentEmailRepository = agentEmailRepository;
            Mapper = mapper;
            FundingSourceRepository = fundingSourceRepository;
            AgentLocations = agentLocations;
            Logger = logger;
        }

        public void Index(bool createNew = false, bool noSwap = false)
        {
            var searchRepository = SearchRepository;
            var zipCodeRepository = ZipCodeRepository;
            var stateRepository = StateRepository;
            var pageRepository = FacebookPage;
            var agentZipRepository = AgentZip;
            var awardRepository = AwardRepository;
            var credentialRepository = CredentialRepository;
            var educationRepository = EducationRepository;
            var expertiseRepository = ExpertiseRepository;
            var languagesRepository = LanguagesRepository;
            var neighborhoodRepository = NeighborhoodRepository;
            var organizationsRepository = OrganizationsRepository;
            var publicationsRepository = PublicationsRepository;
            var mlsRepository = MlsRepository;
            var notificationPreferenceRepository = NotificationPreferenceRepository;
            var agentSettingRepository = AgentSettingRepository;
            var fundingSourceRepository = FundingSourceRepository;
            var agentEmailRepository = AgentEmailRepository;

            var runTime = DateTime.Now;
            string postFix = runTime.ToString("_yyyyMMddhhmmss");
            string indexName = SearchIndex.Agents;
            Logger.Info("Createnew: " + createNew, null, this, "Index");

            if (createNew)
            {
                indexName += postFix;
                SearchIndexConfiguration.Configure(postFix);
                Logger.Info("IndexName: " + indexName, null, this, "Index");
            }
            else
            {
                SearchIndexConfiguration.Configure();
            }

            // get lookups
            zipDict = zipCodeRepository.GetAllNoTracking()
                .ToDictionary(k => k.Id, v => v);
            stateDict = stateRepository.GetAllNoTracking()
                .ToDictionary(k => k.StateId, v => v);
            neighborhoodDict = neighborhoodRepository.GetAllNoTracking()
                .ToDictionary(k => k.NeighborhoodId, v => v);

            awardDict = awardRepository.GetAllNoTracking()
                .ToLookup(k => k.AgentId, v => v);
            educationDict = educationRepository.GetAllNoTracking()
                .ToLookup(k => k.AgentId, v => v);
            organizationsDict = organizationsRepository.GetAllNoTracking()
                .ToLookup(k => k.AgentId, v => v);
            publicationsDict = publicationsRepository.GetAllNoTracking()
                .ToLookup(k => k.AgentId, v => v);

            mlsDict = mlsRepository.GetAllNoTracking()
            .ToDictionary(k => k.Id, v => v);

            var criteria = new SearchCriteria<MLSOffice>()
            {
                Index = SearchIndex.MLSOffices
            };

            var mustNotFilters = new List<FilterContainer>();
            FilterContainer locationFilter = Filter<MLSOffice>.Missing(p => p.Location);
            mustNotFilters.Add(locationFilter);
            criteria.Filters.Add(Filter<MLSOffice>.Bool(b => b.MustNot(mustNotFilters.ToArray())));

            var total = searchRepository.Count(criteria).Total;
            criteria.Size = (int)total;
            agentOfficeLocations = searchRepository.Get(criteria).Documents.AsQueryable();

            pageRepository.SetCommandTimeout(120);

            pages = pageRepository.GetAllNoTracking()
                                      .Select(p => new FacebookPageIndexer { Id = p.Id, UserId = p.UserId, HS = p.PropertySearch, Profile = p.AgentProfile, Directory = p.AgentDirectory, DSDate = p.SweepstakesDate != null })
                                      .GroupBy(p => p.UserId)
                                      .ToDictionary(k => k.Key, v => v.ToList());

            agentZipLocations = agentZipRepository.GetAllNoTracking()
                .GroupBy(a => a.Agent_Id)
                .ToDictionary(k => k.Key, v => v.ToList());

            AgentSMSDict = notificationPreferenceRepository.GetAllNoTracking()
                .ToDictionary(k => k.MembershipId, v => v.SMS);

            AgentHomeValueOnALPDict = AgentSettingRepository.GetAllNoTracking()
                .Where(a => a.Key == AgentSettingKeys.HomeValueOnALP)
                .ToDictionary(k => k.AgentId, v => v.Value == "true");

            var cuttOffDate = runTime.AddDays(1);
            AgentProductsDict = fundingSourceRepository.GetAllNoTracking()
                .Where(f => f.Active && (!f.SubscriptionEndDate.HasValue || f.SubscriptionEndDate.Value > cuttOffDate))
                .Select(f => new { ProductId = f.ProductTypeId, AgentId = f.MembershipId })
                .GroupBy(f => f.AgentId)
                .ToDictionary(f => f.Key, v => v.Select(a => a.ProductId).ToList());

            AgentEmailDict = agentEmailRepository.GetAllNoTracking()
                            .GroupBy(a => a.AgentId)
                            .ToDictionary(k => k.Key, v => v.ToList());


            var timeStart = DateTime.Now;

            Logger.Info("Processing agent index...", null, this, "Index");

            // Process Home Search (IDX) agents first
            try
            {
                ProcessAgentIndex(indexName, true);
                ProcessAgentIndex(indexName, false);
            }
            catch (Exception ex)
            {
                Logger.Error("Unable to process the agent index.", null, ex, this, "Index");
                throw ex;
            }

            Logger.Info("Agent index processing complete.", null, this, "Index");

            if (createNew && !noSwap)
            {
                Logger.Info("Swapping the index...", null, this, "Index");
                try
                {
                    SearchIndexConfiguration.SwapIndex(indexName, SearchIndex.Agents);
                }
                catch (Exception ex)
                {
                    Logger.Error("Unable to swap the index.", null, ex, this, "Index");
                }
                Logger.Info("Index swap complete.", null, this, "Index");
            }

            var time = DateTime.Now.Subtract(timeStart);
            Trace.WriteLine(time.TotalSeconds);
        }


        private void ProcessAgentIndex(string indexName, bool idxOnly)
        {
            var agentRepositoryTemp = AgentRepository;
            var agentRepositoryCountQuery = agentRepositoryTemp.GetAllNoTracking();

            if (idxOnly)
            {
                agentRepositoryCountQuery = agentRepositoryCountQuery.Where(a => a.HomeSearchRegisteredDateTime != null);
            }
            else
            {
                agentRepositoryCountQuery = agentRepositoryCountQuery.Where(a => a.HomeSearchRegisteredDateTime == null);
            }

            int total = agentRepositoryCountQuery.Count();
            int batch = 500;
            int processed = 0;
            int lastId = 0;

            var searchRepository = SearchRepository;

            do
            {
                using (var agentRepository = new AgentContext())
                {
                    agentRepository.Database.CommandTimeout = 600;
                    agentRepository.Configuration.LazyLoadingEnabled = false;

                    var preLoadResult = agentRepository.Set<Agent>()
                                .Include("Membership")
                                .Include("Association")
                                .Include("AgentBrokerages")
                                .Include("AgentBrokerages.Brokerage")
                                .Include("AgentBrokerages.BrokerageLogo")

                                .Include("Credentials")
                                .Include("Expertise")
                                .Include("Languages")

                                .Include("ServiceAreas")
                                .Include("AsapBalance")
                                .Include("AgentAffiliations")
                                .Include("AgentAffiliations.Affiliation")
                                .Include("AgentAffiliations.Affiliation.AgentAffiliations.Agent")
                                .Include("AgentAffiliations.Affiliation.Category")
                                .Include("AgentSettings")
                                /*
                                .Include("Awards")
                                .Include("Education")
                                .Include("FacebookPages")
                                .Include("ServiceAreas")
                                .Include("Organizations")
                                .Include("Publications")
                                */
                                .OrderBy(a => a.Id)
                                .Where(a => a.Id > 0);

                    if (idxOnly)
                    {
                        preLoadResult = preLoadResult.Where(a => a.HomeSearchRegisteredDateTime != null);
                    }
                    else
                    {
                        preLoadResult = preLoadResult.Where(a => a.HomeSearchRegisteredDateTime == null);
                    }

                    List<Agent> result = preLoadResult.Skip(processed).Take(batch).ToList();

                    foreach (var agentInResult in result)
                    {
                        agentInResult.AgentEmails = AgentEmailDict.ContainsKey(agentInResult.Id) ? AgentEmailDict[agentInResult.Id] : null;
                    }

                    var agents = new List<MatchedAgent>();
                    Mapper.Map(result, agents);

                    Console.WriteLine($"Processing {processed + batch}/{total}...");
                    Logger.Info($"Processing {processed + batch}/{total}...", null, this, "ProcessAgentIndex");
                    foreach (var a in agents)
                    {
                        try {
                            lastId = a.Id;
                            var hasPageDsDate = false;

                            var agentPages = pages.ContainsKey(a.Id) ? pages[a.Id] : null;
                            if (agentPages != null)
                            {
                                var profilePage = agentPages.Where(p => p.Profile).OrderByDescending(p => p.Directory).FirstOrDefault();
                                a.AgentProfilePageId = profilePage == null ? string.Empty : profilePage.Id;
                                var homeSearchPage = agentPages.Where(p => p.HS).OrderByDescending(p => p.Directory).FirstOrDefault();
                                a.HomeSearchPageId = homeSearchPage == null ? string.Empty : homeSearchPage.Id;
                                hasPageDsDate = agentPages.Any(p => p.DSDate);
                            }
                            else
                            {
                                a.AgentProfilePageId = string.Empty;
                                a.HomeSearchPageId = string.Empty;
                            }

                            //populate agent settings if available
                            var agentSettings = AgentSettingRepository.GetAll().Where(s => s.AgentId == a.Id).ToList();
                            a.AgentSettings = agentSettings;

                            var agentZips = agentZipLocations.ContainsKey(a.Id) ? agentZipLocations[a.Id] : null;
                            var zipCodeModels = new List<ZipCodeModel>();
                            var zipCodeList = new List<string>();
                            var neighborhoodModels = new List<string>();

                            // Populate the service area from the mls office location
                            ServiceAreaModel serviceArea = new ServiceAreaModel("none", null);

                            if (!string.IsNullOrEmpty(a.MlsOfficeId))
                            {
                                // Populate the service area from the mls office location
                                var agentOffice = agentOfficeLocations.FirstOrDefault(o => o.OfficeId == a.MlsOfficeId && o.MlsId == a.MlsId);
                                if (agentOffice != null)
                                {
                                    serviceArea = ServiceAreaModel.CreateCircle(agentOffice.Location.Lat, agentOffice.Location.Lon, Settings.AgentDefaultServiceAreaRadius);
                                }
                            }

                            if (agentZips != null)
                            {
                                // Populate the service area from the zip if it is still empty
                                if (serviceArea.GeoShape == null && agentZips.Count > 0)
                                {
                                    int firstZip = agentZips.First().Zip;
                                    ZipCode primaryZip = null;
                                    zipDict.TryGetValue(firstZip, out primaryZip);
                                    if (primaryZip != null)
                                    {
                                        string latitude = String.IsNullOrWhiteSpace(primaryZip.Latitude) ? "0" : primaryZip.Latitude;
                                        string longitude = String.IsNullOrWhiteSpace(primaryZip.Longitude) ? "0" : primaryZip.Longitude;
                                        double lat = 0;
                                        double lon = 0;
                                        double.TryParse(latitude, out lat);
                                        double.TryParse(longitude, out lon);
                                        serviceArea = ServiceAreaModel.CreateCircle(lat, lon, Settings.AgentDefaultServiceAreaRadius);
                                    }
                                }

                                foreach (var z in agentZips)
                                {
                                    var zipCode = zipDict.ContainsKey(z.Zip) ? zipDict[z.Zip] : null;
                                    var zipModel = new ZipCodeModel { Zip = z.Zip.ToString("D5"), City = string.Empty, StateCode = string.Empty };
                                    if (zipCode != null)
                                    {
                                        zipModel.City = zipCode.City;
                                        var state = stateDict.ContainsKey(zipCode.StateID) ? stateDict[zipCode.StateID] : null;
                                        if (state != null)
                                        {
                                            zipModel.StateCode = state.StateCode;
                                            zipModel.StateName = state.Name;
                                        }
                                    }
                                    zipCodeModels.Add(zipModel);
                                    zipCodeList.Add(zipModel.Zip);
                                }
                            }

                            a.AgentEmails = AgentEmailDict.ContainsKey(lastId) ? AgentEmailDict[lastId] : null;

                            a.ServiceAreas = a.ServiceAreas ?? new List<ServiceAreaModel>();
                            if (a.ServiceAreas.Count() == 0)
                            {
                                a.ServiceAreas.Add(serviceArea);
                            }

                            a.ZipCodes = zipCodeModels;
                            a.ZipCodeList = zipCodeList;
                            a.Neighborhoods = neighborhoodModels;

                            foreach (var item in awardDict[a.Id])
                            {
                                var itemModel = new AgentAwardModel();
                                Mapper.Map(item, itemModel);
                                a.Awards.Add(itemModel);
                            }
                            foreach (var item in educationDict[a.Id])
                            {
                                var itemModel = new AgentEducationModel();
                                Mapper.Map(item, itemModel);
                                a.Education.Add(itemModel);
                            }
                            foreach (var item in organizationsDict[a.Id])
                            {
                                var itemModel = new AgentOrganizationModel();
                                Mapper.Map(item, itemModel);
                                a.Organizations.Add(itemModel);
                            }
                            foreach (var item in publicationsDict[a.Id])
                            {
                                var itemModel = new AgentPublicationModel();
                                Mapper.Map(item, itemModel);
                                a.Publications.Add(itemModel);
                            }

                            if (a.MlsId != null)
                            {
                                if (mlsDict.ContainsKey(a.MlsId))
                                {
                                    var mls = mlsDict[a.MlsId];
                                    a.MlsActive = mls.IsActive;
                                    a.DataShareMlsIds = mls.QueryableDataShares?.Select(ds => ds.QueryableMlsId).ToList();
                                }
                            }

                            // Populate geographic centroids only for idx agents because we only use this data for displaying an agent on the home search site.
                            if (idxOnly)
                            {
                                try
                                {
                                    AgentLocations.PopulateGeographicCentroids(a, agentZips);
                                }
                                catch (Exception ex)
                                {
                                    Logger.Error("Unable to populate geographic centroid data for this agent.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, a.Id) }, ex, this, "ProcessAgentIndex");
                                }
                            }

                            a.EnableSMS = AgentSMSDict.ContainsKey(a.Id) ? AgentSMSDict[a.Id] : false;

                            var showHomeValueOnALP = AgentHomeValueOnALPDict.ContainsKey(a.Id) ? AgentHomeValueOnALPDict[a.Id] : true;
                            var productIds = AgentProductsDict.ContainsKey(a.Id) ? AgentProductsDict[a.Id].Distinct().ToList() : new List<byte>();
                            var hasTKS = productIds.Contains((int)ProductType.Bundle);
                            var hasInstagramIncluded = false;
                            if (hasTKS)
                            {
                                var fsRecord = FundingSourceRepository.GetAll().Where(f => f.MembershipId == a.Id && f.ProductTypeId == (int)ProductType.Bundle && f.Active == true).FirstOrDefault();
                                if (fsRecord != null)
                                {
                                    var productPlanTypeId = fsRecord.ProductPlan?.ProductPlanTypeId;
                                    if (productPlanTypeId == (int)ProductPlanType.InstagramIncluded ||
                                        productPlanTypeId == (int)ProductPlanType.InstagramIDXIncluded ||
                                        productPlanTypeId == (int)ProductPlanType.AfordalInstagramIDXIncluded)
                                    {
                                        hasInstagramIncluded = true;
                                    }
                                }
                            }
                            var productServices = new List<string>();
                            var agentModel = result.SingleOrDefault(ag => ag.Id == a.Id);
                            bool isIDXPlusIncludedPlan = false;
                            bool isAfordalIncludedPlan = false;

                            // Check for standalone Afordal product
                            if (productIds.Contains((int)ProductType.Afordal))
                            {
                                isAfordalIncludedPlan = true;
                            }

                            // Check for Bundle plans with Afordal included (following Agent.cs pattern)
                            if (productIds.Contains((int)ProductType.Bundle))
                            {
                                var bundleFundingSource = FundingSourceRepository.GetAll()
                                    .Where(f => f.MembershipId == a.Id && f.ProductTypeId == (int)ProductType.Bundle && f.Active == true).FirstOrDefault();
                                if (bundleFundingSource?.ProductPlan != null)
                                {
                                    var productPlanTypeId = bundleFundingSource.ProductPlan.ProductPlanTypeId;
                                    if (productPlanTypeId == (byte)ProductPlanType.AfordalIncluded ||
                                        productPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded)
                                    {
                                        isAfordalIncludedPlan = true;
                                    }
                                }
                            }

                            // Check for IDX plans with IDX Plus and Afordal included
                            if (productIds.Contains((int)ProductType.IDX))
                            {
                                var fundingSource = FundingSourceRepository.GetAll().Where(f => f.MembershipId == a.Id && f.ProductTypeId == (int)ProductType.IDX && f.Active == true).FirstOrDefault();
                                if (fundingSource?.ProductPlan != null)
                                {
                                    if (fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.IDXPlusIncluded)
                                    {
                                        isIDXPlusIncludedPlan = true;
                                    }
                                    if (fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalIncluded ||
                                        fundingSource.ProductPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded)
                                    {
                                        isAfordalIncludedPlan = true;
                                    }
                                }
                            }

                            if (agentModel != null)
                                productServices = agentModel.GetProductServiceAbbreviations(productIds, a.HomeSearchRegisteredDateTime, hasPageDsDate, true, showHomeValueOnALP, hasInstagramIncluded, isIDXPlusIncludedPlan, isAfordalIncludedPlan);
                            a.ProductServices = productServices;
                        }
                        catch(Exception ex)
                        {
                            Logger.Error("Unable to process this batch of agents.{processed + batch}/{total}", null, ex, this, "ProcessAgentIndex");
                        }
                    }

                    try
                    {
                        searchRepository.SaveBatch<MatchedAgent>(agents, indexName, BULK_AGENT_SAVE_BATCH_SIZE);
                    }
                    catch(Exception ex)
                    {
                        Logger.Error("Unable to save this batch of agents.  Attempting to save the agents individually.", null, ex, this, "ProcessAgentIndex");
                        foreach (var a in agents)
                        {
                            try
                            {
                                searchRepository.Save(a, SearchIndex.Agents);
                            }
                            catch(Exception ex2)
                            {
                                Logger.Error("Unable to save this agent. ", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, a.Id) }, ex2, this, "ProcessAgentIndex");
                            }
                        }
                    }

                    processed += result.Count;

                    agentRepository.Dispose();
                }
            }
            while (processed < total);
        }
    }
}
