﻿using MLS.Synchronization.Abstract;
using MLS.Synchronization.Models;
using MLS.Synchronization.Models.Exceptions;
using Nest;
using NPlay;
using NPlay.BusinessLogic.AgentListingWebsite;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Messaging.Payloads;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Models.Canonical;
using NPlay.Common.Services.Abstract;
using NPlay.RealDataFlow.RETSConnector;
using RESO.Connector.Exceptions;
using RESO.Models;
using RETS.Models;
using RETS.Models.RAWStorage;
using RETS.Models.RETSSchema;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace RealData.Simulation
{
    public class DataFeedSynchronizationSimulator : IDataFeedSynchronizationSimulator
    {
        IMlsSynchronizationContextProvider MlsSynchronizationContextProvider;
        IEventBroker EventBroker;
        ISearchRepository SearchRepository;
        IGetMLSFeedData GetRETSFeedData;
        IGetRESOFeedData GetRESOFeedData;
        IRETSMetadataManagerClient MetadataManagerClient;
        IMapRETSData MapRETSData;
        IMapRESOData MapRESOData;
        IRETSImageDownloader RETSImageDownloader;
        IRESOImageDownloader RESOImageDownloader;
        IActiveListingService ActiveListingService;
        IActiveListingRepository ActiveListingRepository;
        ICanonAgentSaveToSQL CanonAgentSaveToSQL;
        IAgentListingSiteManager AgentListingSiteManager;
        ICachedMLSRepository MLSCache;
        IRawListingRepository RawListingRepository;
        IRemoveListingService RemoveListingService;
        IListingProcessorClient ListingProcessorClient;
        IMlsSynchronizationClient MlsSynchronizationClient;
        ILogger Logger;

        private static readonly int REQUESTS_PER_THROTTLING_PERIOD = 100;
        private static readonly TimeSpan THROTTLING_PERIOD = new TimeSpan(0, 0, 1);
        private SemaphoreSlim ListingProcessingThrottler;

        public DataFeedSynchronizationSimulator(IMlsSynchronizationContextProvider mlsSynchronizationContextProvider,
                                    IEventBroker eventBroker,
                                    ISearchRepository searchRepo,
                                    IGetMLSFeedData getMLSFeedData,
                                    IGetRESOFeedData getRESOFeedData,
                                    IRETSMetadataManagerClient metadataClient,
                                    IMapRETSData mapRETSdata,
                                    IMapRESOData mapRESOData,
                                    IRETSImageDownloader retsImageDownloader,
                                    IRESOImageDownloader resoImageDownloader,
                                    IActiveListingService activeListingService,
                                    IActiveListingRepository activeListingRepository,
                                    IAgentListingSiteManager agentListingSiteManager,
                                    ICanonAgentSaveToSQL canonAgentSaveToSQL,
                                    ICachedMLSRepository mlsCache,
                                    IRawListingRepository rawListingRepository,
                                    IRemoveListingService removeListingService,
                                    IListingProcessorClient listingProcessorClient,
                                    IMlsSynchronizationClient mlsSynchronizationClient,
                                    ILogger logger)
        {
            MlsSynchronizationContextProvider = mlsSynchronizationContextProvider;
            //EventBroker = eventBroker as IMemoryEventBroker;
            //if (eventBroker == null)
            //    throw new NotSupportedException("IContextualEventBroker instances must be of type IMemoryEventBroker when injected into the DataFeedSynchronizationSimulator.");

            EventBroker = eventBroker;
            SearchRepository = searchRepo;
            GetRETSFeedData = getMLSFeedData;
            GetRESOFeedData = getRESOFeedData;
            MetadataManagerClient = metadataClient;
            MapRETSData = mapRETSdata;
            MapRESOData = mapRESOData;
            RETSImageDownloader = retsImageDownloader;
            RESOImageDownloader = resoImageDownloader;
            ActiveListingService = activeListingService;
            ActiveListingRepository = activeListingRepository;
            AgentListingSiteManager = agentListingSiteManager;
            CanonAgentSaveToSQL = canonAgentSaveToSQL;
            MLSCache = mlsCache;
            RawListingRepository = rawListingRepository;
            RemoveListingService = removeListingService;
            ListingProcessorClient = listingProcessorClient;
            MlsSynchronizationClient = mlsSynchronizationClient;
            Logger = logger;

            ListingProcessingThrottler = new SemaphoreSlim(REQUESTS_PER_THROTTLING_PERIOD);

        }

        public async Task ProcessDataFeedAsync(DataFeed feed, bool forceImages = false, bool forceGeoCode = false)
        {
            if (EventBroker is IMemoryEventBroker)
            {
                (EventBroker as IMemoryEventBroker).ClearEvents();
            }

            await ProcessWithQueryAsync(feed, null, forceImages, forceGeoCode);
        }

        public async Task ProcessListingAsync(MultipleListingService mls, DataFeed feed, string propertyListingId, bool forceImages = false, bool forceGeoCode = false)
        {
            if (EventBroker is IMemoryEventBroker)
            {
                (EventBroker as IMemoryEventBroker).ClearEvents();
            }

            string query = GetListingQuery(mls, feed, propertyListingId);
            await ProcessWithQueryAsync(feed, query, forceImages, forceGeoCode);
        }

        public async Task ProcessAgentAsync(MultipleListingService mls, DataFeed feed, string mlsAgentId)
        {
            if (EventBroker is IMemoryEventBroker)
            {
                (EventBroker as IMemoryEventBroker).ClearEvents();
            }

            string query = GetAgentQuery(mls, feed, mlsAgentId);
            await ProcessAgentWithQueryAsync(mls, feed, query);
        }

        public async Task ProcessListingDataAsync(string mlsId, string internalId)
        {
            throw new NotImplementedException();
        }

        public Task ProcessAgentAsync(string mlsId, string internalId)
        {
            throw new NotImplementedException();
        }

        public void ProcessListingSiteImages(string mlsId, string internalId)
        {
            AgentListingSiteManager.UpdatePreloadImagesAsync(mlsId, internalId);
        }

        public async Task ProcessListingImagesAsync(string mlsId, string internalId)
        {
            // TODO: Implement RESO

            var memoryEventBroker = EventBroker as IMemoryEventBroker;
            memoryEventBroker.ClearEvents();

            var context = MlsSynchronizationContextProvider.Get(mlsId);

            var listing = await GetListingAsync(mlsId, internalId);
            var mls = GetMLS(mlsId);
            var feed = GetDataFeed(mls, listing);
            var imagesEvent = GenerateRETSImagesEvent(context, feed, listing);
            if (imagesEvent != null)
            {
                imagesEvent.Payload.ImpersonateForImageUpload = true;
                try
                {
                    await RETSImageDownloader.ProcessAsync(imagesEvent.Payload);
                }
                catch (RateLimitExceededException ex)
                {
                    if (ex.ResetTime.HasValue && ex.ResetTime > DateTime.UtcNow)
                    {
                        imagesEvent.RetryOffsetMilliseconds = Math.Abs((int)(ex.ResetTime.Value - DateTime.UtcNow).TotalMilliseconds);
                    }

                    imagesEvent.Reject();
                }
            }

            // Wait for elastic
            await Task.Delay(2000);

            var canonicalListingReadyEvent = memoryEventBroker.GetEvents<CanonicalListingReadyEvent, CanonicalListingPayload>().FirstOrDefault();
            if (canonicalListingReadyEvent != null)
            {
                await ActiveListingService.ProcessAsync(canonicalListingReadyEvent.Payload);
            }
        }

        public async Task ProcessListingAsync(MultipleListingService mls, DataFeed feed, string propertyListingId)
        {
            var memoryEventBroker = EventBroker as IMemoryEventBroker;
            if (memoryEventBroker == null) throw new NotSupportedException("Cannot reprocess individual listings in a chain of events unless using IMemoryEventBroker.");

            memoryEventBroker.ClearEvents();

            string query = GetListingQuery(mls, feed, propertyListingId);

            var schedulePayload = new MLSFeedSchedulePayload() { MlsId = mls.Id, DataFeedId = feed.ID };

            IGetMLSFeedData dataProvider;
            switch (feed.ProtocolType)
            {
                case DataFeedProtocol.RETS:
                    dataProvider = GetRETSFeedData;
                    dataProvider.Process(schedulePayload, query, 0);
                    RETSRawDataEvent retsRawDataEvent = memoryEventBroker.GetEvents<RETSRawDataEvent, RETSRawDataPayload>().FirstOrDefault();
                    retsRawDataEvent.Payload.ForceImageDownload = true;
                    retsRawDataEvent.Payload.ForceGeoCode = true;

                    // FOR DEBUGGING IN MEMORY:
                    //await ProcessListingAsync(retsRawDataEvent);

                    await ListingProcessorClient.ProcessAsync(retsRawDataEvent);
                    break;
                case DataFeedProtocol.RESOWebApi:
                    dataProvider = GetRESOFeedData;
                    dataProvider.Process(schedulePayload, query, 0);
                    RESORawDataEvent resoRawDataEvent = memoryEventBroker.GetEvents<RESORawDataEvent, RESORawDataPayload>().FirstOrDefault();
                    resoRawDataEvent.Payload.ForceImageDownload = true;
                    resoRawDataEvent.Payload.ForceGeoCode = true;

                    // FOR DEBUGGING IN MEMORY:
#if DEBUG
                    await ProcessListingAsync(resoRawDataEvent);
#else
                    await ListingProcessorClient.ProcessAsync(resoRawDataEvent);
#endif
                    break;
            }
        }

        public async Task ProcessListingAsync(string mlsId, string internalId)
        {
            var listing = await GetListingAsync(mlsId, internalId);
            var mls = GetMLS(mlsId);
            var feed = GetDataFeed(mls, listing);

            await ProcessListingAsync(mls, feed, listing.PropertyListingId);
        }

        public async Task ProcessWithQueryAsync(DataFeed feed, string query = null, bool forceImages = false, bool forceGeoCode = false)
        {
            string mlsId = feed.MLSID;
            int dataFeedId = feed.ID;
            var schedulePayload = new MLSFeedSchedulePayload() { MlsId = mlsId, DataFeedId = dataFeedId };
            DateTime startDate;
            if (DateTime.TryParse(feed.LastSyncPoint, out startDate))
            {
                schedulePayload.StartDate = startDate;
            }

            switch (feed.ProtocolType)
            {
                case DataFeedProtocol.RETS:
                    await ProcessWithQueryRETSAsync(schedulePayload, query, forceImages, forceGeoCode);
                    break;
                case DataFeedProtocol.RESOWebApi:
                    await ProcessWithQueryRESOAsync(schedulePayload, query, forceImages, forceGeoCode);
                    break;
                default:
                    throw new InvalidProtocolException($"Unsupported protocol '{feed.ProtocolType.ToString()}'");
            }
        }

        public async Task ProcessAgentWithQueryAsync(MultipleListingService mls, DataFeed feed, string query = null)
        {
            string mlsId = mls.Id;
            int dataFeedId = feed.ID;
            var schedulePayload = new MLSFeedSchedulePayload() { MlsId = mlsId, DataFeedId = dataFeedId };
            switch (feed.ProtocolType)
            {
                case DataFeedProtocol.RETS:
                    await ProcessRETSAgentWithQueryAsync(schedulePayload, query);
                    break;
                case DataFeedProtocol.RESOWebApi:
                    // TODO:
                    //await ProcessListingRESO(schedulePayload, query, forceImages, forceGeoCode);
                    break;
                default:
                    throw new InvalidProtocolException($"Unsupported protocol '{feed.ProtocolType.ToString()}'");
            }
        }

        public async Task ProcessWithQueryRETSAsync(MLSFeedSchedulePayload schedulePayload, string query, bool forceImages = false, bool forceGeoCode = false)
        {
            await Task.Run(() =>
            {
                GetRETSFeedData.Reindex(schedulePayload, query, 0, forceImages, forceGeoCode);
            });
            //List<RETSRawDataEvent> events = EventBroker.GetEvents<RETSRawDataEvent, RETSRawDataPayload>();
            //foreach (var ev in events)
            //{
            //    ev.Payload.ForceImageDownload = forceImages;
            //    ev.Payload.ForceGeoCode = forceGeoCode;
            //}

            //List<Task> listingProcessingTasks = new List<Task>();
            //foreach (var ev in events)
            //{
            //    listingProcessingTasks.Add(Task.Run(async () =>
            //    {
            //        string functionName = "listingProcessingTask";
            //        Guid functionCallId = Logger.StartMethod(functionName);

            //        try
            //        {
            //            await ProcessListingWithThrottlingAsync(async () => await ListingProcessorClient.ProcessAsync(ev), ev.Payload.MLSID, ev.Payload.PropertyListingID);
            //        }
            //        catch (Exception ex)
            //        {
            //            Logger.Error("Unable to process listing", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, ev.Payload.MLSID), new LoggingPair(LoggingKey.PropertyListingId, ev.Payload.PropertyListingID) }, ex, this, "ProcessWithQueryRETSAsync");
            //        }

            //        Logger.EndMethod(functionName, functionCallId);
            //    }));
            //}

            //await Task.WhenAll(listingProcessingTasks);
        }

        private async Task ProcessListingWithThrottlingAsync(Func<Task> doWork, string mlsId, string propertyListingId)
        {
            Logger.Debug("Waiting the throttler", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, mlsId), new LoggingPair(LoggingKey.PropertyListingId, propertyListingId) }, this, "ProcessListingInternalAsync");
            await ListingProcessingThrottler.WaitAsync();

            DateTime start = DateTime.MinValue;
            DateTime end = DateTime.MinValue;
            var processingTask = Task.Run(async () =>
            {
                try
                {
                    start = DateTime.UtcNow;
                    Logger.Info("Processing listing", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, mlsId), new LoggingPair(LoggingKey.PropertyListingId, propertyListingId) }, this, "GetAsync");
                    await doWork();
                }
                catch (Exception ex)
                {
                    Logger.Error("Unable to process listing", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, mlsId), new LoggingPair(LoggingKey.PropertyListingId, propertyListingId) }, ex, this, "ProcessListingInternalAsync");
                    throw ex;
                }
                finally
                {
                    Logger.Debug($"Releasing semaphore.", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, mlsId), new LoggingPair(LoggingKey.PropertyListingId, propertyListingId) }, this, "ProcessListingInternalAsync");
                    ListingProcessingThrottler.Release();
                }
            });

            await processingTask;
        }

        public async Task ReindexListingAsync(RETSRawDataEvent ev)
        {
            var memoryEventBroker = EventBroker as IMemoryEventBroker;
            if (memoryEventBroker == null)
                throw new InvalidOperationException();

            memoryEventBroker.ClearEvents();

            string methodName = "ProcessListingAsync";
            Guid callId = Logger.StartMethod(methodName);

            memoryEventBroker.ClearEvents<RETSImagesEvent, RETSImagesPayload>();
            memoryEventBroker.ClearEvents<CanonicalListingReadyEvent, CanonicalListingPayload>();

            await MapRETSData.ProcessAsync(ev.Payload);

            var imagesEvent = memoryEventBroker.GetEvents<RETSImagesEvent, RETSImagesPayload>().FirstOrDefault();

            if (imagesEvent != null)
            {
                imagesEvent.Payload.ImpersonateForImageUpload = true;
                try
                {
                    await RETSImageDownloader.ProcessAsync(imagesEvent.Payload);
                }
                catch (RateLimitExceededException ex)
                {
                    if (ex.ResetTime.HasValue && ex.ResetTime > DateTime.UtcNow)
                    {
                        imagesEvent.RetryOffsetMilliseconds = Math.Abs((int)(ex.ResetTime.Value - DateTime.UtcNow).TotalMilliseconds);
                    }

                    imagesEvent.Reject();
                }
            }

            // Wait for elastic
            await Task.Delay(2000);

            var canonicalListingReadyEvent = memoryEventBroker.GetEvents<CanonicalListingReadyEvent, CanonicalListingPayload>().FirstOrDefault();
            if (canonicalListingReadyEvent != null)
            {
                await ActiveListingService.ProcessAsync(canonicalListingReadyEvent.Payload);
            }

            Logger.EndMethod(methodName, callId);
        }

        public async Task ProcessWithQueryRESOAsync(MLSFeedSchedulePayload schedulePayload, string query, bool forceImages = false, bool forceGeoCode = false)
        {
            await Task.Run(() =>
            {
                GetRESOFeedData.Reindex(schedulePayload, query, forceImages: forceImages, forceGeoCode: forceGeoCode);
            });

            var memoryEventBroker = EventBroker as IMemoryEventBroker;
            if (memoryEventBroker == null)
                throw new InvalidOperationException();

            var events = memoryEventBroker.GetEvents<RESORawDataEvent, RESORawDataPayload>();

            foreach (var ev in events)
            {
                await ProcessListingAsync(ev, false);
            }

            //foreach (var ev in events)
            //{
            //    ev.Payload.ForceImageDownload = forceImages;
            //    ev.Payload.ForceGeoCode = forceGeoCode;
            //}

            //List<Task> listingProcessingTasks = new List<Task>();
            //foreach (var ev in events)
            //{
            //    listingProcessingTasks.Add(Task.Run(async () =>
            //    {
            //        string functionName = "listingProcessingTask";
            //        Guid functionCallId = Logger.StartMethod(functionName);

            //        try
            //        {
            //            await ProcessListingWithThrottlingAsync(async () => await ListingProcessorClient.ProcessAsync(ev), ev.Payload.MLSID, ev.Payload.PropertyListingID);
            //        }
            //        catch (Exception ex)
            //        {
            //            Logger.Error("Unable to process listing", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, ev.Payload.MLSID), new LoggingPair(LoggingKey.PropertyListingId, ev.Payload.PropertyListingID) }, ex, this, "ProcessWithQueryRETSAsync");
            //        }

            //        Logger.EndMethod(functionName, functionCallId);
            //    }));
            //}

            //await Task.WhenAll(listingProcessingTasks);
        }

        public async Task ProcessListingAsync(RESORawDataEvent ev, bool clearEvents = true)
        {
            var memoryEventBroker = EventBroker as IMemoryEventBroker;
            if (memoryEventBroker == null)
                throw new InvalidOperationException();

            try
            {
                if (clearEvents)
                {
                    memoryEventBroker.ClearEvents<RESOImagesEvent, RESOImagesPayload>();
                    memoryEventBroker.ClearEvents<CanonicalListingReadyEvent, CanonicalListingPayload>();
                }

                await MapRESOData.ProcessAsync(ev.Payload);

                var imagesEvent = memoryEventBroker.GetEvents<RESOImagesEvent, RESOImagesPayload>().FirstOrDefault();

                if (imagesEvent != null)
                {
                    imagesEvent.Payload.ImpersonateForImageUpload = true;
                    try
                    {
                        await RESOImageDownloader.ProcessAsync(imagesEvent.Payload);
                    }
                    catch (RateLimitExceededException ex)
                    {
                        if (ex.ResetTime.HasValue && ex.ResetTime > DateTime.UtcNow)
                        {
                            imagesEvent.RetryOffsetMilliseconds = Math.Abs((int)(ex.ResetTime.Value - DateTime.UtcNow).TotalMilliseconds);
                        }

                        imagesEvent.Reject();
                    }
                }

                var canonicalListingReadyEvent = memoryEventBroker.GetEvents<CanonicalListingReadyEvent, CanonicalListingPayload>().FirstOrDefault();
                if (canonicalListingReadyEvent != null)
                {
                    await ActiveListingService.ProcessAsync(canonicalListingReadyEvent.Payload);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Unexpected error encountered.", null, ex, this, "ProcessListingRESO");
            }
        }

        public async Task ProcessListingAsync(RETSRawDataEvent ev)
        {
            var memoryEventBroker = EventBroker as IMemoryEventBroker;
            if (memoryEventBroker == null)
                throw new InvalidOperationException();

            try
            {
                memoryEventBroker.ClearEvents<RETSImagesEvent, RETSImagesPayload>();
                memoryEventBroker.ClearEvents<CanonicalListingReadyEvent, CanonicalListingPayload>();
                await MapRETSData.ProcessAsync(ev.Payload);

                var imagesEvent = memoryEventBroker.GetEvents<RETSImagesEvent, RETSImagesPayload>().FirstOrDefault();

                if (imagesEvent != null)
                {
                    imagesEvent.Payload.ImpersonateForImageUpload = true;
                    try
                    {
                        await RETSImageDownloader.ProcessAsync(imagesEvent.Payload);
                    }
                    catch (RateLimitExceededException ex)
                    {
                        if (ex.ResetTime.HasValue && ex.ResetTime > DateTime.UtcNow)
                        {
                            imagesEvent.RetryOffsetMilliseconds = Math.Abs((int)(ex.ResetTime.Value - DateTime.UtcNow).TotalMilliseconds);
                        }

                        imagesEvent.Reject();
                    }
                }

                var canonicalListingReadyEvent = memoryEventBroker.GetEvents<CanonicalListingReadyEvent, CanonicalListingPayload>().FirstOrDefault();
                if (canonicalListingReadyEvent != null)
                {
                    await ActiveListingService.ProcessAsync(canonicalListingReadyEvent.Payload);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Unexpected error encountered.", null, ex, this, "ProcessListingRESO");
            }
        }

        public async Task ProcessRETSAgentWithQueryAsync(MLSFeedSchedulePayload schedulePayload, string query)
        {
            var memoryEventBroker = EventBroker as IMemoryEventBroker;
            if (memoryEventBroker == null)
                throw new InvalidOperationException();

            IGetMLSFeedData dataProvider = GetRETSFeedData;
            dataProvider.Process(schedulePayload, query, 0);
            List<RETSRawDataEvent> events = memoryEventBroker.GetEvents<RETSRawDataEvent, RETSRawDataPayload>();

            //execute the translation to the canon
            foreach (var ev in events)
            {
                memoryEventBroker.ClearEvents<CanonicalAgentReadyEvent, CanonicalAgentPayload>();
                await MapRETSData.ProcessAsync(ev.Payload);

                // Wait for elastic
                await Task.Delay(2000);

                var canonicalAgentReadyEvent = memoryEventBroker.GetEvents<CanonicalAgentReadyEvent, CanonicalAgentPayload>().FirstOrDefault();
                if (canonicalAgentReadyEvent != null)
                {
                    CanonAgentSaveToSQL.Process(canonicalAgentReadyEvent);
                }
            }
        }

#region Helper Methods

        private RETSImagesEvent GenerateRETSImagesEvent(MlsSynchronizationContext context, DataFeed feed, MLSPropertyListing listing)
        {
            RAWRow row = RawListingRepository.GetRawRow(context, listing.PropertyListingId);

            Resource resource = MetadataManagerClient.GetResource(context.MlsId, row.MetaDataVersion, row.Resource);
            Class c = MetadataManagerClient.GetClass(context.MlsId, row.MetaDataVersion, row.Resource, row.Class, feed.ClassName);
            if (c == null || resource == null)
            {
                throw new Exception("Unable to retrieve RETS metadata for this listing.");
            }

            RETSResults results = GetRETSResults(row, c);
            string imageIdField = c.GetDisplayField(resource.KeyField);

            return new RETSImagesEvent()
            {
                Payload = new RETSImagesPayload()
                {
                    ID = row.ID,
                    ImageID = results.Records.First()[imageIdField],
                    MlsId = context.MlsId,
                    Resource = resource.DisplayName,
                    PropertyListingId = listing.PropertyListingId,
                    InternalListingId = listing.Id,
                    ImageModifiedTimestamp = DateTime.UtcNow,
                    ImageCount = listing.ImageCount,
                    MetaDataVersion = row.MetaDataVersion,
                    IsNewListing = false
                }
            };
        }

        private RETSResults GetRETSResults(RAWRow row, Class c)
        {
            RETSResults results = new RETSResults();
            var dataTable = Parser.ParseCompactDecoded(row, c.Table);
            results.AddRecord(row, dataTable);

            return results;
        }

        private async Task<MLSPropertyListing> GetListingAsync(string mlsId, string internalId)
        {
            var listing = await ActiveListingRepository.GetListingByIdAsync(mlsId, internalId);
            if (listing == null)
            {
                Logger.Warning("Listing not found", new LoggingPair[] { new LoggingPair(LoggingKey.IndexId, internalId) }, this, "ProcessListing");
                throw new ListingNotFoundException(internalId);
            }

            return listing;
        }

        private MultipleListingService GetMLS(string mlsId)
        {
            var mls = MLSCache.Get(mlsId);
            if (mls == null)
            {
                Logger.Warning("MLS not found", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, mlsId) }, this, "ProcessListing");
                throw new ArgumentException("Invalid MLD id", nameof(mlsId));
            }

            return mls;
        }

        private DataFeed GetDataFeed(MultipleListingService mls, MLSPropertyListing listing)
        {
            DataFeed feed = null;
            var context = MlsSynchronizationContextProvider.Get(mls.Id);

            var possibleFeeds = mls.DataFeeds.Where(df => df.Status == DataFeedStatus.Active &&
                                                          df.ProtocolType == context.ProtocolType).ToList();

            if (context.ProtocolType == DataFeedProtocol.RESOWebApi)
            {
                if (possibleFeeds.Count == 1)
                {
                    feed = possibleFeeds.First();
                }
                else
                {
                    feed = GetDataFeed(possibleFeeds, listing.PropertyType, listing.SaleType);
                }
            }
            else
            {
                possibleFeeds = possibleFeeds.Where(df => df.Resource == listing.SourceResource).ToList();

                feed = possibleFeeds.FirstOrDefault(df => df.Class == listing.SourceClass);
                feed = feed ?? possibleFeeds.FirstOrDefault(df => df.ClassName == listing.SourceClass);
            }

            if (feed == null)
            {
                Logger.Warning("DataFeed not found", new LoggingPair[] {
                    new LoggingPair(LoggingKey.MlsId, mls.Id),
                    new LoggingPair(LoggingKey.IndexId, listing.Id),
                    new LoggingPair(LoggingKey.RetsResource, listing.SourceResource),
                    new LoggingPair(LoggingKey.RetsClass, listing.SourceClass)}, this, "ProcessListing");

                throw new ArgumentException("Unable to get DataFeed using mls id", nameof(mls.Id));
            }

            return feed;
        }

        private DataFeed GetDataFeed(IEnumerable<DataFeed> possibleFeeds, PropertyType propertyType, SaleType saleType)
        {
            // TODO:  Make the data feed select more robust for reso web api.  The problem is that the listings for reso do not include
            //        any sourceClass or SourceResource information.  It is difficult to select the correct feed because there is no
            //        one-to-one mapping backwards from a set of fields (propertyType, etc.) back to the feed id.  We don't save feed id on listings
            DataFeed feed = null;

            switch (propertyType)
            {
                case PropertyType.TownHomeCondo:
                case PropertyType.MultiFamily:
                case PropertyType.SingleFamily:
                    possibleFeeds = possibleFeeds.Where(df => df.Class.ToLower() == "resi" || df.Class.ToLower() == "residential");
                    break;
                case PropertyType.Commercial:
                    possibleFeeds = possibleFeeds.Where(df => df.Class.ToLower() == "coms" || df.Class == "commercialsale");
                    break;
                case PropertyType.LotsLand:
                    possibleFeeds = possibleFeeds.Where(df => df.Class.ToLower() == "land");
                    break;
                case PropertyType.MixedUse:
                    break;
                case PropertyType.All:
                    break;
            }

            if (possibleFeeds.Count() == 1)
                feed = possibleFeeds.First();

            return feed;
        }

        private string GetListingQuery(MultipleListingService mls, DataFeed feed, string propertyListingId)
        {
            switch (feed.ProtocolType)
            {
                case DataFeedProtocol.RETS:
                    Class c = MetadataManagerClient.GetClass(mls.Id, mls.RETSMetadataVersion, feed.Resource, feed.Class, feed.ClassName);

                    String field = c.Table.GetListingIDField();
                    if (String.IsNullOrEmpty(field))
                    {
                        Resource r = MetadataManagerClient.GetResource(mls.Id, mls.RETSMetadataVersion, feed.Resource);
                        field = r.KeyField;
                    }

                    return "(" + field + "=" + propertyListingId + ")";
                case DataFeedProtocol.RESOWebApi:
                    RESORecord resoRecord;
                    SearchCriteria<RESORecord> criteria = new SearchCriteria<RESORecord>(SearchResultSizes.One);
                    criteria.Index = SearchIndex.RESOListings;
                    criteria.Filters.Add(Filter<RESORecord>.Term("record.ListingId", propertyListingId.ToLower()));

                    MlsSynchronizationContext context = MlsSynchronizationContextProvider.Get(mls.Id);
                    criteria.Filters.Add(Filter<RESORecord>.Term($"record.{context.DataProviderMlsIdFieldName}", context.DataProviderMlsId));

                    resoRecord = SearchRepository.Get(criteria).Documents.FirstOrDefault();

                    // Set up for the query to the data provider:
                    string dataProviderPropertyListingId = context.DataProviderListingPrefix != null && propertyListingId.StartsWith(context.DataProviderListingPrefix) ? propertyListingId : context.DataProviderListingPrefix + propertyListingId;
                    string classPart = feed.Class == "All" ? String.Empty : feed.Class;
                    string versionPart = String.Empty;
                    string expandMedia;
                    string mlsFilter = String.Empty;
                    string listingFilter = $"{context.DataProviderListingIdFieldName} eq '{dataProviderPropertyListingId}'";
                    string quote = String.Empty;
                    switch (context.DataProvider)
                    {
                        case DataProviders.Rapattoni:
                            expandMedia = "$expand=PropertyPictures";
                            break;
                        case DataProviders.MlsGrid:
                            expandMedia = "$expand=Media";
                            versionPart = "v2/";
                            mlsFilter = $"OriginatingSystemName eq '{context.DataProviderMlsId}' and ";
                            quote = "'";
                            break;
                        case DataProviders.Spark:
                        case DataProviders.UtahRealEstate:
                        case DataProviders.RMLS:
                            expandMedia = "$expand=Media";
                            break;
                        default:
                            expandMedia = String.Empty;
                            quote = "'";
                            break;
                    }

                    if (resoRecord != null)
                    {
                        if (!String.IsNullOrEmpty(expandMedia))
                        {
                            expandMedia = "?" + expandMedia;
                        }

                        if (context.DataProvider == DataProviders.MlsGrid || context.DataProvider == DataProviders.Spark)
                        {
                            return $"{resoRecord.Id}{expandMedia}";
                        }

                        return $"{versionPart}{feed.Resource}{classPart}({quote}{resoRecord.Record.ListingKey}{quote}){expandMedia}";
                    }
                    else
                    {
                        if (!String.IsNullOrEmpty(expandMedia))
                        {
                            expandMedia = "&" + expandMedia;
                        }

                        switch (context.DataProvider)
                        {
                            case DataProviders.Trestle:
                            case DataProviders.Rapattoni:
                                return $"{versionPart}{feed.Resource}?Class={feed.Class}&$filter={listingFilter}{expandMedia}";
                            case DataProviders.MlsGrid:
                                return $"{versionPart}{feed.Resource}{classPart}?$filter={mlsFilter}{listingFilter}{expandMedia}";
                            default:
                                return $"{versionPart}{feed.Resource}{classPart}?$filter={listingFilter}{expandMedia}";
                        }
                    }

                    throw new ArgumentException($"Could not find listing '{propertyListingId}'", "propertyListingId");
            }

            throw new InvalidProtocolException($"Unsupported protocol '{feed.ProtocolType.ToString()}'");
        }

        private string GetAgentQuery(MultipleListingService mls, DataFeed feed, string mlsAgentId)
        {
            switch (feed.ProtocolType)
            {
                case DataFeedProtocol.RETS:
                    Class c = MetadataManagerClient.GetClass(mls.Id, mls.RETSMetadataVersion, feed.Resource, feed.Class, feed.ClassName);

                    String field = c.Table.GetAgentIDField();
                    return "(" + field + "=" + mlsAgentId + ")";
                case DataFeedProtocol.RESOWebApi:
                    // TODO:
                    //RESORecord resoRecord;
                    //SearchCriteria<RESORecord> criteria = new SearchCriteria<RESORecord>(SearchResultSizes.One);
                    //criteria.Index = SearchIndex.RESOListings;
                    //criteria.Filters.Add(Filter<RESORecord>.Term("record.ListingId", propertyListingId.ToLower()));

                    //MlsSynchronizationContext context = MlsSynchronizationContextProvider.Get(mls.Id);
                    //criteria.Filters.Add(Filter<RESORecord>.Term(context.DataProviderMlsIdFieldName, context.DataProviderMlsId));

                    //string classPart = feed.Class == "All" ? String.Empty : feed.Class;
                    //resoRecord = SearchRepository.Get(criteria).Documents.FirstOrDefault();
                    //if (resoRecord != null)
                    //{
                    //    bool useQuotes = true;
                    //    string expandMedia = String.Empty;
                    //    if (context.DataProvider == DataProviders.UtahRealEstate)
                    //    {
                    //        useQuotes = false;
                    //        expandMedia = "?$expand=Media";
                    //    }

                    //    string quote = useQuotes ? "'" : String.Empty;
                    //    return $"{feed.Resource}{classPart}({quote}{resoRecord.Record.ListingKey}{quote}){expandMedia}";
                    //}
                    //else
                    //{
                    //    propertyListingId = context.DataProviderListingPrefix != null && propertyListingId.StartsWith(context.DataProviderListingPrefix) ? propertyListingId : context.DataProviderListingPrefix + propertyListingId;
                    //    if (context.DataProvider == DataProviders.Trestle)
                    //    {
                    //        return $"{feed.Resource}?Class={feed.Class}&$filter={context.DataProviderListingIdFieldName} eq '{propertyListingId}'";
                    //    }
                    //    else if (context.DataProvider == DataProviders.Rapattoni)
                    //    {
                    //        return $"{feed.Resource}?Class={feed.Class}&$filter={context.DataProviderListingIdFieldName} eq '{propertyListingId}'&$expand=PropertyPictures";
                    //    }
                    //    else
                    //    {
                    //        return $"{feed.Resource}{classPart}?$filter={context.DataProviderListingIdFieldName} eq '{propertyListingId}'";
                    //    }
                    //}

                    //throw new ArgumentException($"Could not find listing '{propertyListingId}'", "propertyListingId");
                    break;
            }

            throw new InvalidProtocolException($"Unsupported protocol '{feed.ProtocolType.ToString()}'");
        }

#endregion
    }
}
