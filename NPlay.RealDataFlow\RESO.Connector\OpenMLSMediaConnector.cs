﻿using MLS.Synchronization.Abstract;
using MLS.Synchronization.Models;
using NPlay.Common.Abstract;
using NPlay.Common.Models;
using RESO.Connector.Abstract;
using RESO.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace RESO.Connector
{
    public class OpenMLSMediaConnector : MediaConnector, IOpenMLSMediaConnector
    {
        public OpenMLSMediaConnector(IMlsSynchronizationContextProvider mlsSynchronizationContextProvider,
                                    ISearchRepository searchRepo,
                                    IImageStreamDownloader imageStreamDownloader,
                                    IRepository<RESOCredential> resoCredentialRepository,
                                    ILogger logger) : base(mlsSynchronizationContextProvider, resoCredentialRepository, logger)
        {
        }

        public override async Task<List<MlsImage>> GetImagesAsync(string query, string mlsId, string destinationFolder)
        {
            SetCredentials(mlsId);
            List<MlsImage> result = new List<MlsImage>();
            await Task.Run(() =>
            {
                dynamic response = Get<dynamic>(query);
                List<string> imagesProcessed = new List<string>();
                var mediaItems = response["value"];
                foreach (dynamic mediaItem in response["value"])
                {
                    try
                    {
                        string url = mediaItem["MediaURL"];

                        // Avoid processing duplicate images that were in the response.
                        if (imagesProcessed.Contains(url))
                            continue;

                        if (url.StartsWith("//")) url = $"https:{url}";

                        imagesProcessed.Add(url);
                        Uri uri = new Uri(url);
                        var fileName = uri.Segments[uri.Segments.Length - 1];
                        string extension = Path.GetExtension(fileName);
                        if (String.IsNullOrEmpty(extension))
                            fileName += ".jpg";

                        string destinationFilePath = Path.Combine(destinationFolder, fileName);

                        MlsImage mlsImage = new MlsImage();
                        mlsImage.ObjectID = mediaItem.ContainsKey("Order") ? mediaItem["Order"].ToString() : null;
                        mlsImage.ContentID = mediaItem.ContainsKey("MediaKey") ? mediaItem["MediaKey"].ToString() : null;
                        mlsImage.ContentType = mediaItem.ContainsKey("MediaType") ? mediaItem["MediaType"].ToString() : null;
                        mlsImage.Preferred = mlsImage.ObjectID == "0";
                        mlsImage.Description = mediaItem.ContainsKey("LongDescription") ? mediaItem["LongDescription"]?.ToString() : null;
                        mlsImage.RemotePath = url;
                        mlsImage.SourceLocation = url;

                        result.Add(mlsImage);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("Unable to download image.", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, mlsId) }, ex, this, "GetImagesAsync");
                    }
                }

                // Media category handling
                result = RESOMediaFilter.FilterByImageContentTypes(result);
            });

            return result;
        }
    }
}
