﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <section name="messaging" type="NPlay.Common.Messaging.MessagingConfiguration" />
  </configSections>
  <system.diagnostics>
    <trace autoflush="true" indentsize="4">
      <listeners>
        <!--<add name="ESLog" type="NPlay.Common.Search.ElasticSearchTraceListener,NPlay.Common.Search" />-->
      </listeners>
    </trace>
  </system.diagnostics>
  <connectionStrings>
    <!--<add name="NPlayDb" connectionString="server=testsql01.home.asap;database=nplay_test;user id=sync;password=*******$9;MultipleActiveResultSets=True" providerName="System.Data.SqlClient" />-->
    <add name="NPlayDb" connectionString="server=sql.home.asap;database=nplay;user id=sync;password=*******$9" providerName="System.Data.SqlClient" />
    <!--<add name="NPlayActivityDb" connectionString="server=nplaytest;database=nplay_activity_debug;user id=NPlayWebUser;password=****************;MultipleActiveResultSets=True" providerName="System.Data.SqlClient" />-->
    <add name="NPlayActivityDb" connectionString="server=testsql01.home.asap;database=nplay_activity_test;user id=sync;password=*******$9;MultipleActiveResultSets=True" providerName="System.Data.SqlClient" />
    <!-- <add name="NPlayDb" connectionString="server=nplaytest;database=nplay_debug;user id=NPlayWebUser;password=****************;MultipleActiveResultSets=True" providerName="System.Data.SqlClient" /> -->
  </connectionStrings>
  <appSettings>
    <!--<add key="RabbitMQHost" value="nplaytest"/>
    <add key="RabbitMQPassword" value="password"/>
    <add key="RabbitMQUserName" value="cbrown"/>-->
    <!--<add key="ESConnectionString" value="http://es3.n-play.com:9200/"/>-->
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="ProtocolOfRead1.0" value="https://dev.fb.com/" />
    <add key="vs:EnableBrowserLink" value="false" />
    <add key="Facebook:CreatePageUrl" value="https://www.facebook.com/pages/create" />
    <add key="Facebook:MobileCreatePageUrl" value="https://m.facebook.com/pages/create" />
    <add key="NewRelic.AppName" value="NPlay.Services.NPlayApi" />
    <add key="base_uri" value="https://api.createsend.com/api/v3.1" />
    <add key="base_oauth_uri" value="https://api.createsend.com/oauth" />
    <!--<add key="FacebookApiService" value="http://dev.fb.com/NPlay.Services.FacebookApi/"/>-->
    <!--<add key="ESConnectionString" value="http://testes01.home.asap:9200/" />-->
    <!--
      ADD ANY PUBLIC API KEYS HERE
      Apis can be authorized for any api category by using a combination of the [ApiKeyAuthorize("{api-category}")] attribute, and key/value pairs configured here
      The naming convention is:
        <add key="api_key_{api-category}_{clientid}" value="{api-key}" />
      Callers can access these restricted APIs by specifying a valid clientid and apikey in the query string as follows:
      https://api.homeasap.com/<path-to-api>?clientid={clientid}&apikey={apikey}
      
      EXAMPLE:
        <add key="api_key_leadaccelerator_azure-leadaccelerator" value="aa487e01-e147-4bbf-85ab-0e22fed8aa69" />
        POST to:
        http://dev.fb.com/NPlay.Services.NPlayApi/api/leadaccelerator/3/activate?clientid=azure-leadaccelerator&apikey=aa587e01-e147-4bbf-85ab-0e22fed8aa69
    -->
    <add key="api_key_videomaker_video-maker-server" value="7def6b24-b73b-4f5d-9ffc-7246bf3a25ce" />
    <add key="api_key_agentlistingsite_azure-agentlistingsite" value="9ab7ec53-6b83-48ef-9599-0f3bc5af392a" />
    <add key="api_key_zoho_sql-clr-sproc-zoho-sync" value="0ae50cfe-491a-4f44-9255-03e851edcb03" />
    <add key="api_key_internal-tasks_marketing" value="b66bc69d-286e-4ead-b34e-79310cb967f7" />
    <add key="api_key_listing-thumbnails_rateplug" value="2846f0dc-ee4a-4278-90b3-5e8ab188bc72" />
    <add key="api_key_internal-tasks_dev" value="4049c542-9fed-4f6c-9556-29f85b264f83" />
  </appSettings>
  <messaging eventFolder="C:\NPlayEvents" persistAllMessages="false" persistMessagesOnException="true" />
  <log4net>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="C:\NPlayLogs\NPlayApi\NPlayApi.txt" />
      <appendToFile value="true" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="5" />
      <maximumFileSize value="5MB" />
      <staticLogFileName value="true" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date{G} - [%thread] %-5level %logger - %message%newline" />
      </layout>
    </appender>
    <root>
      <level value="INFO" />
      <!--<appender-ref ref="ConsoleAppender" />-->
      <appender-ref ref="RollingFileAppender" />
      <!--<appender-ref ref="ADONetAppender" />-->
    </root>
  </log4net>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5.2" />
      </system.Web>   
  -->
  <system.web>
    <machineKey compatibilityMode="Framework45" validationKey="53D094B9BF7B769396BEB46AE7C4F2826B7C8081848A4FE090D255598D9A7B87ADF758672EF98126D9EB6884568C05981DF64294B2CA604F762C95EAB5277958" decryptionKey="AEF76777251A726EBEEF81A9CAF3B65359CD192CAACD37C2" validation="SHA1" />
    <sessionState mode="Off" />
    <authentication mode="Forms">
      <forms name=".NPlay" domain="dev.fb.com" />
    </authentication>
    <httpRuntime targetFramework="4.8" maxUrlLength="4096" maxQueryStringLength="4096" requestValidationMode="2.0" />
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      </assemblies>
    </compilation>
    <customErrors mode="Off" />
  </system.web>
  <!--<system.net>
    <defaultProxy>
      <proxy
          autoDetect="false"
          bypassonlocal="false"
          proxyaddress="http://127.0.0.1:8888"
          usesystemdefault="false" />
    </defaultProxy>  
  </system.net>-->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http.WebHost" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="AutoMapper" publicKeyToken="BE96CD2C38EF1005" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="PagedList" publicKeyToken="abbb863e9397c5e1" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.17.0.0" newVersion="1.17.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Ninject" publicKeyToken="c7192dc5380945e7" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.3.4.0" newVersion="3.3.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Ninject.Extensions.Factory" publicKeyToken="c7192dc5380945e7" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.3.3.0" newVersion="3.3.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Castle.Core" publicKeyToken="407dd0808d44fbdc" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.2.0.0" newVersion="3.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages.Deployment" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Edm" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.6.2.0" newVersion="5.6.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.OData" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.6.2.0" newVersion="5.6.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Spatial" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.6.2.0" newVersion="5.6.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.29.0" newVersion="4.2.29.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <!--<dependentAssembly>
        <assemblyIdentity name="GoogleAnalyticsTracker.Core" publicKeyToken="04ab204e84b117c0" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.10.0" newVersion="4.1.10.0" />
      </dependentAssembly>-->
      <dependentAssembly>
        <assemblyIdentity name="RabbitMQ.Client" publicKeyToken="89e7d7c5feba84ce" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.6.1.0" newVersion="3.6.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis.PlatformServices" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.16.0.0" newVersion="1.16.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.16.0.0" newVersion="1.16.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Google.Apis.Core" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.16.0.0" newVersion="1.16.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.7.0" newVersion="2.0.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Services.Client" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.8.1.0" newVersion="5.8.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Azure.KeyVault.Core" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Ninject.Web.Common" publicKeyToken="c7192dc5380945e7" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.3.2.0" newVersion="3.3.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Ninject.Web.WebApi" publicKeyToken="c7192dc5380945e7" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.3.0.0" newVersion="3.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.4.1" newVersion="4.0.4.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Fabric" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="basicHttpEndpoint" closeTimeout="00:03:00" openTimeout="00:03:00" receiveTimeout="00:03:00" sendTimeout="00:03:00" bypassProxyOnLocal="false" maxReceivedMessageSize="196608" />
        <binding name="EchoSignDocumentService19HttpBinding" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxReceivedMessageSize="65536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="Transport">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
        <binding name="EchoSignDocumentService19HttpBinding1" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxReceivedMessageSize="65536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
          <security mode="None">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" algorithmSuite="Default" />
          </security>
        </binding>
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="https://secure.echosign.com/services/EchoSignDocumentService19" binding="basicHttpBinding" bindingConfiguration="EchoSignDocumentService19HttpBinding" contract="EchoSignService.EchoSignDocumentService19PortType" name="EchoSignDocumentService19HttpPort" />
    </client>
  </system.serviceModel>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="v11.0" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxQueryString="4096" />
      </requestFiltering>
    </security>
    <handlers>
      <remove name="WebDAV" />
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <modules>
      <add name="BasicAuthHttpModule" type="NPlay.Common.Web.BasicAuthHttpModule, NPlay.Common.Web" />
    </modules>
    <rewrite>
      <rules>
        <rule name="Catalog Centroid Rewrite" stopProcessing="true">
          <match url="api\/catalog\/getcatalogforcentroid\/(\d+)\/(-?\d+(\.\d+)?)\/(-?\d+(\.\d+)?)\/(\d+).csv$" negate="false" />
          <action type="Rewrite" url="api/catalog/getcatalogforcentroid/{R:1}/{R:2}/{R:4}/{R:6}" appendQueryString="true" logRewrittenUrl="true" />
        </rule>
        <rule name="Catalog Rewrite" stopProcessing="true">
          <match url="api\/catalog\/getcatalog\/(\d+)\.csv$" negate="false" />
          <action type="Rewrite" url="api/catalog/getcatalog/{R:1}" appendQueryString="true" logRewrittenUrl="true" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>