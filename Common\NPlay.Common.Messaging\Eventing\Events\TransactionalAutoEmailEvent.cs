﻿using NPlay.Common.Messaging.Models;
using System.Collections.Generic;
using NPlay.Common.Messaging.Retry;
using NPlay.Common.Models.Emails.Engine;

namespace NPlay.Common.Messaging.Events
{
    public class TransactionalAutoEmailEvent : Event<AutoEmail>
    {
        public override string Name
        {
            get
            {
                return "TransactionalAutoEmailTEST";
            }
        }

        protected override void RegisterMappings()
        {
            EventMappings.Instance.RegisterMapping(this.GetType(), new List<TargetTypes>() { TargetTypes.GuaranteedDelivery });
        }

        protected override void RegisterRetryPolicy()
        {
            EventMappings.Instance.RegisterRetryPolicy(this.GetType(), new ExponentialBackoffPolicy<TransactionalAutoEmailEvent>());
        }
    }
}
