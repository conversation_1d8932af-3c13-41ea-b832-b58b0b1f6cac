﻿using NPlay.BusinessLogic.FacebookWebhook;
using NPlay.Common.Abstract;
using NPlay.Common.FacebookModels;
using NPlay.Common.Models.Services;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Script.Serialization;

namespace NPlay.Services.NPlayApi.Controllers.Webhooks
{
    public class FacebookController : ApiController
    {
        private readonly IFacebookWebhookUserUpdateProcessor UserUpdateProcessor;
        private readonly IFacebookWebhookPageUpdateProcessor PageUpdateProcessor;
        private readonly IFacebookWebhookPermissionUpdateProcessor PermissionUpdateProcessor;
        private readonly IFacebookWebhookDeauthorizeProcessor DeauthorizeProcessor;
        private readonly ILogger Logger;

        public FacebookController(
            IFacebookWebhookUserUpdateProcessor userUpdateProcessor,
            IFacebookWebhookPageUpdateProcessor pageUpdateProcessor,
            IFacebookWebhookPermissionUpdateProcessor permissionUpdateProcessor,
            IFacebookWebhookDeauthorizeProcessor deauthorizeProcessor,
            ILogger logger
            )
        {
            UserUpdateProcessor = userUpdateProcessor;
            PageUpdateProcessor = pageUpdateProcessor;
            PermissionUpdateProcessor = permissionUpdateProcessor;
            DeauthorizeProcessor = deauthorizeProcessor;
            Logger = logger;
        }

        [FacebookSubscriptionVerify]
        [Route("api/webhooks/facebook")]
        public HttpResponseMessage Get([FromUri]SubscriptionContext hub)
        {
            var resp = new HttpResponseMessage(HttpStatusCode.OK);
            resp.Content = new StringContent(hub.Challenge, System.Text.Encoding.UTF8, "text/plain");
            return resp;
        }

        [FacebookSubscriptionVerify]
        [Route("api/webhooks/facebook")]
        public IHttpActionResult Post(object result)
        {
            const string methodName = "Post";
            var serializer = new JavaScriptSerializer();
            var resultString = result == null ? "null" : result.ToString();
            Logger.Info(string.Format("Post was called with this result: {0}", resultString), null, this, methodName);
            var notification = serializer.Deserialize<ChangeNotification>(resultString);

            if (notification != null)
            {
                if (notification.Object.Equals("user"))
                {
                    Logger.Info(string.Format("User update was notified. result: {0}", resultString), null, this, methodName);
                    UserUpdateProcessor.ProcessUserUpdates(notification);
                }
                else if (notification.Object.Equals("page"))
                {
                    Logger.Info(string.Format("Page update was notified. result: {0}", resultString), null, this, methodName);
                    PageUpdateProcessor.ProcessPageUpdates(notification);
                }
                else if (notification.Object.Equals("permissions"))
                {
                    Logger.Info(string.Format("Permissions update was notified. result: {0}", resultString), null, this, methodName);
                    PermissionUpdateProcessor.ProcessPermissionUpdates(notification);
                }
                else
                    Logger.Info(string.Format("Update of other type was notified. result: {0}", resultString), null, this, methodName);
            }
            else
                Logger.Error(string.Format("ChangeNotification is null. result: {0}", resultString), null, this, methodName);

            return Ok(true);
        }



        [HttpPost]
        [Route("api/webhooks/facebook/AppUninstalled")]
        public IHttpActionResult AppUninstalled([FromUri]string appId, [FromBody]RequestBodySignedRequest body)
        {
            Logger.Info(string.Format("AppUninstalled was called. appId: {0}, signedRequest: {1}", appId, body.signed_request), null, this, "AppUninstalled");
            DeauthorizeProcessor.ProcessDeauthorization(appId, body.signed_request);
            return Ok(true);
        }

        [HttpPost]
        [Route("api/webhooks/facebooktest")]
        public IHttpActionResult Test([FromBody] object payload)
        {
            const string methodName = "Test";
            Logger.Info("FacebookTest endpoint was called", null, this, methodName);

            var serializer = new JavaScriptSerializer();
            var payloadString = payload == null ? "null" : payload.ToString();
            var notification = serializer.Deserialize<ChangeNotification>(payloadString);

            if (notification != null)
            {
                Logger.Info("Processing test leadgen notification", null, this, methodName);
                PageUpdateProcessor.ProcessPageUpdates(notification);
                return Ok(true);
            }
            else
            {
                Logger.Error("Failed to deserialize test leadgen notification", null, this, methodName);
                return BadRequest("Failed to deserialize notification");
            }
        }
    }
}