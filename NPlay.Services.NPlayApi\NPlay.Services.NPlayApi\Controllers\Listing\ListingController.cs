using AutoMapper;
using Google.Apis.Analytics.v3.Data;
using Microsoft.Ajax.Utilities;
using MLS.Synchronization.Abstract;
using MLS.Synchronization.Models;
using Nest;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPlay.BusinessLogic.Asap;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.Models;
using NPlay.Common.Models.Emails.Engine;
using NPlay.Common.Models.Extensions;
using NPlay.Common.Models.Services;
using NPlay.Common.Models.Services.Listing;
using NPlay.Common.Mortgages.Abstract;
using NPlay.Common.Mortgages.Extensions;
using NPlay.Common.ServiceAgents.ListingReprocessor;
using NPlay.Common.Services.Abstract;
using NPlay.Common.Web.Abstract;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Device.Location;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using WebGrease.Css.Extensions;

namespace NPlay.Services.NPlayApi.Controllers
{
    public class ListingController : ApiController
    {
        ISearchRepository SearchRepository;
        IActiveListingRepository ActiveListingRepository;
        ListingViewController ListingViewController;
        IRepository<MultipleListingService> MlsRepository;
        IMlsSynchronizationContextProvider MlsSynchronizationContextProvider;
        IRepository<Agent> AgentRepository;
        IBrowserExporter BrowserExporter;
        IMortgageCalculator MortgageCalculator;
        IRatePlugClient RatePlugClient;
        IMapper Mapper;

        public ListingController(ISearchRepository searchRepo,
            IActiveListingRepository activeListingRepository,
            ListingViewController listingViewController,
            IRepository<MultipleListingService> mlsRepo,
            IMlsSynchronizationContextProvider mlsSynchronizationContextProvider,
            IRepository<Agent> agentRepo,
            IBrowserExporter browserExporter,
            IMortgageCalculator mortgageCalculator,
            IRatePlugClient ratePlugClient,
            IMapper mapper)
        {
            SearchRepository = searchRepo;
            ActiveListingRepository = activeListingRepository;
            ListingViewController = listingViewController;
            MlsRepository = mlsRepo;
            MlsSynchronizationContextProvider = mlsSynchronizationContextProvider;
            AgentRepository = agentRepo;
            BrowserExporter = browserExporter;
            MortgageCalculator = mortgageCalculator;
            RatePlugClient = ratePlugClient;
            Mapper = mapper;
        }

        /// <summary>
        /// Search for top fify listings closest to the defined
        /// center point and inside the specified bounding box.
        /// </summary>
        /// <param name="LatitudeTop">Latitude of bounding box top left corner</param>
        /// <param name="LongitudeTop">Longitude of bounding box top left corner</param>
        /// <param name="LatitudeBottom">Latitude of bounding box lower right corner</param>
        /// <param name="LongitudeBottom">Longitude of bounding box lower right corner</param>
        /// <param name="LatitudeCenter">Latitude of center point for sort by distance.</param>
        /// <param name="LongitudeCenter">Longitude of center point for sort by distance.</param>
        /// <param name="Filters">Optional Params:
        ///     lt PriceLessThan
        ///     gt PriceGreaterThan
        ///     bd MinBeds
        ///     bt MinBaths
        ///     ny MinYear
        ///     xy MaxYear
        ///     nl MinLotSizeAcres (computed)
        ///     xl MaxLotSizeAcres (computed)
        ///     nq MinLivingSqft
        ///     xq MaxLivingSqft
        ///     tp PropertyType array
        ///     ml MlsId
        ///     ma MlsAgentId
        ///     st ListingSortType
        ///     ct CountPerPage
        ///     pg PageNumber
        ///     xg MaxGarage
        ///     ng MinGarage
        ///     att Full Text
        ///     oh Open House
        ///     xs Max Stories
        ///     ns Min Stories
        ///     sa SaleType
        ///     </param>
        [HttpGet]
        [Route("api/listings/search/{LatitudeTop}/{LongitudeLeft}/{LatitudeBottom}/{LongitudeRight}/{LatitudeCenter}/{LongitudeCenter}")]
        public IHttpActionResult SearchClosest(string LatitudeTop,
                                                string LongitudeLeft,
                                                string LatitudeBottom,
                                                string LongitudeRight,
                                                string LatitudeCenter,
                                                string LongitudeCenter,
                                                [FromUri]ListingSearchRequestModel Filters)
        {
            BoundingBox boundingBox;
            GeoCoordinate centerPoint;

            // validate  inputs
            if (!(BoundingBox.IsValidBoundingBox(LatitudeTop, LongitudeLeft, LatitudeBottom, LongitudeRight, out boundingBox) &&
                  IsValidCenterPoint(LatitudeCenter, LongitudeCenter, out centerPoint)))
            {
                return BadRequest("Invalid Coordinates");
            }

            // set filters
            Filters = Filters == null ? new ListingSearchRequestModel() : Filters;
            Filters.pg = 1;
            Filters.ct = 50;

            var filters = GetFiltersForMap(Filters);
            BasicListingModelResult result = new BasicListingModelResult();

            if (!filters.NoListings)
            {
                AddMlsOfficeIdFilterIfNeeded(filters);

                var data = SearchRepository.BoundingBox<MLSPropertyListing>(GetListingsIndex(filters), l => l.Location, boundingBox, centerPoint,
                    true, filters.PageNumber, filters.CountPerPage, filters.Filters, filters.Queries, filters.ContainsCriteria, filters.SortList, filters.BoostingQueries);
                var docs = data.Documents.AsQueryable();

                docs = ApplyMortgageFilters(docs, Filters);

                if (filters.IsAgentIdOfficeIdSearch)
                    docs = SortForAgentIdOfficeIdSearch(docs, filters.MlsAgentId);

                int count = Filters.UseExpandedSearch ? docs.Count() : (int)data.Total;
                result.NumResults = count;
                result.Results = Finalize(docs);
            }

            return Ok(result);
        }

        private string GetListingsIndex(ListingFilters filters)
        {
            string index = SearchIndex.Listings;
            if (!String.IsNullOrEmpty(filters.MlsId))
            {
                try
                {
                    index = Task.Run(() => ActiveListingRepository.GetListingWriteIndex(filters.MlsId)).Result;
                }
                catch (Exception)
                {
                    // Intentionally left blank in case the service that gets the listing write index is down for some reason
                    // We will just return the star pattern so that searches still work
                }
            }

            return index;
        }

        private IQueryable<MLSPropertyListing> ApplyMortgageFilters(IQueryable<MLSPropertyListing> listings, ListingSearchRequestModel Filters)
        {
            PopulateMortgagePaymentInfo(listings, Filters);
            PopulateSpecialFinancePrograms(listings, Filters);
            return FilterByMonthlyPaymentRange(listings, Filters);
        }

        private void PopulateSpecialFinancePrograms(IEnumerable<MLSPropertyListing> listings, ListingSearchRequestModel Filters)
        {
            if (Filters.SpecialFinanceVALoanLimit.HasValue && Filters.DownPayment.HasValue)
            {
                listings.ForEach(l =>
                {
                    if (l.ListPrice.HasValue
                        && !l.SpecialFinancePrograms.Contains("VA")
                        && (l.ListPrice.Value - Filters.DownPayment.Value) <= Filters.SpecialFinanceVALoanLimit)
                    {
                        l.SpecialFinancePrograms.Add("VA");
                    }
                });
            }

            if (Filters.DownPayment.HasValue)
            {
                var fhaLoanLimitsByCounty = RatePlugClient.GetFHALoanLimits();
                listings.ForEach(l =>
                {
                    string countyKey = $"{l.StateCode}-{l.CleanCountyCode()}";
                    if (l.ListPrice.HasValue
                        && !l.SpecialFinancePrograms.Contains("FHA")
                        && fhaLoanLimitsByCounty.ContainsKey(countyKey))
                    {
                        var fhaLoanLimit = fhaLoanLimitsByCounty[countyKey];
                        bool fhaEligible = false;
                        switch (l.PropertyType)
                        {
                            case PropertyType.SingleFamily:
                                double? sfLoanLimit = fhaLoanLimit.FHALoanLimitSingle;
                                if (sfLoanLimit.HasValue && (l.ListPrice - Filters.DownPayment.Value) <= sfLoanLimit)
                                {
                                    fhaEligible = true;
                                }
                                break;
                            case PropertyType.MultiFamily:
                                // TODO: Restore multi-family FHA eligibility when we are able to differentiate between 2, 3, and 4 unit MF properties
                                //double? mfLoanLimit = fhaLoanLimit.FHALoanLimitMulti;
                                //if (mfLoanLimit.HasValue && (l.ListPrice - Filters.DownPayment.Value) <= mfLoanLimit)
                                //{
                                //    fhaEligible = true;
                                //}
                                break;
                        }

                        if (fhaEligible)
                        {
                            l.SpecialFinancePrograms.Add("FHA");
                        }
                    }
                });
            }

            listings.ForEach(l =>
            {
                if (l.Tags.Any(t => t.Name?.Contains("USDAEligible__True") == true))
                {
                    l.SpecialFinancePrograms.Add("USDA");
                }

                if (l.Tags.Any(t => t.Name?.Contains("AssumableEligible__True") == true))
                {
                    l.SpecialFinancePrograms.Add("Assumable");
                }

                if (l.Tags.Any(t => t.Name?.Contains("FHACondoEligible__True") == true))
                {
                    l.SpecialFinancePrograms.Add("FHACondo");
                }
            });
        }

        private void PopulateMortgagePaymentInfo(IEnumerable<MLSPropertyListing> listings, ListingSearchRequestModel Filters)
        {
            if (Filters.InterestRate.HasValue && (Filters.DownPayment.HasValue || Filters.DownPaymentPercentage.HasValue) && Filters.NumberOfMonths.HasValue)
            {
                if (Filters.DownPayment.HasValue)
                {
                    MortgageCalculator.PopulateMortgagePaymentInfo(listings, Filters.DownPayment.Value, Filters.InterestRate.Value, Filters.NumberOfMonths.Value);
                }
                else if (Filters.DownPaymentPercentage.HasValue)
                {
                    MortgageCalculator.PopulateMortgagePaymentInfo(listings, Filters.DownPaymentPercentage.Value, Filters.InterestRate.Value, Filters.NumberOfMonths.Value);
                }
            }
            else if (Filters.SpecialFinancePrograms?.Any(sfp => sfp.ToLower() == "assumable") == true)
            {
                MortgageCalculator.PopulateAssumableMortgagePaymentInfo(listings);
            }
        }

        private void PopulateMortgagePaymentInfo(MLSPropertyListing listing, ListingSearchRequestModel Filters)
        {
            if (Filters != null && Filters.InterestRate.HasValue && (Filters.DownPayment.HasValue || Filters.DownPaymentPercentage.HasValue) && Filters.NumberOfMonths.HasValue)
            {
                List<MLSPropertyListing> singleListingList = new List<MLSPropertyListing>();
                singleListingList.Add(listing);
                if (Filters.DownPayment.HasValue)
                {
                    MortgageCalculator.PopulateMortgagePaymentInfo(singleListingList, Filters.DownPayment.Value, Filters.InterestRate.Value, Filters.NumberOfMonths.Value);
                }
                else if (Filters.DownPaymentPercentage.HasValue)
                {
                    MortgageCalculator.PopulateMortgagePaymentInfo(singleListingList, Filters.DownPaymentPercentage.Value, Filters.InterestRate.Value, Filters.NumberOfMonths.Value);
                }
            }
        }

        private IQueryable<MLSPropertyListing> FilterByMonthlyPaymentRange(IQueryable<MLSPropertyListing> listings, ListingSearchRequestModel Filters)
        {
            var filteredListings = listings;
            if (Filters.LowPayment.HasValue && Filters.HighPayment.HasValue)
            {
                filteredListings = listings.Where(l => l.MortgagePaymentInfo != null && l.MortgagePaymentInfo.TotalPayment >= Filters.LowPayment.Value && l.MortgagePaymentInfo.TotalPayment <= Filters.HighPayment.Value).AsQueryable();
            }

            return filteredListings;
        }

        /// <summary>
        /// Search for listings within a provided distance from a point
        /// </summary>
        /// <param name="Latitude">Centroid Latitude</param>
        /// <param name="Longitude">Centroid Longitude</param>
        /// <param name="Distance">Distance from centroid in m or km. Example: 6200m / 6.2km</param>
        /// <param name="Filters">Optional Params:
        ///     lt PriceLessThan
        ///     gt PriceGreaterThan
        ///     bd MinBeds
        ///     bt MinBaths
        ///     ny MinYear
        ///     xy MaxYear
        ///     nl MinLotSizeAcres (computed)
        ///     xl MaxLotSizeAcres (computed)
        ///     nq MinLivingSqft
        ///     xq MaxLivingSqft
        ///     tp PropertyType array
        ///     ml MlsId
        ///     ma MlsAgentId
        ///     st ListingSortType
        ///     ct CountPerPage
        ///     pg PageNumber
        ///     xg MaxGarage
        ///     ng MinGarage
        ///     att Full Text
        ///     oh Open House
        ///     xs Max Stories
        ///     ns Min Stories
        ///     sa SaleType
        ///     rm Read Members
        ///     ci City
        ///     zi ZipCode
        ///     </param>
        /// <returns></returns>
        [HttpGet]
        [Route("api/listings/search/{Latitude}/{Longitude}/{Distance}")]
        public IHttpActionResult SearchDistance(string Latitude,
                                                string Longitude,
                                                string Distance,
                                                [FromUri]ListingSearchRequestModel Filters)
        {
            double lat, lon;

            var parseLat = double.TryParse(Latitude, out lat);
            var parseLon = double.TryParse(Longitude, out lon);

            if (!(parseLat &&
                  parseLat))
                return BadRequest("Invalid Coordinates");

            if (string.IsNullOrWhiteSpace(Distance) || Distance == "0m" || Distance == "0km" || (!Distance.EndsWith("m") && !Distance.EndsWith("km")))
                return BadRequest();

            var filters = GetFiltersForMap(Filters);
            BasicListingModelResult result = new BasicListingModelResult();

            if (!filters.NoListings)
            {
                AddMlsOfficeIdFilterIfNeeded(filters);

                double radiusKm = GetRadiusInKmFromDistanceString(Distance);
                int queryResultCount = filters.CountPerPage;
                if (Filters.ReadMembersOnly == true)
                {
                    radiusKm = radiusKm * 10; // Expand the search radius because we will filter the results in memory using a query for READ member matches
                    queryResultCount = queryResultCount * 5;
                }

                var data = SearchRepository.Distance<MLSPropertyListing>(GetListingsIndex(filters), l => l.Location, new GeoCoordinate(lat, lon), radiusKm, GeoUnit.Kilometers,
                    filters.SortByDistanceAsc, filters.PageNumber, queryResultCount, filters.Filters, filters.Queries, filters.ContainsCriteria, filters.SortList, filters.BoostingQueries);
                var docs = data.Documents.AsQueryable();

                if (Filters.ReadMembersOnly == true)
                {
                    docs = GetReadMemberListings(docs).Take(filters.CountPerPage);
                }

                if (filters.IsAgentIdOfficeIdSearch)
                {
                    docs = SortForAgentIdOfficeIdSearch(docs, filters.MlsAgentId);
                }

                docs = ApplyMortgageFilters(docs, Filters);

                int count = Filters.UseExpandedSearch ? docs.Count() : (int)data.Total;
                result.NumResults = count;
                result.Results = Finalize(docs);
            }

            return Ok(result);
        }

        private IQueryable<MLSPropertyListing> GetReadMemberListings(IQueryable<MLSPropertyListing> docs)
        {
            var listings = docs.ToList();
            var matchingListings = new List<MLSPropertyListing>();

            // Query SQL for matches on READ members or READ member offices
            List<string> agentIds = new List<string>();
            agentIds.AddRange(docs.Select(l => l.ListingAgent.Id).Distinct());
            agentIds.AddRange(docs.Select(l => l.CoAgent.Id).Distinct());
            agentIds = agentIds.Distinct().ToList();

            List<string> officeIds = new List<string>();
            officeIds.AddRange(docs.Select(l => l.ListingAgent.OfficeId).Distinct());
            officeIds.AddRange(docs.Select(l => l.CoAgent.OfficeId).Distinct());

            List<string> mlsIds = new List<string>();
            mlsIds.AddRange(docs.Select(l => l.MLSSource).Distinct());

            Func<MLSPropertyListing, Agent, MLSPropertyListing> populateListingAgentReadData = (l, a) =>
            {
                l.ListingAgent.ProfileImageUrl = a.FullProfileImageUrl();
                l.ListingAgent.MembershipId = a.Id;
                return l;
            };

            Func<MLSPropertyListing, Agent, MLSPropertyListing> populateCoAgentReadData = (l, a) =>
            {
                l.CoAgent.ProfileImageUrl = a.FullProfileImageUrl();
                l.CoAgent.MembershipId = a.Id;
                return l;
            };

            Func<MLSPropertyListing, Agent, MLSPropertyListing> populateOfficeAgentReadData = (l, a) =>
            {
                l.AlternativeOfficeAgent = new ListingAgentData()
                {
                    Id = a.MLSAgentId,
                    Name = a.Membership?.Name(),
                    OfficeId = a.MLSOfficeId,
                    ProfileImageUrl = a.FullProfileImageUrl(),
                    MembershipId = a.Id
                };
                return l;
            };

            var readMembers = AgentRepository.GetAllNoTracking().Where(a => mlsIds.Contains(a.MLSOrgId) && (agentIds.Contains(a.MLSAgentId) || officeIds.Contains(a.MLSOfficeId)));
            var readMembersWithAgentId = readMembers.Where(a => a.MLSAgentId != null && a.MLSAgentId.Length > 0);
            var readMembersWithOfficId = readMembers.Where(a => a.MLSOfficeId != null && a.MLSOfficeId.Length > 0);
            var listingsWithListingAgent = listings.Where(l => !String.IsNullOrWhiteSpace(l.ListingAgent?.Id));
            var listingsWithCoAgent = listings.Where(l => !String.IsNullOrWhiteSpace(l.CoAgent?.Id));
            var listingsWithListingAgentOffice = listings.Where(l => !String.IsNullOrWhiteSpace(l.ListingAgent?.OfficeId));
            var listingsWithCoAgentOffice = listings.Where(l => !String.IsNullOrWhiteSpace(l.CoAgent?.OfficeId));

            var listingAgentListings = from l in listingsWithListingAgent
                                       join a in readMembersWithAgentId
                                       on l.ListingAgent.Id.ToLower() equals a.MLSAgentId.ToLower()
                                       where l.ListingAgent != null && a.MLSAgentId != null
                                       select populateListingAgentReadData(l, a);

            matchingListings.AddRange(listingAgentListings);

            var coAgentListings = from l in listingsWithCoAgent
                                  join a in readMembersWithAgentId
                                  on l.CoAgent.Id.ToLower() equals a.MLSAgentId.ToLower()
                                  where !matchingListings.Contains(l) && l.CoAgent != null && a.MLSAgentId != null
                                  select populateCoAgentReadData(l, a);
            matchingListings.AddRange(coAgentListings);

            var listingAgentOfficeListings = from l in listingsWithListingAgentOffice
                                             join a in readMembersWithOfficId
                                             on l.ListingAgent.OfficeId.ToLower() equals a.MLSOfficeId.ToLower()
                                             where !matchingListings.Contains(l) && l.ListingAgent != null && a.MLSOfficeId != null
                                             select populateOfficeAgentReadData(l, a);
            matchingListings.AddRange(listingAgentOfficeListings);

            var coAgentOfficeListings = from l in listingsWithCoAgentOffice
                                        join a in readMembersWithOfficId
                                        on l.CoAgent.OfficeId.ToLower() equals a.MLSOfficeId.ToLower()
                                        where !matchingListings.Contains(l) && l.CoAgent != null && a.MLSOfficeId != null
                                        select populateOfficeAgentReadData(l, a);
            matchingListings.AddRange(coAgentOfficeListings);

            var nonReadMemberListings = docs.Except(matchingListings);
            foreach (var l in nonReadMemberListings)
            {
                listings.Remove(l);
            }

            return listings.AsQueryable();
        }

        /// <summary>
        /// Search for listings within a bounding box
        /// </summary>
        /// <param name="LatitudeA">Top Left Latitude</param>
        /// <param name="LongitudeA">Top Left Longitude</param>
        /// <param name="LatitudeB">Bottom Right Latitude</param>
        /// <param name="LongitudeB">Bottom Right Longitude</param>
        /// <param name="Filters">Optional Params:
        ///     lt PriceLessThan
        ///     gt PriceGreaterThan
        ///     bd MinBeds
        ///     bt MinBaths
        ///     ny MinYear
        ///     xy MaxYear
        ///     nl MinLotSizeAcres (computed)
        ///     xl MaxLotSizeAcres (computed)
        ///     nq MinLivingSqft
        ///     xq MaxLivingSqft
        ///     tp PropertyType array
        ///     ml MlsId
        ///     ma MlsAgentId
        ///     st ListingSortType
        ///     ct CountPerPage
        ///     pg PageNumber
        ///     xg MaxGarage
        ///     ng MinGarage
        ///     att Full Text
        ///     oh Open House
        ///     xs Max Stories
        ///     ns Min Stories
        ///     sa SaleType
        ///     </param>
        /// <returns></returns>
        [HttpGet]
        [Route("api/listings/search/{LatitudeA}/{LongitudeA}/{LatitudeB}/{LongitudeB}")]
        public IHttpActionResult SearchGeoBoundingBox(string LatitudeA,
                                                      string LongitudeA,
                                                      string LatitudeB,
                                                      string LongitudeB,
                                                      [FromUri]ListingSearchRequestModel Filters)
        {
            BoundingBox boundingBox;
            if (!BoundingBox.IsValidBoundingBox(LatitudeA, LongitudeA, LatitudeB, LongitudeB, out boundingBox))
                return BadRequest("Invalid Coordinates");

            var filters = GetFiltersForMap(Filters);
            BasicListingModelResult result = new BasicListingModelResult();

            if (!filters.NoListings)
            {
                AddMlsOfficeIdFilterIfNeeded(filters);

                var data = SearchRepository.BoundingBox<MLSPropertyListing>(GetListingsIndex(filters), l => l.Location, boundingBox, null,
                    filters.SortByDistanceAsc, filters.PageNumber, filters.CountPerPage, filters.Filters, filters.Queries, filters.ContainsCriteria, filters.SortList, filters.BoostingQueries);
                var docs = data.Documents.AsQueryable();

                docs = ApplyMortgageFilters(docs, Filters);

                if (filters.IsAgentIdOfficeIdSearch)
                {
                    docs = SortForAgentIdOfficeIdSearch(docs, filters.MlsAgentId);
                }

                int count = Filters.UseExpandedSearch ? docs.Count() : (int)data.Total;
                result.NumResults = count;
                result.Results = Finalize(docs);
            }

            return Ok(result);
        }

        /// <summary>
        /// Search for listings within a bounding box, but outside the perimeter
        /// of a radial area extending from defined center point.
        /// </summary>
        /// <param name="LatitudeTop">Latitude of bounding box top left corner</param>
        /// <param name="LongitudeTop">Longitude of bounding box top left corner</param>
        /// <param name="LatitudeBottom">Latitude of bounding box lower right corner</param>
        /// <param name="LongitudeBottom">Longitude of bounding box lower right corner</param>
        /// <param name="LatitudeCenter">Latitude of center point for radial exclusion.</param>
        /// <param name="LongitudeCenter">Longitude of center point for radial exclusion.</param>
        /// <param name="Distance">Distance from center poinf for radial exclusion in m or km. Example: 6200m / 6.2km</param>
        /// <param name="Filters">Optional Params:
        ///     lt PriceLessThan
        ///     gt PriceGreaterThan
        ///     bd MinBeds
        ///     bt MinBaths
        ///     ny MinYear
        ///     xy MaxYear
        ///     nl MinLotSizeAcres (computed)
        ///     xl MaxLotSizeAcres (computed)
        ///     nq MinLivingSqft
        ///     xq MaxLivingSqft
        ///     tp PropertyType array
        ///     ml MlsId
        ///     ma MlsAgentId
        ///     st ListingSortType
        ///     ct CountPerPage
        ///     pg PageNumber
        ///     xg MaxGarage
        ///     ng MinGarage
        ///     att Full Text
        ///     oh Open House
        ///     xs Max Stories
        ///     ns Min Stories
        ///     sa SaleType
        ///     </param>
        /// <returns>List of ListingLocationModel</returns>
        [HttpGet]
        [Route("api/listings/search/{LatitudeTop}/{LongitudeLeft}/{LatitudeBottom}/{LongitudeRight}/{LatitudeCenter}/{LongitudeCenter}/{Distance}")]
        public IHttpActionResult SearchGeoPerimeter(string LatitudeTop,
                                                    string LongitudeLeft,
                                                    string LatitudeBottom,
                                                    string LongitudeRight,
                                                    string LatitudeCenter,
                                                    string LongitudeCenter,
                                                    string Distance,
                                                    [FromUri]ListingSearchRequestModel Filters)
        {
            BoundingBox boundingBox;
            GeoCoordinate centerPoint;

            // validate  inputs
            if (!(BoundingBox.IsValidBoundingBox(LatitudeTop, LongitudeLeft, LatitudeBottom, LongitudeRight, out boundingBox) &&
                  IsValidCenterPoint(LatitudeCenter, LongitudeCenter, out centerPoint)))
            {
                return BadRequest("Invalid Coordinates");
            }

            if (string.IsNullOrWhiteSpace(Distance) || Distance == "0m" || Distance == "0km" || (!Distance.EndsWith("m") && !Distance.EndsWith("km")))
                return BadRequest();

            var filters = GetFiltersForMap(Filters);
            var model = new List<ListingLocationModel>();

            if (!filters.NoListings)
            {
                AddMlsOfficeIdFilterIfNeeded(filters);

                double radiusKm = GetRadiusInKmFromDistanceString(Distance);
                var data = SearchRepository.BoundingBoxWithExclusion<MLSPropertyListing>(GetListingsIndex(filters), l => l.Location, boundingBox, centerPoint, radiusKm, GeoUnit.Kilometers,
                    filters.SortByDistanceAsc, filters.PageNumber, filters.CountPerPage, filters.Filters, filters.Queries, filters.ContainsCriteria, filters.SortList, filters.BoostingQueries);
                var count = data.Total; //Total gives you total count for all the pages
                var docs = data.Documents.AsQueryable();

                docs = ApplyMortgageFilters(docs, Filters);

                if (filters.IsAgentIdOfficeIdSearch)
                {
                    docs = SortForAgentIdOfficeIdSearch(docs, filters.MlsAgentId);
                }

                model = Mapper.Map<List<ListingLocationModel>>(docs);
            }

            return Ok(model);
        }

        /// <summary>
        /// This end point is supposed to
        /// "Search for listings within a radius and return distinct MLS IDs"
        /// However, MLSPropertyListing's MlsId has been changed to MlsIds (array of string) but ListingLocationModel's MlsId (string) stayed as MlsId, therefore
        /// without specific mapping, ListingLocationModel's MlsId is always null currently.
        /// Therefore, this endpoint returns null always (even before being converted from elastic linq to Nest).
        /// This endpoint is not used currently.
        /// </summary>
        /// <param name="LatitudeTop">Latitude of center point</param>
        /// <param name="LongitudeTop">Longitude of center point</param>
        /// <param name="Distance">Radius in m or km. Example: 6200m / 6.2km</param>
        /// <returns>List of ListingLocationModel</returns>
        [HttpGet]
        [Route("api/listings/getmls/{Latitude}/{Longitude}/{Distance}")]
        public IHttpActionResult GetMls(string Latitude,
                                                string Longitude,
                                                string Distance)
        {
            double lat, lon;

            var parseLat = double.TryParse(Latitude, out lat);
            var parseLon = double.TryParse(Longitude, out lon);

            if (!(parseLat &&
                  parseLat))
                return BadRequest("Invalid Coordinates");

            if (string.IsNullOrWhiteSpace(Distance) || Distance == "0m" || Distance == "0km" || (!Distance.EndsWith("m") && !Distance.EndsWith("km")))
                return BadRequest();

            var allFilters = new List<FilterContainer>();
            FilterContainer internetFilter = Filter<MLSPropertyListing>.Term(p => p.ListOnInternet, true);
            allFilters.Add(internetFilter);

            double radiusKm = GetRadiusInKmFromDistanceString(Distance);
            var query = SearchRepository.Distance<MLSPropertyListing>(SearchIndex.Listings, l => l.Location, new GeoCoordinate(lat, lon), radiusKm, GeoUnit.Kilometers,
                    false, null, null, allFilters).Documents.AsQueryable();

            var model = Mapper.Map<List<ListingLocationModel>>(query);
            var distinctModel = model.Select(q => q.MlsId).Distinct();

            return Ok(distinctModel);
        }

        /// <summary>
        /// Matches partial search term against locations.
        /// Returns list of most common entries.
        /// </summary>
        /// <param name="term">Partial query</param>
        /// <param name="size">Number of results to return</param>
        /// <returns>List of possible matching locations</returns>
        [HttpGet]
        [Route("api/listings/search/suggestions/{term}")]
        [Route("api/listings/search/suggestions/{term}/{size}")]
        public IHttpActionResult SearchSuggestions(string term, int? size = 20)
        {
            int intSize = size ?? 20;
            var locations = SearchRepository.SearchSuggestions<MatchedPropertyLocationModel>(SearchIndex.PropertyLocations, "name", term, intSize)
                .Select(loc => new { value = loc.Key, location = loc.Value });
            return Ok(locations);
        }

        [HttpGet]
        [Route("api/listings/imagecaptions/{id}")]
        public IHttpActionResult ImageCaptions(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return BadRequest("listingid is required");

            var criteria = new SearchCriteria<ListingImageCaptionModel>(1)
            {
                Index = SearchIndex.Listings
            };
            criteria.Filters.Add(Filter<ListingImageCaptionModel>.Term(x => x.Id, id));
            var result = SearchRepository.Get(criteria).Documents.SingleOrDefault();

            return Ok(result);
        }

        /// <param name="distance">Distance in km or m. Example: 6200m / 6.2km</param>
        private double GetRadiusInKmFromDistanceString(string distance)
        {
            var radius = distance.EndsWith("km") ? double.Parse(distance.Substring(0, distance.Length - 2)) : double.Parse(distance.Substring(0, distance.Length - 1));
            double radiusKm = distance.EndsWith("km") ? radius : radius / 1000;
            return radiusKm;
        }

        /// <summary>
        /// Filters out any listings that should not display on the map
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private ListingFilters GetFiltersForMap(ListingSearchRequestModel request)
        {
            var basicFilters = GetFilters(request);
            // NPLAY-7214: Always return all listings in search results.
            // Frontend will still display on the right rail and hide the pin if display address === false.
            // basicFilters?.Filters?.Add(Filter<MLSPropertyListing>.Term(l => l.DisplayAddress, true));

            return basicFilters;
        }

        private ListingFilters GetFilters(ListingSearchRequestModel request)
        {
            var allFilters = new List<FilterContainer>();
            var allQueries = new List<QueryContainer>();
            var containsCriteria = new List<ContainsCriteria<MLSPropertyListing>>();
            var boostingQueries = new List<QueryContainer>();

            var mustNotFilters = new List<FilterContainer>();
            FilterContainer statusFilter = Filter<MLSPropertyListing>.Term(p => p.Status, "S");
            mustNotFilters.Add(statusFilter);

            if (!string.IsNullOrWhiteSpace(request.StatusNotEquals))
            {
                string[] statuses = request.StatusNotEquals.Split(new char[] { ',' });
                if (statuses.Length > 0)
                {
                    for (int i = 0; i < statuses.Length; i++)
                    {
                        mustNotFilters.Add(Filter<MLSPropertyListing>.Term(l => l.Status, statuses[i]));
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(request.StatusEquals))
            {
                string[] statuses = request.StatusEquals.Split(new char[] { ',' });
                if (statuses.Length > 1)
                {
                    FilterContainer statusFilters = null;
                    for (int i = 0; i < statuses.Length; i++)
                    {
                        if (i == 0)
                            statusFilters = Filter<MLSPropertyListing>.Term(p => p.Status, statuses[i]);
                        else
                            statusFilters |= Filter<MLSPropertyListing>.Term(p => p.Status, statuses[i]);
                    }

                    allFilters.Add(Filter<MLSPropertyListing>.Bool(b => b.Should(statusFilters)));
                }
                else
                {
                    allFilters.Add(Filter<MLSPropertyListing>.Term(l => l.Status, request.StatusEquals));
                }
            }

            FilterContainer internetFilter = Filter<MLSPropertyListing>.Term(p => p.ListOnInternet, true);
            allFilters.Add(internetFilter);

            if (!string.IsNullOrWhiteSpace(request.MlsListingId))
            {
                var upperCase = request.MlsListingId.ToUpper();
                var lowerCase = request.MlsListingId.ToLower();

                FilterContainer mlsListingIdFilter = null;

                if (lowerCase.Equals(upperCase))
                    mlsListingIdFilter = Filter<MLSPropertyListing>.Term(p => p.PropertyListingId, lowerCase);
                else
                    mlsListingIdFilter = Filter<MLSPropertyListing>.Bool(b => b
                                                      .Should(s => Filter<MLSPropertyListing>.Term(p => p.PropertyListingId, lowerCase),
                                                              s => Filter<MLSPropertyListing>.Term(p => p.PropertyListingId, upperCase)));
                allFilters.Add(mlsListingIdFilter);
            }

            var noListings = false;
            if (!string.IsNullOrWhiteSpace(request.MlsId))
            {
                string[] arr = { request.MlsId };
                containsCriteria.Add(new ContainsCriteria<MLSPropertyListing>()
                {
                    ContainsExpression = l => l.MlsIds,
                    PossibleValues = arr
                });

                if (!string.IsNullOrWhiteSpace(request.MlsAgentId))
                {
                    var mls = MlsRepository.GetAllNoTracking().SingleOrDefault(m => m.Id == request.MlsId);
                    if (mls != null && !mls.Autoimport)
                    {
                        var agent = AgentRepository.GetAllNoTracking().FirstOrDefault(a => a.MLSOrgId == request.MlsId && a.MLSAgentId == request.MlsAgentId);
                        noListings = agent == null || agent.HomeSearchRegisteredDateTime == null;
                    }
                }
            }

            if (request.MinBeds.HasValue)
            {
                QueryContainer minBedQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinBeds)
                                                                                    .OnField(x => x.Rooms.TotalBedrooms));
                allQueries.Add(minBedQuery);
            }

            if (request.MinBaths.HasValue)
            {
                QueryContainer minBathQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinBaths)
                                                                                    .OnField(x => x.Rooms.TotalBaths));
                allQueries.Add(minBathQuery);
            }

            if (request.PriceGreaterThan.HasValue)
            {
                QueryContainer minPriceQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.PriceGreaterThan)
                                                                                    .OnField(x => x.ListPrice));
                allQueries.Add(minPriceQuery);
            }

            if (request.PriceLessThan.HasValue)
            {
                QueryContainer maxPriceQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(request.PriceLessThan)
                                                                                    .OnField(x => x.ListPrice));
                allQueries.Add(maxPriceQuery);
            }

            if (request.MinYear.HasValue)
            {
                QueryContainer minYearQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinYear)
                                                                                    .OnField(x => x.YearBuilt));
                allQueries.Add(minYearQuery);
            }

            if (request.MaxYear.HasValue)
            {
                QueryContainer maxYearQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(request.MaxYear)
                                                                                   .OnField(x => x.YearBuilt));
                allQueries.Add(maxYearQuery);
            }

            if (request.MinLotSizeSqFt.HasValue)
            {
                QueryContainer minLotSizeQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinLotSizeSqFt)
                                                                                    .OnField(x => x.Exterior.LotSizeAcresComputed));
                allQueries.Add(minLotSizeQuery);
            }

            if (request.MaxLotSizeAcres.HasValue)
            {
                QueryContainer maxLotSizeQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(request.MaxLotSizeAcres)
                                                                                   .OnField(x => x.Exterior.LotSizeAcresComputed));
                allQueries.Add(maxLotSizeQuery);
            }

            if (request.MinLivingAcres.HasValue)
            {
                QueryContainer minLivingAcresQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinLivingAcres)
                                                                                    .OnField(x => x.LivingSquareFeet));
                allQueries.Add(minLivingAcresQuery);
            }

            if (request.MaxLivingSqFt.HasValue)
            {
                QueryContainer maxLivingSqFtQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(request.MaxLivingSqFt)
                                                                                   .OnField(x => x.LivingSquareFeet));
                allQueries.Add(maxLivingSqFtQuery);
            }

            if (request.PropertyType != null && request.PropertyType.Length > 0)
            {
                if (request.SaleType != SaleType.Rent)
                {
                    FilterContainer propertyTypeFilters = null;
                    for (int i = 0; i < request.PropertyType.Length; i++)
                    {
                        if (i == 0)
                            propertyTypeFilters = Filter<MLSPropertyListing>.Term(p => p.PropertyType, request.PropertyType[i]);
                        else
                            propertyTypeFilters |= Filter<MLSPropertyListing>.Term(p => p.PropertyType, request.PropertyType[i]);
                    }

                    allFilters.Add(propertyTypeFilters);
                }
            }

            if (request.SaleType.HasValue)
            {
                FilterContainer saleTypeFilter = Filter<MLSPropertyListing>.Term(x => x.SaleType, request.SaleType);
                allFilters.Add(saleTypeFilter);
            }

            if (!String.IsNullOrEmpty(request.City))
            {
                allFilters.AddRange(request.City.Split(new char[] { ' ' }).Select(token => Filter<MLSPropertyListing>.Term(x => x.Address.CityName, token)));
            }

            if (!String.IsNullOrEmpty(request.ZipCode))
            {
                allFilters.Add(Filter<MLSPropertyListing>.Term(x => x.Address.ZipCode, request.ZipCode));
            }

            if (request.MaxParking.HasValue)
            {
                QueryContainer maxParkingQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(request.MaxParking)
                                                                                   .OnField(x => x.Exterior.TotalSpaces));
                allQueries.Add(maxParkingQuery);
            }

            if (request.MinParking.HasValue)
            {
                QueryContainer minParkingQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinParking)
                                                                                    .OnField(x => x.Exterior.TotalSpaces));
                allQueries.Add(minParkingQuery);
            }

            if (request.FullText != null && request.FullText.Count() > 0)
            {
                var query = string.Join(" ", request.FullText);

                //Address, Neighborhood, School, City, Zip, Description
                QueryContainer fullTextQuery = Query<MLSPropertyListing>.MultiMatch(r => r.Query(query)
                .OnFields(f => f.Address.UnitNumber, f => f.Address.StreetAddress, f => f.Address.StreetName, f => f.Address.StreetNumber, f => f.Address.CityName,
                f => f.Address.State, f => f.Address.ZipCode, f => f.Neighborhood, f => f.Schools.Elementary, f => f.Schools.High, f => f.Schools.JuniorHigh, f => f.AgeFeatures, f => f.Description, f => f.MlsIds, f => f.PropertyListingId)
                .Operator(Operator.Or)
                );

                allQueries.Add(fullTextQuery);
            }

            if (request.OnlyOpenHouse == true)
            {
                //l.OpenHouse.Start > DateTime.Today || l.OpenHouse.End > DateTime.Today
                FilterContainer openHouseFilter = Filter<MLSPropertyListing>.Bool(b => b
                                                      .Should(s => s.Range(r => r.Greater(DateTime.Today)
                                                        .OnField(x => x.OpenHouse.Start)),
                                                              s => s.Range(r => r.Greater(DateTime.Today)
                                                        .OnField(x => x.OpenHouse.End))));

                allFilters.Add(openHouseFilter);
            }

            if (request.MinStories.HasValue)
            {
                QueryContainer minStoriesQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinStories)
                                                                                    .OnField(x => x.Interior.Levels));
                allQueries.Add(minStoriesQuery);
            }

            if (request.MaxStories.HasValue)
            {
                QueryContainer maxStoriesQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(request.MaxStories)
                                                                                   .OnField(x => x.Interior.Levels));
                allQueries.Add(maxStoriesQuery);
            }

            if (request.MinListingDate.HasValue)
            {
                QueryContainer minListingDateQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(request.MinListingDate)
                                                                                    .OnField(x => x.ListedOn));
                allQueries.Add(minListingDateQuery);
            }

            if (request.MaxListingDate.HasValue)
            {
                QueryContainer maxListingDateQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(request.MaxListingDate)
                                                                                   .OnField(x => x.ListedOn));
                allQueries.Add(maxListingDateQuery);
            }

            if (request.SpecialFinancePrograms?.Length > 0)
            {
                // Handle Assumable, FHA Condo, and USDA financing as strict filters
                if (request.SpecialFinancePrograms.Any(sf => sf.ToLower() == "assumable"))
                {
                    string assumableTag = "SpecialFinancing__AssumableEligible__True".ToLower();
                    FilterContainer assumableTagFilter = Filter<MLSPropertyListing>.Term(l => l.Tags.First().Name, assumableTag);
                    allFilters.Add(assumableTagFilter);
                }

                if (request.SpecialFinancePrograms.Any(sf => sf.ToLower() == "fhacondo"))
                {
                    string fhaCondoTag = "SpecialFinancing__FHACondoEligible__True".ToLower();
                    FilterContainer fhaCondoTagFilter = Filter<MLSPropertyListing>.Term(l => l.Tags.First().Name, fhaCondoTag);
                    allFilters.Add(fhaCondoTagFilter);
                }

                if (request.SpecialFinancePrograms.Any(sf => sf.ToLower() == "usda"))
                {
                    string usdaTag = "SpecialFinancing__USDAEligible__True".ToLower();
                    FilterContainer usdaTagFilter = Filter<MLSPropertyListing>.Term(l => l.Tags.First().Name, usdaTag);
                    allFilters.Add(usdaTagFilter);
                }


                // Handle VA financing with boosting to prioritize in results
                if (request.SpecialFinancePrograms.Any(sf => sf.ToLower() == "va") && request.SpecialFinanceVALoanLimit.HasValue && request.DownPayment.HasValue)
                {
                    // VA eligibility: ListPrice <= (VALoanLimit + DownPayment)
                    double maxVAEligibleListPrice = request.SpecialFinanceVALoanLimit.Value + request.DownPayment.Value;
                    QueryContainer vaBoostQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(maxVAEligibleListPrice)
                                                                                        .OnField(x => x.ListPrice)
                                                                                        .Boost(2.0));
                    boostingQueries.Add(vaBoostQuery);
                }

                // Handle FHA financing with boosting to prioritize in results
                if (request.SpecialFinancePrograms.Any(sf => sf.ToLower() == "fha") && request.DownPayment.HasValue)
                {
                    // FHA eligibility: PropertyType equals "SingleFamily" AND ListPrice <= (FHALoanLimit + DownPayment)
                    const double fhaLoanLimit = 600000.0; // Hard-coded FHA loan limit as specified which covers 90% of all counties limit as of 06/05/2025. Listings outside of this range still has a chance to be returned and they will be tagged correctly if they meet the FHA criteria and have a higher county loan limit.
                    double maxFHAEligibleListPrice = fhaLoanLimit + request.DownPayment.Value;
                    QueryContainer fhaBoostQuery = Query<MLSPropertyListing>.Bool(b => b
                        .Must(
                            Query<MLSPropertyListing>.Term(t => t.PropertyType, PropertyType.SingleFamily),
                            Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(maxFHAEligibleListPrice)
                                                                  .OnField(x => x.ListPrice))
                        )
                        .Boost(2.0));
                    boostingQueries.Add(fhaBoostQuery);
                }
            }

            var isAgentIdOfficeIdSearch = false;
            if (!string.IsNullOrWhiteSpace(request.MlsAgentOfficeId) && string.IsNullOrWhiteSpace(request.MlsListingId))
            {
                //Moved out the code to filter with office id since there is a case where we don't won't to filter with office id:
                //1st part of 2 queries for agentId office Id queries: to get agent's own listings, not other agents' listings in the same office
                //FilterContainer officeFilter = Filter<MLSPropertyListing>.Term(x => x.AgentOfficeId, request.MlsAgentOfficeId);
                //allFilters.Add(officeFilter);

                if (!string.IsNullOrWhiteSpace(request.MlsAgentId))
                    isAgentIdOfficeIdSearch = true;
            }
            else if (!string.IsNullOrWhiteSpace(request.MlsAgentId))
            {
                string[] arr = { request.MlsAgentId };
                containsCriteria.Add(new ContainsCriteria<MLSPropertyListing>()
                {
                    ContainsExpression = l => l.AgentIds,
                    PossibleValues = arr
                });
            }

            allFilters.Add(Filter<MLSPropertyListing>.Bool(b => b.MustNot(mustNotFilters.ToArray())));

            var sortExpressions = new List<Sort<MLSPropertyListing>>();

            if (boostingQueries.Count > 0)
            {
                sortExpressions.Add(new Sort<MLSPropertyListing> { SortOrder = SortOrder.Descending }); // Sort by _score first
            }

            Sort<MLSPropertyListing> sortExpression = null;
            var sortByDistanceAsc = false;
            switch (request.Sort)
            {
                case ListingSortType.PriceLowToHigh:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.ListPrice, SortOrder = SortOrder.Ascending };
                    break;
                case ListingSortType.PriceHighToLow:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.ListPrice, SortOrder = SortOrder.Descending };
                    break;
                case ListingSortType.YearBuiltNewToOld:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.YearBuilt, SortOrder = SortOrder.Descending };
                    break;
                case ListingSortType.YearBuiltOldToNew:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.YearBuilt, SortOrder = SortOrder.Ascending };
                    break;
                case ListingSortType.DateListedNewToOld:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.ListedOn, SortOrder = SortOrder.Descending };
                    break;
                case ListingSortType.DateListedOldToNew:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.ListedOn, SortOrder = SortOrder.Ascending };
                    break;
                case ListingSortType.GarageLowToHigh:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.Exterior.GarageSpaces, SortOrder = SortOrder.Ascending };
                    break;
                case ListingSortType.GarageHighToLow:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.Exterior.GarageSpaces, SortOrder = SortOrder.Descending };
                    break;
                case ListingSortType.SqftLowToHigh:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.LivingSquareFeet, SortOrder = SortOrder.Ascending };
                    break;
                case ListingSortType.SqftHighToLow:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.LivingSquareFeet, SortOrder = SortOrder.Descending };
                    break;
                case ListingSortType.BedsLowToHigh:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.Rooms.TotalBedrooms, SortOrder = SortOrder.Ascending };
                    break;
                case ListingSortType.BedsHighToLow:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.Rooms.TotalBedrooms, SortOrder = SortOrder.Descending };
                    break;
                case ListingSortType.DaysOnMarketLowToHigh:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.ListedOn, SortOrder = SortOrder.Descending };
                    break;
                case ListingSortType.DaysOnMarketHighToLow:
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.ListedOn, SortOrder = SortOrder.Ascending };
                    break;
                case ListingSortType.DistanceLowToHigh: //Applicable for radius search only.
                    sortByDistanceAsc = true;
                    break;
                default:
                    //Commenting out the below condition since the result should still be sorted by days on market(NPLAY- 2650)
                    // if (!isAgentIdOfficeIdSearch) //if agent id and office id search, the list will be sorted by a certain agent id on top
                    sortExpression = new Sort<MLSPropertyListing> { FieldExpression = l => l.ListedOn, SortOrder = SortOrder.Descending };
                    break;
            }

            if (sortExpression != null)
                sortExpressions.Add(sortExpression);

            var filters = new ListingFilters
            {
                Filters = allFilters,
                Queries = allQueries,
                BoostingQueries = boostingQueries,
                ContainsCriteria = containsCriteria,
                SortList = sortExpressions,
                PageNumber = request.PageNumber,
                CountPerPage = request.CountPerPage * request.SearchCountExpander,
                MlsAgentId = request.MlsAgentId,
                MlsOfficeId = request.MlsAgentOfficeId,
                SortByDistanceAsc = sortByDistanceAsc,
                NoListings = noListings,
                IsAgentIdOfficeIdSearch = isAgentIdOfficeIdSearch
            };
            return filters;
        }

        private void AddMlsOfficeIdFilterIfNeeded(ListingFilters filters)
        {
            if (!string.IsNullOrWhiteSpace(filters.MlsOfficeId))
            {
                //TO make office id with "-" work, we use l.AgentOfficeId rather than l.ListingAgent.OfficeId.
                FilterContainer officeFilter = Filter<MLSPropertyListing>.Term(x => x.AgentOfficeId, filters.MlsOfficeId);
                filters.Filters.Add(officeFilter);
            }
        }

        private IQueryable<MLSPropertyListing> SortForAgentIdOfficeIdSearch(IQueryable<MLSPropertyListing> listings, string mlsAgentId)
        {
            var agentListings = listings.Where(l => l.AgentIds.Contains(mlsAgentId))
                                         .OrderByDescending(l => l.ListedOn).ToList();

            var officeListings = listings.Where(l => !l.AgentIds.Contains(mlsAgentId))
                                      .OrderByDescending(l => l.ListedOn);

            agentListings.AddRange(officeListings);
            return agentListings.AsQueryable();
        }

        private bool IsValidCenterPoint(string latitude, string longitude, out GeoCoordinate centerPoint)
        {
            double lat = 0, lon = 0;
            centerPoint = new GeoCoordinate();

            bool isValid = double.TryParse(latitude, out lat);
            isValid = isValid && double.TryParse(longitude, out lon);

            if (isValid)
            {
                centerPoint.Latitude = lat;
                centerPoint.Longitude = lon;
            }

            return isValid;
        }

        // GET api/listing/5
        [HttpGet]
        [Route("api/listings/{id}")]
        public IHttpActionResult Get(string id, [FromUri] ListingSearchRequestModel Filters)
        {
            var listing = ActiveListingRepository.GetListing(id);

            if (listing == null)
                return NotFound();

            int buyerViews = 0;

            var pinCriteria = new SearchCriteria<IDXPin>()
            {
                Index = SearchIndex.LeadActivity
            };
            FilterContainer pinFilter = Filter<IDXPin>.Term(x => x.ListingId, listing.PropertyListingId);
            pinCriteria.Filters.Add(pinFilter);

            var total = SearchRepository.Count(pinCriteria).Total;
            pinCriteria.Size = (int)total;
            var pinnedListings = SearchRepository.Get(pinCriteria).Documents.ToList();

            foreach (IDXPin pin in pinnedListings)
            {
                buyerViews += pin.Counter;
            }

            listing.BuyerViews = buyerViews;

            PopulateMortgagePaymentInfo(listing, Filters);
            PopulateSpecialFinancePrograms(new MLSPropertyListing[] { listing }, Filters);
            PopulateFIPSCode(listing);

            var result = SetDataProvider(Mapper.Map<ListingModel>(listing));
            if (!result.ListOnInternet)
                return Unauthorized();

            result.Images = result.Images.OrderBy(i => i.Order).ToList();

            ListingViewModel listingViewModel = new ListingViewModel()
            {
                Date = DateTime.UtcNow,
                Latitude = listing.Location.Lat,
                Longitude = listing.Location.Lon
            };
            var listingViewResult = ListingViewController.Post(listingViewModel);

            return Ok(result);
        }

        /// <summary>
        /// Search for listings by filters, no geo constraints
        /// </summary>
        /// <param name="Filters">Optional Params:
        ///     lt PriceLessThan
        ///     gt PriceGreaterThan
        ///     bd MinBeds
        ///     bt MinBaths
        ///     ny MinYear
        ///     xy MaxYear
        ///     nl MinLotSizeAcres (computed)
        ///     xl MaxLotSizeAcres (computed)
        ///     nq MinLivingSqft
        ///     xq MaxLivingSqft
        ///     tp PropertyType array
        ///     ml MlsId
        ///     ma MlsAgentId
        ///     mo MlsAgentOfficeId
        ///     st ListingSortType
        ///     ct CountPerPage
        ///     pg PageNumber
        ///     xg MaxGarage
        ///     ng MinGarage
        ///     att Full Text
        ///     oh Open House
        ///     xs Max Stories
        ///     ns Min Stories
        ///     sa SaleType
        ///     </param>
        [HttpGet]
        [Route("api/listings/search/")]
        public IHttpActionResult Search([FromUri]ListingSearchRequestModel Filters)
        {
            // set filters
            Filters = Filters ?? new ListingSearchRequestModel();
            var filters = GetFilters(Filters);
            BasicListingModelResult result = null;

            if (!filters.NoListings)
            {
                if (filters.IsAgentIdOfficeIdSearch)
                {
                    Agent agent = AgentRepository.GetAll().FirstOrDefault(a => a.MLSAgentId.ToLower() == filters.MlsAgentId.ToLower() && a.MLSOfficeId.ToLower() == filters.MlsOfficeId.ToLower() && a.MLSOrgId.ToLower() == Filters.MlsId.ToLower());
                    if (agent != null)
                        result = GetFeaturedListings(agent, Filters);
                }

                result = result ?? QueryListings(filters);
            }

            result = result ?? new BasicListingModelResult();

            return Ok(result);
        }

        [HttpGet]
        [Route("api/listings/pdf/{id}")]
        public HttpResponseMessage GetPdf(string id, [FromUri] string agentId = "", [FromUri] string u = "homeasappdf", [FromUri] int download = 0)
        {
            var pdf = GetPdfInfo(id, agentId, userAgent: u);

            if (pdf != null)
            {
                var resp = new HttpResponseMessage(HttpStatusCode.OK);
                resp.Content = new ByteArrayContent(pdf.FileBytes);
                resp.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/pdf");
                if (download == 1)
                {
                    resp.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment") { FileName = pdf.FileName };
                }
                else
                {
                    resp.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("inline") { FileName = pdf.FileName };
                }
                return resp;
            }
            else
            {
                var resp = new HttpResponseMessage(HttpStatusCode.NotFound);
                return resp;
            }
        }

        /// <summary>
        /// Returns listing image. Currently this endpoint returns only primary image. The 0 in the route indicates image index 0. In future, we could make it a parameter that you can change.
        /// </summary>
        [HttpGet]
        [Route("api/listings/{id}/image/0/{width?}")]
        public IHttpActionResult GetListingImage(string id, int? width = null)
        {
            var url = Settings.ListingNoPhotoUrl;
            var listing = ActiveListingRepository.GetListing(id);
            if (listing != null)
            {
                if (width == null)
                    width = 400;
                var height = width.Value * 0.75;

                var listingModel = SetDataProvider(Mapper.Map<ListingModel>(listing));
                var image = listingModel.PrimaryImageUrl(width);
                if (image.StartsWith(url))//no photo url
                {
                    //google street view
                    var client = new RestClient("https://maps.googleapis.com/");
                    var location = listing.Location != null ? string.Format("{0},{1}", listing.Location.Lat, listing.Location.Lon) :
                        listing.Address != null ? listing.Address.ToString() : null;
                    if (location != null)
                    {
                        var metadataUrl = string.Format("maps/api/streetview/metadata?size={0}x{1}&location={2}&key={3}", width, height, location, Settings.GoogleMapsApiKey);
                        var restRequest = new RestRequest(metadataUrl, Method.GET) { RequestFormat = DataFormat.Json };
                        var response = client.Execute(restRequest);
                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            var result = JsonConvert.DeserializeObject<GoogleStreetViewMetadataResult>(response.Content);
                            if (result.Status.Equals("OK", StringComparison.InvariantCultureIgnoreCase))
                                url = string.Format("https://maps.googleapis.com/maps/api/streetview?size={0}x{1}&location={2}&key={3}", width, height, location, Settings.GoogleMapsApiKey);
                        }
                    }
                }
                else
                    url = image;
            }

            return Redirect(url);
        }

        /// <summary>
        /// Returns featured listings for the authorized agent
        /// </summary>
        [HttpGet]
        [Authorize]
        [Route("api/listings/featured")]
        public IHttpActionResult GetFeaturedListings()
        {
            int agentid;

            if (!int.TryParse(User.Identity.Name, out agentid))
            {
                return BadRequest();
            }

            var agent = AgentRepository.GetById(agentid);
            if (agent == null)
            {
                return NotFound();
            }

            BasicListingModelResult result = GetFeaturedListings(agent);

            return Ok(result);
        }

        /// <summary>
        /// Returns featured listings for the agent
        /// </summary>
        [HttpGet]
        [Route("api/listings/featured/{agentId}")]
        public IHttpActionResult GetFeaturedListings(int agentId)
        {
            var agent = AgentRepository.GetById(agentId);
            if (agent == null)
            {
                return NotFound();
            }

            BasicListingModelResult result = GetFeaturedListings(agent);

            return Ok(result);
        }

        #region Helpers

        private void SetAgentFilters(ListingFilters filters, Agent agent)
        {
            filters.MlsAgentId = agent.MLSAgentId;
            filters.MlsOfficeId = agent.MLSOfficeId;
            filters.MlsId = agent.MLSOrgId;
        }

        private T GetObjectFromQueryString<T>(string queryString)
        {
            JObject json;
            string tempStringUri = $"http://www.temp.com?{queryString}";
            Uri tempUri = new Uri(tempStringUri);

            tempUri.TryReadQueryAsJson(out json);
            string sjson = JsonConvert.SerializeObject(json);
            return JsonConvert.DeserializeObject<T>(sjson);
        }

        private BasicListingModelResult GetFeaturedListings(Agent agent, ListingSearchRequestModel Filters = null)
        {
            Filters = Filters ?? new ListingSearchRequestModel()
            {
                mo = agent.MLSOfficeId,
                ml = agent.MLSOrgId,
                ma = agent.MLSAgentId,
                ct = 1000 // Expand above the default count so that we can get all the featured listings
            };


            if (agent.AgentSettings?.Any(s => s.Key == AgentSettingKeys.FeaturedListingsFilters) == true)
            {
                string featureListingsFilters = agent.AgentSettings.First(s => s.Key == AgentSettingKeys.FeaturedListingsFilters).Value;
                ListingSearchRequestModel featuredListingFiltersRequestModel = GetObjectFromQueryString<ListingSearchRequestModel>(featureListingsFilters);
                Filters.Merge(featuredListingFiltersRequestModel);
            }

            ListingFilters filters = GetFilters(Filters);

            // Get the featured listings for the main agent:
            BasicListingModelResult baseAgentResults = GetFeaturedListingsForSingleAgent(agent, false, filters);

            // Check for any self-affiliations, and get featured listings for all those agents as well.
            IEnumerable<Agent> selfAffiliates = agent.SelfAffiliates();

            if (selfAffiliates?.Count() > 0)
            {
                List<BasicListingModelResult> affiliateResults = new List<BasicListingModelResult>();
                filters = filters ?? new ListingFilters()
                {
                    Queries = new List<QueryContainer>(),
                };

                foreach (var affilate in selfAffiliates)
                {
                    SetAgentFilters(filters, affilate);
                    BasicListingModelResult basicResult = GetFeaturedListingsForSingleAgent(affilate, true, filters);
                    if (basicResult?.Results?.Count() > 0)
                        affiliateResults.Add(basicResult);
                }

                SetAgentFilters(filters, agent);
                List<BasicListingModel> allListings = new List<BasicListingModel>();

                // Add listing results from the base agent:
                allListings.AddRange(baseAgentResults.Results);

                // Add listing results from all affiliate agents
                foreach (var ar in affiliateResults)
                {
                    allListings.AddRange(ar.Results);
                }

                // Construct the result sorted by the root agent first, followed by affiliate featured listings
                BasicListingModelResult result = new BasicListingModelResult()
                {
                    NumResults = baseAgentResults.NumResults + affiliateResults.Sum(r => r.NumResults),
                    Results = new List<BasicListingModel>()
                };

                // Return sorted list of listing results in the following order:
                //  Agent's featured listings
                Predicate<BasicListingModel> selector = fl => fl.ListingAgent?.Id == agent.MLSAgentId;
                result.Results.AddRange(allListings.Where(l => selector(l)));
                allListings.RemoveAll(selector);
                //  Affiliate agent's featured listings
                string[] affiliateAgentIds = selfAffiliates.Select(a => a.MLSAgentId).ToArray();
                selector = fl => fl.ListingAgent != null && affiliateAgentIds.Contains(fl.ListingAgent.Id);
                result.Results.AddRange(allListings.Where(l => selector(l)));
                allListings.RemoveAll(selector);
                //  Agent's office featured listings
                selector = fl => fl.ListingAgent?.OfficeId == agent.MLSOfficeId;
                result.Results.AddRange(allListings.Where(l => selector(l)));
                allListings.RemoveAll(selector);
                //  Affiliate agents' office featured listings
                result.Results.AddRange(allListings);

                return result;
            }
            else
            {
                // This is a normal agent with no self affiliates
                return baseAgentResults;
            }
        }

        private BasicListingModelResult GetFeaturedListingsForSingleAgent(Agent agent, bool isSelfAffiliate, ListingFilters filters = null)
        {
            BasicListingModelResult result = null;

            // Some MLSes only allow Featured Listings to be displayed for explicitly approved agents.  In this case, we just return null for agents without this approval.
            if (!String.IsNullOrWhiteSpace(agent.MLSOrgId))
            {
                var context = MlsSynchronizationContextProvider.Get(agent.MLSOrgId);
                if (context.ShowFeaturedListingsForIDXOnly
                    && agent.HomeSearchRegisteredDateTime == null
                    && agent.AgentSettings?.FirstOrDefault(s => s.Key == AgentSettingKeys.AllowFeaturedListingsWhenHidden)?.Value != "true")
                {
                    return result;
                }
            }

            if (String.IsNullOrWhiteSpace(agent.MLSOfficeId) && String.IsNullOrWhiteSpace(agent.MLSAgentId))
            {
                result = new BasicListingModelResult()
                {
                    NumResults = 0,
                    Results = new List<BasicListingModel>()
                };
            }
            else
            {
                ListingFilters queryFilters = null;
                if (isSelfAffiliate || filters == null)
                {
                    ListingSearchRequestModel searchRequestModel = new ListingSearchRequestModel()
                    {
                        mo = agent.MLSOfficeId,
                        ml = agent.MLSOrgId,
                        ma = agent.MLSAgentId,
                        ct = 1000 // Expand above the default count so that we can get all the featured listings
                    };

                    if (agent.AgentSettings?.FirstOrDefault(s => s.Key == AgentSettingKeys.FeaturedListingsHideRentals) != null)
                    {
                        searchRequestModel.sa = SaleType.Sale;
                    }

                    queryFilters = GetFilters(searchRequestModel);
                }
                else
                {
                    queryFilters = filters;
                }

                result = QueryListings(queryFilters);
            }

            return result;
        }

        private BasicListingModelResult QueryListings(ListingFilters filters)
        {
            string listingsIndex = GetListingsIndex(filters);
            BasicListingModelResult result = new BasicListingModelResult();
            var criteria = new SearchCriteria<MLSPropertyListing>(filters.CountPerPage)
            {
                Index = listingsIndex
            };
            criteria.QueryFilters.AddRange(filters.Queries);
            criteria.BoostingQueries.AddRange(filters.BoostingQueries);
            criteria.ContainsExpressions.AddRange(filters.ContainsCriteria);
            criteria.SortExpressions.AddRange(filters.SortList);
            criteria.PageNumber = filters.PageNumber;

            if (filters.IsAgentIdOfficeIdSearch)
            {
                var criteria2 = new SearchCriteria<MLSPropertyListing>(filters.CountPerPage)
                {
                    Index = listingsIndex
                };
                criteria2.Filters.AddRange(filters.Filters);
                criteria2.QueryFilters.AddRange(filters.Queries);
                criteria2.BoostingQueries.AddRange(filters.BoostingQueries);
                criteria2.ContainsExpressions.AddRange(filters.ContainsCriteria);
                criteria2.SortExpressions.AddRange(filters.SortList);
                criteria2.PageNumber = filters.PageNumber;

                //Firstly, get listings with the specific mls agent id
                string[] arr2 = { filters.MlsAgentId };
                criteria2.ContainsExpressions.Add(new ContainsCriteria<MLSPropertyListing>()
                {
                    ContainsExpression = l => l.AgentIds,
                    PossibleValues = arr2
                });

                var data2 = SearchRepository.Get(criteria2);
                var count2 = data2.Total; //Total gives you total count for all the pages
                var docs2 = data2.Documents.AsQueryable();

                //Secondly, get listings that are not by the specific mls agent id
                AddMlsOfficeIdFilterIfNeeded(filters);
                criteria.Filters.AddRange(filters.Filters);

                string[] arr = { filters.MlsAgentId };
                criteria.ExcludeExpressions.Add(new ExcludeCriteria<MLSPropertyListing>()
                {
                    ExcludeExpression = l => l.AgentIds,
                    ExcludedValues = arr
                });

                var mlsAgentsListingCount = docs2.Count();
                if (mlsAgentsListingCount < filters.CountPerPage)
                {
                    criteria.Size = filters.CountPerPage - mlsAgentsListingCount;
                    var data = SearchRepository.Get(criteria);
                    var count = data.Total; //Total gives you total count for all the pages
                    var docs = data.Documents.AsQueryable();

                    // TODO:
                    // docs = ApplyMortgageFilters(docs, Filters);

                    var entireList = new List<MLSPropertyListing>();
                    entireList.AddRange(docs2.ToList());
                    entireList.AddRange(docs.ToList());
                    result.NumResults = (int)count2 + (int)count;
                    result.Results = Finalize(entireList.AsQueryable());
                }
                else
                {
                    criteria.Size = null;
                    var count = SearchRepository.Count(criteria).Total;

                    result.NumResults = (int)count2 + (int)count;
                    result.Results = Finalize(docs2);
                }
            }
            else
            {
                AddMlsOfficeIdFilterIfNeeded(filters);
                criteria.Filters.AddRange(filters.Filters);

                var data = SearchRepository.Get(criteria);
                var count = data.Total; //Total gives you total count for all the pages
                var docs = data.Documents.AsQueryable();

                // TODO:
                // docs = ApplyMortgageFilters(docs, Filters);

                result.NumResults = (int)count;
                result.Results = Finalize(docs);
            }

            return result;
        }

        private List<BasicListingModel> SetDataProvider(List<BasicListingModel> basicListingModels)
        {
            foreach (var listing in basicListingModels)
            {
                SetDataProvider(listing);
            }

            return basicListingModels;
        }

        private void PopulateFIPSCodes(IQueryable<MLSPropertyListing> listings)
        {
            foreach (var listing in listings)
            {
                PopulateFIPSCode(listing);
            }
        }

        private void PopulateFIPSCode(MLSPropertyListing listing)
        {
            if (listing.Address != null)
            {
                listing.Address.FIPS = RatePlugClient.GetFIPS(listing);
            }
        }

        private BasicListingModel SetDataProvider(BasicListingModel listing)
        {
            string mlsId = GetMlsIdFromListing(listing);
            if (String.IsNullOrWhiteSpace(mlsId))
            {
                listing.DataProvider = DataProviders.NotSet.ToString();
            }
            else
            {
                var context = MlsSynchronizationContextProvider.Get(mlsId);
                listing.DataProvider = context.DataProvider.ToString();
            }

            return listing;
        }

        private ListingModel SetDataProvider(ListingModel listing)
        {
            string mlsId = GetMlsIdFromListing(listing);
            if (String.IsNullOrWhiteSpace(mlsId))
            {
                listing.DataProvider = DataProviders.NotSet.ToString();
            }
            else
            {
                var context = MlsSynchronizationContextProvider.Get(mlsId);
                listing.DataProvider = context.DataProvider.ToString();
            }

            return listing;
        }

        private string GetMlsIdFromListing(BasicListingModel listing)
        {
            string mlsId = listing.OriginatingMls;
            if (String.IsNullOrWhiteSpace(mlsId))
            {
                if (listing.MlsIds?.FirstOrDefault() != null)
                {
                    mlsId = listing.MlsIds[0];
                }
            }

            return mlsId;
        }

        protected Int16? TotalSpaceResolver(ListingExterior result)
        {
            Int16? val = 0;
            if (result.CarportSpaces != null)
                val = result.CarportSpaces;
            else if (result.GarageSpaces != null)
                val = result.GarageSpaces;
            else
                return null;

            return val;
        }

        // Pushes all listings that are missing photos to the end of the list
        private List<BasicListingModel> ShiftListingsWithoutPhotos(List<BasicListingModel> originalList)
        {
            var listingsWithPhotos = from l in originalList
                                     where !l.IsMissingImages
                                     select l;
            var listingsWithoutPhotos = from l in originalList
                                        where l.IsMissingImages
                                        select l;

            return listingsWithPhotos.Concat(listingsWithoutPhotos).ToList();
        }

        private List<BasicListingModel> Finalize(IQueryable<MLSPropertyListing> originalList)
        {
            PopulateFIPSCodes(originalList);
            var basicListingModels = Mapper.Map<List<BasicListingModel>>(originalList);
            basicListingModels = SetDataProvider(basicListingModels);
            basicListingModels = ShiftListingsWithoutPhotos(basicListingModels);
            return basicListingModels;
        }

        private AttachmentInfo GetPdfInfo(string listingId, string agentId, string userAgent)
        {
            AttachmentInfo info = null;
            var listing = ActiveListingRepository.GetListing(listingId);

            if (listing != null)
            {
                var url = string.IsNullOrWhiteSpace(agentId) ?
                    string.Format(Settings.ListingDetailsUrlWithoutAgent, listingId) :
                    string.Format(Settings.ListingDetailsUrlWithAgent, agentId, listingId);
                var fileName = listing.AddressDisplay().ToString().Replace(" ", "_") + ".pdf";
                var content = BrowserExporter.GetPdf(url, userAgent);

                info = new AttachmentInfo
                {
                    FileBytes = content,
                    FileName = fileName
                };
            }

            return info;
        }

        #endregion
    }

    public class ListingFilters
    {
        public List<FilterContainer> Filters { get; set; }
        public List<QueryContainer> Queries { get; set; }
        public List<QueryContainer> BoostingQueries { get; set; }
        public List<ContainsCriteria<MLSPropertyListing>> ContainsCriteria { get; set; }
        public List<Sort<MLSPropertyListing>> SortList { get; set; }
        public string MlsAgentId { get; set; }
        public string MlsOfficeId { get; set; }
        public string MlsId { get; set; }
        public int PageNumber { get; set; }
        public int CountPerPage { get; set; }
        public bool SortByDistanceAsc { get; set; }
        public bool NoListings { get; set; }
        public bool IsAgentIdOfficeIdSearch { get; set; }
    }
}
