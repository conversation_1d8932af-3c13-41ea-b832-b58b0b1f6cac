﻿using AutoMapper;
using HomeASAP.Services.ListingReprocessorState.Controllers;
using Microsoft.ServiceFabric.Data;
using Microsoft.ServiceFabric.Services.Client;
using Microsoft.ServiceFabric.Services.Communication.Runtime;
using Microsoft.ServiceFabric.Services.Runtime;
using MLS.Synchronization;
using MLS.Synchronization.Abstract;
using MLS.Synchronization.GeoCoding;
using MLS.Synchronization.Images;
using MLS.Synchronization.Listings;
using MLS.Synchronization.Models;
using Ninject;
using Ninject.Extensions.NamedScope;
using Nplay.Services.Data;
using NPlay;
using NPlay.BusinessLogic.AgentListingWebsite;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Contextual;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Models.Mapping;
using NPlay.Common.Models.MLS;
using NPlay.Common.Queues;
using NPlay.Common.Repository;
using NPlay.Common.Search;
using NPlay.Common.Search.Abstract;
using NPlay.Common.ServiceAgents.ListingReprocessor;
using NPlay.Common.ServiceAgents.Messaging;
using NPlay.Common.ServiceAgents.MLSQuery;
using NPlay.Common.ServiceAgents.NPlayApi;
using NPlay.Common.ServiceAgents.RETSConnections;
using NPlay.Common.ServiceAgents.RETSMetadata;
using NPlay.Common.ServiceFabric.OwinHost;
using NPlay.Common.Services.Abstract;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.ReadDataFlow.Data;
using NPlay.RealDataFlow.Mapping;
using NPlay.RealDataFlow.Mapping.Abstract;
using NPlay.RealDataFlow.Mapping.Maps;
using NPlay.RealDataFlow.Mapping.Models.Maps;
using NPlay.RealDataFlow.RETSConnector;
using NPlay.RealDataFlow.RETSConnector.Abstract;
using RealData.Simulation;
using RESO.Connector;
using RESO.Connector.Abstract;
using RESO.Synchronization;
using RESO.Synchronization.RESOData;
using RETS.Parsing;
using RETS.Parsing.Abstract;
using RETS.Web;
using RETS.Web.Abstract;
using RETSDataSynchronization;
using RETSDataSynchronization.Images;
using RETSDataSynchronization.MapExecution;
using RETSDataSynchronization.RETSData;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Fabric;
using System.Fabric.Description;
using System.Linq;
using System.Net;
using System.Security.Policy;
using System.Threading;
using System.Threading.Tasks;

namespace HomeASAP.Services.ListingReprocessorState
{
    /// <summary>
    /// An instance of this class is created for each service replica by the Service Fabric runtime.
    /// </summary>
    internal sealed class ListingReprocessorState : StatefulService
    {
        ILogger Logger;

        public ListingReprocessorState(StatefulServiceContext context)
            : base(context)
        { }

        /// <summary>
        /// Optional override to create listeners (e.g., HTTP, Service Remoting, WCF, etc.) for this service replica to handle client or user requests.
        /// </summary>
        /// <remarks>
        /// For more information on service communication, see https://aka.ms/servicefabricservicecommunication
        /// </remarks>
        /// <returns>A collection of listeners.</returns>
        protected override IEnumerable<ServiceReplicaListener> CreateServiceReplicaListeners()
        {
            return new[] { new ServiceReplicaListener(context => this.CreateInternalListener(context)) };
        }
        private void SetMaxConcurrency(string url, int maxConcurrentRequests)
        {
            ServicePointManager.FindServicePoint(new Uri(url)).ConnectionLimit = maxConcurrentRequests;
        }

        private ICommunicationListener CreateInternalListener(System.Fabric.ServiceContext context)
        {
            var endpoints = Context.CodePackageActivationContext.GetEndpoints()
                                   .Where(endpoint => endpoint.Protocol == EndpointProtocol.Http || endpoint.Protocol == EndpointProtocol.Https)
                                   .Select(endpoint => endpoint.Name);

            Action<IKernel> bindingAction = k =>
            {
                Logger = k.Get<ILogger>();
                k.Bind<IReliableStateManager>().ToMethod<IReliableStateManager>(c => this.StateManager);
                string notNullText = this.StateManager != null ? "not " : "";
                Logger.Info($"Bound to the IReliableStateManager.  StateManager is {notNullText}null");

                // Simulations
                string SIMULATION_CONTEXT_NAME = "SimulationContext";
                k.Bind<IDataFeedSynchronizationSimulator>().To<DataFeedSynchronizationSimulator>().DefinesNamedScope(SIMULATION_CONTEXT_NAME);

                // MLS Context
                k.Bind<IDBFactory<MlsContext>>().To<DBFactory<MlsContext>>();
                k.Bind<IRepository<MultipleListingService>>().To<EntityRepositoryBase<MultipleListingService, MlsContext>>();
                k.Bind<IRepository<ReDataVaultVerify>>().To<EntityRepositoryBase<ReDataVaultVerify, MlsContext>>();
                k.Bind<IRepository<MlsReDataVaultSetting>>().To<EntityRepositoryBase<MlsReDataVaultSetting, MlsContext>>();
                k.Bind<IRepository<MLSActiveStatus>>().To<EntityRepositoryBase<MLSActiveStatus, MlsContext>>();
                k.Bind<IRepository<MLSMaster>>().To<EntityRepositoryBase<MLSMaster, MlsContext>>();
                k.Bind<IRepository<State>>().To<EntityRepositoryBase<State, MlsContext>>();
                k.Bind<IRepository<DataFeed>>().To<EntityRepositoryBase<DataFeed, MlsContext>>();
                k.Bind<IRepository<RETSCredential>>().To<EntityRepositoryBase<RETSCredential, MlsContext>>();
                k.Bind<IRepository<RESOCredential>>().To<EntityRepositoryBase<RESOCredential, MlsContext>>();
                k.Bind<ICachedMLSRepository>().To<MLSCache>().InSingletonScope();
                k.Bind<IMLSRepository>().To<MLSRepository>();

                // Mapping context
                k.Bind<IDBFactory<MapContext>>().To<DBFactory<MapContext>>();
                k.Bind<IRepository<Map>>().To<EntityRepositoryBase<Map, MapContext>>();
                k.Bind<IRepository<MapField>>().To<EntityRepositoryBase<MapField, MapContext>>();
                k.Bind<IRepository<MapNote>>().To<EntityRepositoryBase<MapNote, MapContext>>();
                k.Bind<IRepository<Related>>().To<EntityRepositoryBase<Related, MapContext>>();
                k.Bind<IRepository<MapPropertyType>>().To<EntityRepositoryBase<MapPropertyType, MapContext>>();
                k.Bind<IRepository<MappingListingStatus>>().To<EntityRepositoryBase<MappingListingStatus, MapContext>>();

                // Real Data
                k.Bind<IGetMLSFeedData>().To<GetRETSData>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IMapRETSData>().To<MapRETSData>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRealDataMapper>().To<RealDataMapper>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<ICanonTranslator>().To<CanonTranslator>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<ICanonImageTranslator>().To<CanonImageTranslator>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<ICanonAgentSaveToSQL>().To<CanonAgentSaveToSQL>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IListingStatusCacher>().To<ListingStatusCacher>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IListingStatusProvider>().To<ListingStatusProvider>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSImageDownloader>().To<RETSImageDownloader>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IImageUploader>().To<CIFSImageUploader>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IFTPConnector>().To<FTPConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IDataFeedManager>().To<TransparentRETSDataFeedManager>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSWebClientFactory>().To<RETSWebClientFactory>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSConnectionManagerClient>().To<RETSConnectionManagerClient>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSMetadataManagerClient>().To<RETSMetadataManagerClient>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSQueryHandler>().To<RETSQueryHandler>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSParser>().To<RETSParser>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSSearchConnector>().To<RETSSearchConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRETSImagesConnector>().To<RETSImagesConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRealDataClient>().To<RealDataClient>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IGeoCoder>().To<GeoCoder>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRawListingRepository>().To<RawListingRepository>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IImageStreamDownloader>().To<ImageStreamDownloader>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IMlsSynchronizationClient>().To<MlsSynchronizationClient>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRemoveListingService>().To<RemoveListingService>().InNamedScope(SIMULATION_CONTEXT_NAME);

                // Agent Listing Websites
                k.Bind<IDBFactory<AgentContext>>().To<DBFactory<AgentContext>>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRepository<Agent>>().To<EntityRepositoryBase<Agent, AgentContext>>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRepository<ZipCode>>().To<EntityRepositoryBase<ZipCode, AgentContext>>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IAgentListingSiteManager>().To<AgentListingSiteManager>().InNamedScope(SIMULATION_CONTEXT_NAME);

                // RESO 
                k.Bind<IGetRESOFeedData>().To<GetRESOData>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRESOQueryConnectorFactory>().To<RESOQueryConnectorFactory>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IBridgeQueryConnector>().To<BridgeQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<ITrestleQueryConnector>().To<ThrottledTrestleQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRapattoniQueryConnector>().To<RapattoniQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IMlsGridListingQueryConnector>().To<ThrottledMlsGridListingQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IMLSQueryClient>().To<MLSQueryClient>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IMapRESOData>().To<MapRESOData>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRESOImageDownloader>().To<RESOImageDownloader>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IListingImageLazyLoadManager>().To<ListingImageLazyLoadManager>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IMlsGridMediaConnector>().To<MlsGridMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IBridgeMediaConnector>().To<BridgeMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<ITrestleMediaConnector>().To<TrestleMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRapattoniMediaConnector>().To<RapattoniMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IUtahRealEstateQueryConnector>().To<UtahRealEstateQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IUtahRealEstateMediaConnector>().To<UtahRealEstateMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRMLSQueryConnector>().To<RMLSQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IRMLSMediaConnector>().To<RMLSMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<ISparkQueryConnector>().To<SparkQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<ISparkMediaConnector>().To<SparkMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IParagonQueryConnector>().To<ParagonQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IParagonMediaConnector>().To<ParagonMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IPerchwellQueryConnector>().To<PerchwellQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IPerchwellMediaConnector>().To<PerchwellMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IOpenMLSQueryConnector>().To<OpenMLSQueryConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IOpenMLSMediaConnector>().To<OpenMLSMediaConnector>().InNamedScope(SIMULATION_CONTEXT_NAME);

                // Listing reprocessor
                k.Bind<IListingProcessorClient>().To<ListingProcessorClient>();
                k.Bind<IListingIndexConfigurator>().To<ListingIndexConfigurator>();

                //MLS Synchronization Context
                k.Bind<IMlsSynchronizationContextProvider>().ToMethod(p =>
                {
                    MlsSynchronizationContextProvider.Instance.MLSCache = MlsSynchronizationContextProvider.Instance.MLSCache ?? k.Get<ICachedMLSRepository>();
                    return MlsSynchronizationContextProvider.Instance;
                }).InSingletonScope();

                // Listing Translation to Active
                k.Bind<IActiveListingService>().To<ActiveListingService>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IListingImageManager>().To<ListingImageManager>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IActiveListingRepository>().To<ActiveListingRepository>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IListingTypeAheadManager>().To<ListingTypeAheadManager>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IListingChangeNotifier>().To<ListingChangeNotifier>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IListingChangeConfigurationProvider>().To<ListingChangeConfigurationProvider>().InSingletonScope();

                // Search
                k.Bind<ISearchRepository>().To<SearchRepository>().InNamedScope(SIMULATION_CONTEXT_NAME);

                // Eventing
                k.Bind<IEventBroker>().To<PersistentEventBroker>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IEventBroker>().To<MemoryEventBroker>().WhenAnyAncestorMatches(p => p.Request.ParentRequest != null && p.Request.ParentRequest.Service == typeof(InMemoryReprocessingController)).InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IQueueFactory>().To<RabbitQueueFactory>().InSingletonScope();
                k.Bind<IEventPrismFactory>().To<RabbitEventPrismFactory>().InSingletonScope();
                k.Bind<IDelayedEventClient>().To<DelayedEventClient>().InSingletonScope();
                k.Bind<IContextualEventBroker>().To<ContextualEventBroker>().InNamedScope(SIMULATION_CONTEXT_NAME);
                k.Bind<IProcessingContext>().ToMethod(c => new ProcessingContext() { IsReprocessing = true }).InNamedScope(SIMULATION_CONTEXT_NAME);

                // AutoMapper
                k.Bind<IAutoMapperConfigurationProvider>().ToMethod(c => {
                    var provider = new AutoMapperConfigurationProvider();
                    provider.AddProfile<MLSPropertyListingMappingProfile>();
                    provider.AddProfile<MLSPropertyMappingProfile>();

                    return provider;
                }).InSingletonScope();

                k.Bind<MapperConfiguration>().ToProvider<IAutoMapperConfigurationProvider>().InSingletonScope();
                k.Bind<IMapper>().ToMethod(c => c.Kernel.Get<MapperConfiguration>().CreateMapper());
            };

            return new OwinCommunicationListener(bindingAction, context, endpoints.FirstOrDefault(), null, null);
        }
    }
}
