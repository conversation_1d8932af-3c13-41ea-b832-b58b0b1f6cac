﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using AutoMapper;
using AutoMapper.QueryableExtensions;

using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.Enums;
using NPlay.Common.Identity;
using NPlay.Common.Models;
using NPlay.Common.Models.Services;
using NPlay.Common.Web.Abstract;
using NPlay.Services.NPlayApi.Builders.Abstract;

namespace NPlay.Services.NPlayApi.Builders
{
    /// <summary>
    /// Base class for service model builders
    /// </summary>
    /// <remarks>TODO migrate builders to command pattern to avoid
    /// repetition of basic functions like AgentExists, etc.
    /// </remarks>
    public abstract class ServiceModelBuilderBase
    {
        #region Members

        protected readonly IRepository<Agent> _agentRepository;
        protected readonly IRepository<Service> _serviceRepository;
        protected readonly IRepository<ProductPlan> _productPlanRepository;
        protected readonly IRepository<FundingSource> _fundingSourceRepository;
        protected readonly IFacebookAgent _facebookAgent;
        protected readonly IMapper _mapper;

        private Agent _agent = null;
        private List<ProductPlan> _productPlans = null;
        private List<FundingSource> _fundingSources = null;

        #endregion

        #region Properties

        /// <summary>
        /// All of agent's funding sources.
        /// </summary>
        protected List<FundingSource> FundingSources
        {
            get
            {
                if (_fundingSources == null)
                {
                    if (_agent != null)
                    {
                        _fundingSources = _fundingSourceRepository.GetAll().Where(f => f.MembershipId == _agent.Id && f.Active == true)
                            .ToList();
                    }
                    else
                    {
                        return new List<FundingSource>();
                    }
                }
                return _fundingSources;
            }
        }

        /// <summary>
        /// All active product plans defined for the service.
        /// </summary>
        protected List<ProductPlan> ProductPlans
        {
            get
            {
                if (_productPlans == null)
                {
                    // get related products
                    var service = _serviceRepository.GetById(ServiceType.GetId());

                    // get product ids
                    var productIds = service.Products.Select(p => p.Id).ToList();

                    // get active product plans
                    _productPlans = _productPlanRepository.GetAll()
                        .Where(pl => productIds.Contains(pl.ProductId))
                        //.Where(pl => pl.Enabled)
                        .ToList();
                    if(service.Id == (int)ServiceType.InstagramPageEngage)
                    {
                        _productPlans.RemoveAll(p => p.ProductId == (int)ProductType.Bundle &&
                                                    (p.ProductPlanTypeId != (byte)ProductPlanType.InstagramIncluded &&
                                                     p.ProductPlanTypeId != (byte)ProductPlanType.InstagramIDXIncluded &&
                                                     p.ProductPlanTypeId != (byte)ProductPlanType.AfordalInstagramIDXIncluded));

                    }
                }
                return _productPlans;
            }
        }

        protected virtual ServiceType ServiceType
        {
            get
            {
                return ServiceType.AgentGroups;
            }
        }

        #endregion

        #region Constructors

        /// <summary>
        /// Injects dependencies
        /// </summary>
        /// <param name="agentRepository">Agent repository</param>
        /// <param name="serviceRepository">Service repository</param>
        /// <param name="serviceType">Service type</param>
        public ServiceModelBuilderBase(IRepository<Agent> agentRepository,
            IRepository<Service> serviceRepository,
            IRepository<ProductPlan> productPlanRepository,
            IRepository<FundingSource> fundingSourceRepository,
            IFacebookAgent facebookAgent,
            IMapper mapper)
        {
            _agentRepository = agentRepository;
            _serviceRepository = serviceRepository;
            _productPlanRepository = productPlanRepository;
            _fundingSourceRepository = fundingSourceRepository;
            _facebookAgent = facebookAgent;
            _mapper = mapper;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Implemented by child classes to return service-specific
        /// registration events.
        /// </summary>
        /// <returns></returns>
        protected abstract List<EventType> GetRegistrationEvents();

        /// <summary>
        /// Implemented by child classes to return service-specific
        /// setup events.
        /// </summary>
        /// <returns>List of EventType</returns>
        protected abstract List<EventType> GetSetupEvents();

        /// <summary>
        /// Corrects out-of-sync condition between user's auth cookie
        /// and registered service state.
        /// </summary>
        /// <param name="serviceModel">Current service</param>
        /// <param name="agent">Subscribing agent</param>
        /// <param name="basicInfoOnly">Ignore if true</param>
        protected void SyncRegisteredService(ServiceModel serviceModel, Agent agent, bool basicInfoOnly = false)
        {
            if (agent != null && !basicInfoOnly)
            {
                bool inSync = true;
                var user = HttpContext.Current.User as NPlayPrincipal;
                if (user != null && user.RegisteredServices != null)
                {
                    var registeredService = user.RegisteredServices.FirstOrDefault(s => s.Type.Equals(serviceModel.ServiceType));
                    if (registeredService == null)
                    {
                        inSync = serviceModel.Status.Equals(ServiceStatus.Available);
                    }
                    else 
                    {
                        switch (serviceModel.Status)
                        {
                            case ServiceStatus.Available:
                                inSync = false;
                                break;
                            case ServiceStatus.Funded:
                                inSync = registeredService.Status.Equals(ServiceStatus.Funded);
                                break;
                            case ServiceStatus.SetupComplete:
                            case ServiceStatus.SetupWithErrors:
                                inSync = registeredService.Status.Equals(ServiceStatus.SetupComplete);
                                break;
                            case ServiceStatus.MlsSetupComplete:
                                inSync = registeredService.Status.Equals(ServiceStatus.MlsSetupComplete);
                                break;
                        }
                    }

                    if (!inSync)
                    {
                        UpdateAuthCookie(agent);
                    }                    
                }
            }
        }

        /// <summary>
        /// Returns service status based on last completed setup event.
        /// </summary>
        /// <param name="setupEvent">AccountEvent</param>
        /// <returns>ServiceStatus</returns>
        protected virtual ServiceStatus GetSetupStatus(AccountEvent setupEvent)
        {
            ServiceStatus status = ServiceStatus.Funded;
            if (setupEvent != null)
            {
                EventType eventType = (EventType)setupEvent.EventId;
                var setupEventType = GetSetupEvents().First();
                if (eventType == setupEventType)
                    status = ServiceStatus.SetupComplete;
            }
            return status;
        }

        #endregion

        #region Helpers

        /// <summary>
        /// Retrieves agent information by Id.
        /// </summary>
        /// <param name="agentId">Agent Id</param>
        /// <param name="agent">Agent model</param>
        /// <returns>True if agent found</returns>
        protected bool AgentExists(int agentId, out Agent agent)
        {
            if (_agent == null)
            {
                _agent = _agentRepository.GetById(agentId);
            }
            agent = _agent;
            return _agent != null;
        }

        /// <summary>
        /// Retrieves service product plans and cross-checks
        /// against agent's registered service.
        /// </summary>
        /// <param name="productPlan">ProductPlan model</param>
        /// <returns>True if agent is registered for applicable plan</returns>
        protected bool AgentIsRegistered(out ProductPlan productPlan, out FundingSource fundingSource)
        {
            productPlan = null;
            fundingSource = null;

            if (_agent != null)
            {
                List<int> productPlanIds = ProductPlans.Select(p => p.Id).ToList();
                if (productPlanIds.Count > 0)
                {
                    if (FundingSources.Count > 0)
                    {
                        var fs = FundingSources.Where(f => f.ProductPlanId.HasValue &&
                            productPlanIds.Contains(f.ProductPlanId.Value))
                            .OrderByDescending(f => f.Active)
                            .ThenBy(f => f.FailCount)
                            .FirstOrDefault();

                        if (fs != null)
                        {
                            productPlan = ProductPlans.Where(p => p.Id == fs.ProductPlanId).First();
                            fundingSource = fs;
                        }
                    }
                }
            }
            return productPlan != null;
        }

        /// <summary>
        /// Filters agent's account events for service-specific events
        /// and returns most recent.
        /// </summary>
        /// <returns>AccountEvent</returns>
        private AccountEvent GetLastEvent(List<int> eventIds)
        {
            AccountEvent lastEvent = null;
            if (_agent != null)
            {
                if (eventIds.Count > 0)
                {
                    lastEvent = _agent.AccountEvents
                        .Where(e => eventIds.Contains(e.EventId))
                        .Where(e => e.Enabled)
                        .OrderBy(e => e.Created)
                        .LastOrDefault();
                }
            }
            return lastEvent;
        }

        /// <summary>
        /// Filters agent's account events for service-specific registration events
        /// and returns most recent.
        /// </summary>
        /// <returns></returns>
        protected AccountEvent GetLastRegistrationEvent()
        {
            List<int> registrationEvents = GetRegistrationEvents().Select(e => (int)e).ToList();
            return GetLastEvent(registrationEvents);
        }

        /// <summary>
        /// Filters agent's account events for service-specific setup events
        /// and returns most recent.
        /// </summary>
        /// <returns>AccountEvent</returns>
        protected AccountEvent GetLastSetupEvent()
        {
            List<int> setupEvents = GetSetupEvents().Select(e => (int)e).ToList();
            return GetLastEvent(setupEvents);
        }

        /// <summary>
        /// Retrieves agent's facebook business pages.
        /// </summary>
        /// <param name="viewModel">Service view model</param>
        protected void GetPages(ServiceModel viewModel)
        {
            if (_agent != null)
            {
                List<NPlay.Common.Models.Facebook.FacebookPage> facebookPages = _facebookAgent.UpdateFacebookPages(_agent.Id);
                if ((facebookPages != null) && (facebookPages.Count > 0))
                {
                    viewModel.Pages.BusinessPages = _mapper.Map<List<FacebookPageModel>>(facebookPages);
                }
            }
        }

        /// <summary>
        /// Retrieves agent's facebook permissions and cross-checks
        /// against required service permissions.
        /// </summary>
        /// <param name="viewModel">Service view model</param>
        protected void GetPermissions(ServiceModel viewModel)
        {
            if (_agent != null)
            {
                if (viewModel.Permissions.Required)
                {
                    List<string> grants = _facebookAgent.GetPermissions(_agent.FacebookId, _agent.AccessToken);
                    foreach (var p in viewModel.Permissions.FacebookPermissions)
                    {
                        p.Granted = grants.Where(g => g.Equals(p.Name, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault() != null;
                    }
                }
            }
        }       

        /// <summary>
        /// Validates that agent's funding source is active and 
        /// has no failed payments.
        /// </summary>
        /// <param name="fundingSource">FundingSource</param>
        /// <returns>True if active and no payment failures.</returns>
        protected bool IsValidFundingSource(FundingSource fundingSource)
        {
            return fundingSource != null && fundingSource.Active;
        }

        /// <summary>
        /// Updates agent's auth ticket to include newly setup service.
        /// </summary>
        /// <param name="agent">Agent</param>
        protected void UpdateAuthCookie(Agent agent)
        {
            var currentUser = System.Web.HttpContext.Current.User as NPlayPrincipal;
            var isSearchAllianceMember = agent.IsSearchAllianceMember && agent.ListingsImportedDateTime !=null;
            var cookies = AuthCookieHelper.GetAuthCookies(
                agent.Id,
                agent.Membership.FirstName,
                agent.Membership.LastName,
                agent.Membership.Email,
                agent.GetProfileImage(),
                agent.GetRegisteredServices(),
                true,
                agent.DirectoryMemberType,
                CookieConstants.AGENT_COOKIE_NAME,
                currentUser.IsImpersonated,
                currentUser.DelegateUserName,
                Settings.DefaultCookieExpiration,
                (agent.IsSearchAllianceMember && agent.ListingsImportedDateTime != null),
                agent.ListingsImportedDateTime,
                agent.IsSearchAllianceMember,
                false
                );
            cookies.ForEach(c => System.Web.HttpContext.Current.Response.Cookies.Add(c));
        }

        /// <summary>
        /// Checks for failed payments and updates service model status.
        /// </summary>
        /// <param name="fundingSource">FundingSource</param>
        /// <param name="viewModel">Service view model</param>
        /// <returns></returns>
        protected bool ValidateFundingSource(FundingSourceModel fundingSource, ServiceStateModel serviceState)
        {
            bool isCurrent = fundingSource.FailCount == 0;
            if (!isCurrent)
            {
                serviceState.Description = ServiceState.FailedPayment.ToString();
                serviceState.Alert = "Please go to {0} to update your credit card information.";
            }
            return isCurrent;
        }

        #endregion
    }
}