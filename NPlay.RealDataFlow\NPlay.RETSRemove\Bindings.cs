﻿using Ninject.Modules;
using Nplay.Services.Data;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Logging;
using NPlay.Common.Models;
using NPlay.Common.Models.MLS;
using NPlay.Common.Search;
using NPlay.Common.Queues;
using NPlay.ReadDataFlow.Data;
using NPlay.RealDataFlow.Mapping.Models.Maps;
using NPlay.RealDataFlow.RETSConnector;
using NPlay.RealDataFlow.RETSConnector.Abstract;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging;
using NPlay.RealDataFlow.Mapping;
using NPlay.RealDataFlow.Mapping.Abstract;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Repository;
using RETSDataSynchronization.RETSData;
using NPlay.Common.ServiceAgents.Messaging;
using NPlay.RealDataFlow.Data;
using RETS.Parsing.Abstract;
using RETS.Parsing;
using RETS.Web.Abstract;
using RETS.Web;
using NPlay.Common.Services.Abstract;
using NPlay.Common.ServiceAgents.RETSConnections;
using NPlay.Common.ServiceAgents.RETSMetadata;
using MLS.Synchronization.Abstract;
using MLS.Synchronization.Models;
using NPlay.Common.Messaging.Contextual;
using MLS.Synchronization.Listings;
using MLS.Synchronization;
using RESO.Synchronization.RESOData;
using RESO.Connector.Abstract;
using RESO.Connector;
using NPlay.Common.ServiceAgents.MLSQuery;
using NPlay.Common.ServiceAgents.ListingReprocessor;
using RETSDataSynchronization.MapExecution;
using Ninject.Extensions.NamedScope;

namespace NPlay.RemoveMlsData
{
    public class Bindings : NinjectModule
    {
        public override void Load()
        {
            Bind<IRealDataMapper>().To<RealDataMapper>();

            //Setting context
            Bind<IDBFactory<SettingsContext>>().To<DBFactory<SettingsContext>>();
            Bind<IUnitOfWork>().To<UnitOfWork<SettingsContext>>();
            Bind<IRepository<Setting>>().To<EntityRepositoryBase<Setting, SettingsContext>>();

            // Mapping context
            Bind<IDBFactory<MapContext>>().To<DBFactory<MapContext>>();
            Bind<IRepository<Map>>().To<EntityRepositoryBase<Map, MapContext>>();
            Bind<IRepository<MapField>>().To<EntityRepositoryBase<MapField, MapContext>>();
            Bind<IRepository<MapNote>>().To<EntityRepositoryBase<MapNote, MapContext>>();
            Bind<IRepository<Related>>().To<EntityRepositoryBase<Related, MapContext>>();
            Bind<IRepository<MapPropertyType>>().To<EntityRepositoryBase<MapPropertyType, MapContext>>();
            Bind<IRepository<MappingListingStatus>>().To<EntityRepositoryBase<MappingListingStatus, MapContext>>();

            //Mls Context
            Bind<IDBFactory<MlsContext>>().To<DBFactory<MlsContext>>();
            Bind<IRepository<MultipleListingService>>().To<EntityRepositoryBase<MultipleListingService, MlsContext>>();
            Bind<IRepository<RETSCredential>>().To<EntityRepositoryBase<RETSCredential, MlsContext>>();
            Bind<IRepository<MLSParameter>>().To<EntityRepositoryBase<MLSParameter, MlsContext>>();
            Bind<IRepository<DataFeed>>().To<EntityRepositoryBase<DataFeed, MlsContext>>();
            Bind<IRepository<MLSMaster>>().To<EntityRepositoryBase<MLSMaster, MlsContext>>();
            Bind<IMLSRepository>().To<MLSRepository>();
            Bind<IRepository<State>>().To<EntityRepositoryBase<State, MlsContext>>();
            Bind<IRepository<MLSActiveStatus>>().To<EntityRepositoryBase<MLSActiveStatus, MlsContext>>();
            Bind<IRepository<Membership>>().To<EntityRepositoryBase<Membership, PaymentContext>>();
            Bind<IDBFactory<PaymentContext>>().To<DBFactory<PaymentContext>>();
            Bind<IDBFactory<MLSRegionsByMasterContext>>().To<DBFactory<MLSRegionsByMasterContext>>().InThreadScope();
            Bind<IRepository<MLSRegionsByMaster>>().To<EntityRepositoryBase<MLSRegionsByMaster, MLSRegionsByMasterContext>>();
            Bind<IRepository<RESOCredential>>().To<EntityRepositoryBase<RESOCredential, MlsContext>>();

            Bind<IEventBroker>().To<PersistentEventBroker>().InSingletonScope();
            Bind<IEventPrismFactory>().To<RabbitEventPrismFactory>().InSingletonScope();
            Bind<IQueueFactory>().To<RabbitQueueFactory>().InSingletonScope();

            //MLS_Agents context
            Bind<IDBFactory<MLS_AgentContext>>().To<DBFactory<MLS_AgentContext>>();
            Bind<IRepository<MLSAgent>>().To<EntityRepositoryBase<MLSAgent, MLS_AgentContext>>();

            Bind<IRETSSearchConnector>().To<RETSSearchConnector>().InThreadScope();
            Bind<IFTPConnector>().To<FTPConnector>();
            Bind<IRETSParser>().To<RETSParser>();
            Bind<IRETSWebClientFactory>().To<RETSWebClientFactory>();
            Bind<IRETSConnectionManagerClient>().To<RETSConnectionManagerClient>();
            Bind<IRETSMetadataManagerClient>().To<RETSMetadataManagerClient>();

            Bind<IRETSQueryHandler>().To<RETSQueryHandler>();

            // Mls Synchronization Context
            Bind<IMlsSynchronizationContextProvider>().ToMethod(p => MlsSynchronizationContextProvider.Instance).InSingletonScope();

            //SearchRepo
            Bind<ISearchRepository>().To<SearchRepository>().InThreadScope();

            // NAMED SCOPE CONFIGURATIONS
            // All instances of ContextualEventBroker and ProcessingContext will be a shared single instance
            //   when injected into anything that is underneath the root processing objects (MapRETSData and MapRESOData).
            // All other instances use the default binding context as defined in "WhenNoAncestorMatches" bindings.
            // In other words, when an instance is created of either of the root processing objects, a single
            //   instance of all the below objects is created and shared through the entire object graph.
            // The purpose of this is to ensure that a single logger/processing context is persisted throughout the
            //   entire lifetime of the message processing context.  This is different than ThreadScope or SingletonScope.
            // See Ninject documentation for details about NamedScope.
            string PROCESSING_CONTEXT_NAME = "ProcessingContext";
            Bind<IRemoveRETSData>().To<RemoveRETSData>().DefinesNamedScope<RemoveRETSData>(PROCESSING_CONTEXT_NAME);
            Bind<IRemoveRESOData>().To<RemoveRESOData>().DefinesNamedScope<RemoveRESOData>(PROCESSING_CONTEXT_NAME);
            Bind<IContextualEventBroker>().To<ContextualEventBroker>().WhenAnyAncestorMatches(c =>
            {
                return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
            })
                .InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IContextualEventBroker>().To<ContextualEventBroker>().WhenNoAncestorMatches(c =>
            {
                return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
            })
                .InSingletonScope();
            Bind<IProcessingContext>().To<ProcessingContext>().WhenAnyAncestorMatches(c =>
            {
                return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
            })
                .InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<IProcessingContext>().To<ProcessingContext>().WhenNoAncestorMatches(c =>
            {
                return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
            })
                .InTransientScope();
            Bind<ILogger>().To<Log4NetLogger>().WhenAnyAncestorMatches(c =>
            {
                return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
            })
                .InNamedScope(PROCESSING_CONTEXT_NAME);
            Bind<ILogger>().To<Log4NetLogger>().WhenNoAncestorMatches(c =>
            {
                return typeof(IContextualProcessor).IsAssignableFrom(c.Binding.Service);
            })
                .InTransientScope();

            Bind<IRESOQueryConnectorFactory>().To<RESOQueryConnectorFactory>();
            Bind<IBridgeQueryConnector>().To<BridgeQueryConnector>();
            Bind<ITrestleQueryConnector>().To<ThrottledTrestleQueryConnector>();
            Bind<IRapattoniQueryConnector>().To<RapattoniQueryConnector>();
            Bind<IUtahRealEstateQueryConnector>().To<UtahRealEstateQueryConnector>();
            Bind<IRMLSQueryConnector>().To<RMLSQueryConnector>();
            Bind<ISparkQueryConnector>().To<SparkQueryConnector>();
            Bind<IParagonQueryConnector>().To<ParagonQueryConnector>();
            Bind<IPerchwellQueryConnector>().To<PerchwellQueryConnector>();
            Bind<IOpenMLSQueryConnector>().To<OpenMLSQueryConnector>();
            Bind<IMlsGridListingQueryConnector>().To<ThrottledMlsGridListingQueryConnector>();
            Bind<IMLSQueryClient>().To<MLSQueryClient>();

            //delayed event client
            Bind<IDelayedEventClient>().To<DelayedEventClient>();

            Bind<IActiveListingRepository>().To<ActiveListingRepository>();
            Bind<IListingStatusProvider>().To<ListingStatusProvider>();
            Bind<IListingStatusCacher>().To<ListingStatusCacher>();
            Bind<IMlsSynchronizationClient>().To<MlsSynchronizationClient>();
        }
    }
}
