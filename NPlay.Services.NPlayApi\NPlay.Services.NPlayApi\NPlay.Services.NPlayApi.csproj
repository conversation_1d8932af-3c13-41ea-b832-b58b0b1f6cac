﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B50C7FF2-72A6-4AF6-840A-D5A59F4DEA16}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NPlay.Services.NPlayApi</RootNamespace>
    <AssemblyName>NPlay.Services.NPlayApi</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <UseIISExpress>false</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <TypeScriptToolsVersion>2.1</TypeScriptToolsVersion>
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\NPlay.Services.NPlayApi.XML</DocumentationFile>
    <NoWarn>1591,1592,1573,1571,1570,1572</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;RELEASE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\NPlay.Services.NPlayApi.XML</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=5.2.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.5.2.0\lib\net45\AutoMapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.7.4137.9688, Culture=neutral, PublicKeyToken=a4292a325f69b123, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.1.7.0\lib\Net40-Client\BouncyCastle.Crypto.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Castle.Core, Version=*******, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.4.2.0\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="createsend-dotnet">
      <HintPath>..\packages\campaignmonitor-api.4.2.2\lib\net40\createsend-dotnet.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper, Version=27.0.0.0, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>..\packages\CsvHelper.27.1.1\lib\net45\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="Elasticsearch.Net, Version=1.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\Elasticsearch.Net.1.9.0\lib\net45\Elasticsearch.Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\EntityFramework.6.1.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\EntityFramework.6.1.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation, Version=5.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\FluentValidation.5.5.0.0\lib\Net40\FluentValidation.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation.WebApi">
      <HintPath>..\packages\FluentValidation.WebApi.5.5.0.0\lib\Net45\FluentValidation.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="ftplib, Version=1.0.1.2, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ftplib.1.0.1.2\lib\net35\ftplib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis, Version=1.20.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.20.0\lib\net45\Google.Apis.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Analytics.v3, Version=1.20.0.642, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Analytics.v3.1.20.0.642\lib\net45\Google.Apis.Analytics.v3.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=1.20.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.20.0\lib\net45\Google.Apis.Auth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Auth.PlatformServices, Version=1.20.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.20.0\lib\net45\Google.Apis.Auth.PlatformServices.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.20.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.20.0\lib\net45\Google.Apis.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.PlatformServices, Version=1.20.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.20.0\lib\net45\Google.Apis.PlatformServices.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="GoogleAnalyticsTracker.Core, Version=3.1.0.0, Culture=neutral, PublicKeyToken=04ab204e84b117c0, processorArchitecture=MSIL">
      <HintPath>..\packages\GoogleAnalyticsTracker.Core.3.1.0\lib\portable-net45+win+wp80\GoogleAnalyticsTracker.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="GoogleAnalyticsTracker.MVC4, Version=3.1.0.0, Culture=neutral, PublicKeyToken=04ab204e84b117c0, processorArchitecture=MSIL">
      <HintPath>..\packages\GoogleAnalyticsTracker.MVC4.3.1.0\lib\net45\GoogleAnalyticsTracker.MVC4.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="LinqToQuerystring">
      <HintPath>..\packages\LinqToQuerystring.0.7.0.7\lib\net35\LinqToQuerystring.dll</HintPath>
    </Reference>
    <Reference Include="LinqToQuerystring.EntityFramework">
      <HintPath>..\packages\linqtoquerystring.entityframework.1.1.1\lib\net40\LinqToQuerystring.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="LinqToQuerystring.WebApi2">
      <HintPath>..\packages\LinqToQuerystring.WebApi2.1.4.1.14\lib\net45\LinqToQuerystring.WebApi2.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.7.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.7\lib\net45-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.WebApi.MessageHandlers.Compression">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.MessageHandlers.Compression.1.3.0\lib\portable-net45+netcore45+wpa81+wp8+MonoAndroid1+MonoTouch1\Microsoft.AspNet.WebApi.MessageHandlers.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.2.0.4\lib\net45\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.Edm, Version=5.6.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.6.2\lib\net40\Microsoft.Data.Edm.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.6.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.6.2\lib\net40\Microsoft.Data.OData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Nest, Version=1.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\NEST.1.7.2\lib\net45\Nest.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NewRelic.Api.Agent, Version=5.19.47.0, Culture=neutral, PublicKeyToken=06552fced0b33d87, processorArchitecture=MSIL">
      <HintPath>..\packages\NewRelic.Agent.Api.5.19.47.0\lib\NewRelic.Api.Agent.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=8.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.8.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Ninject, Version=3.3.4.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.3.3.4\lib\net45\Ninject.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Extensions.Factory, Version=3.3.3.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Extensions.Factory.3.3.3\lib\net45\Ninject.Extensions.Factory.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Web.Common, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.Common.3.3.2\lib\net45\Ninject.Web.Common.dll</HintPath>
    </Reference>
    <Reference Include="Ninject.Web.Common.WebHost, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.Common.WebHost.3.3.0\lib\net45\Ninject.Web.Common.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Ninject.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.MVC5.3.3.0\lib\net45\Ninject.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Ninject.Web.WebApi, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7, processorArchitecture=MSIL">
      <HintPath>..\packages\Ninject.Web.WebApi.3.3.1\lib\net45\Ninject.Web.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.105.2.3\lib\net46\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.1.0\lib\net46\System.AppContext.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.NonGeneric.4.0.1\lib\net46\System.Collections.NonGeneric.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Console, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.0.0\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Device" />
    <Reference Include="System.Diagnostics.Process, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.Process.4.1.0\lib\net461\System.Diagnostics.Process.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.StackTrace.4.0.1\lib\net46\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.TraceSource.4.0.0\lib\net46\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.FileSystem, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.0.1\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.0.1\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Watcher.4.0.0\lib\net46\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.9\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=4.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.NameResolution.4.0.0\lib\net46\System.Net.NameResolution.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.1.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.TypeExtensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.TypeExtensions.4.1.0\lib\net46\System.Reflection.TypeExtensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.0.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Formatters, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Serialization.Formatters.4.3.0\lib\net46\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Spatial, Version=5.6.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.6.2\lib\net40\System.Spatial.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Thread, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Thread.4.0.0\lib\net46\System.Threading.Thread.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Cors, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.3\lib\net45\System.Web.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.9\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Cors">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Cors.5.2.2\lib\net45\System.Web.Http.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.OData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.OData.5.7.0\lib\net45\System.Web.Http.OData.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.9\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.9\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.9\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.1\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\WebActivatorEx.2.0.5\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Zlib.Portable, Version=1.11.0.0, Culture=neutral, PublicKeyToken=431cba815f6a8b5b, processorArchitecture=MSIL">
      <HintPath>..\packages\Zlib.Portable.Signed.1.11.0\lib\portable-net4+sl5+wp8+win8+wpa81+MonoTouch+MonoAndroid\Zlib.Portable.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Views\AgentServices\" />
    <Folder Include="Views\Auth\" />
    <Folder Include="Views\Brokerage\" />
    <Folder Include="Views\ChrisTest\" />
    <Folder Include="Views\Dashboard\" />
    <Folder Include="Views\Demographics\" />
    <Folder Include="Views\SubscriptionErrors\" />
    <Folder Include="Views\Team\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\ApplicationStateConfig.cs" />
    <Compile Include="App_Start\SettingsConfiguration.cs" />
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\NinjectWebCommon.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Areas\HelpPage\ApiDescriptionExtensions.cs" />
    <Compile Include="Areas\HelpPage\App_Start\HelpPageConfig.cs" />
    <Compile Include="Areas\HelpPage\Controllers\HelpController.cs" />
    <Compile Include="Areas\HelpPage\HelpPageAreaRegistration.cs" />
    <Compile Include="Areas\HelpPage\HelpPageConfigurationExtensions.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\CollectionModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ComplexTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\DictionaryModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumValueDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\IModelDocumentationProvider.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\KeyValuePairModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescriptionGenerator.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameAttribute.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameHelper.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterAnnotation.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\SimpleTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\Models\HelpPageApiModel.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleKey.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ImageSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\InvalidSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ObjectGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\SampleDirection.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\TextSample.cs" />
    <Compile Include="Areas\HelpPage\XmlDocumentationProvider.cs" />
    <Compile Include="Builders\Abstract\ILeadPredicateBuilder.cs" />
    <Compile Include="Builders\Abstract\IReportBuilder.cs" />
    <Compile Include="Builders\Abstract\IServiceModelBuilder.cs" />
    <Compile Include="Builders\Lead\ExpressionExtensions.cs" />
    <Compile Include="Builders\Lead\Hard\AgentContact\AgentContactActivityPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\AgentContact\MessageAgentActivityPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\AgentContact\RequestDetailMarketAnalysisActivityPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\AgentContact\ScheduleShowingActivityPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\HardLeadActivityPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\HardSweepstakeFacebookApplicationPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\Pins\AgentPinPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\Pins\IDXPinPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\Pins\PinActivityBasePredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\Pins\PromoPinPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\Pins\PropertyPinPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Hard\SweepstakeApplicationPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\LeadActivityPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Soft\PageEngageReactionPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Soft\SoftLeadActivityPredicateBuilder.cs" />
    <Compile Include="Builders\Lead\Soft\SoftSweepstakeFacebookApplicationPredicateBuilder.cs" />
    <Compile Include="Builders\Reporting\ReportBuilderBase.cs" />
    <Compile Include="Builders\Reporting\SASignupBasicReportBuilder.cs" />
    <Compile Include="Builders\Reporting\SASignupReportBuilderBase.cs" />
    <Compile Include="Builders\Reporting\SASignupReportBuilder.cs" />
    <Compile Include="Builders\Reporting\SAFunnelBySourceReportBuilder.cs" />
    <Compile Include="Builders\Reporting\SATrafficBySourceReportBuilder.cs" />
    <Compile Include="Builders\Service\HomeValueServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\IDXServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\FeaturedListingsServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\HomeValueAdsServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\ContinuousAdsServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\InstagramPageEngageServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\LeadMagnetServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\ListingLeadAdsServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\PageEngageServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\PageCreateServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\PageManageServiceModelBuilder.cs" />
    <Compile Include="Builders\ServiceModelBuilderBase.cs" />
    <Compile Include="Builders\Service\SearchAllianceServiceModelBuilder.cs" />
    <Compile Include="Builders\Service\SweepstakesServiceModelBuilder.cs" />
    <Compile Include="Commands\Abstract\IAgentCommand.cs" />
    <Compile Include="Commands\Abstract\IAgentCommandFactory.cs" />
    <Compile Include="Commands\Abstract\ICommand.cs" />
    <Compile Include="Commands\Abstract\IContinuousAdsServiceManager.cs" />
    <Compile Include="Commands\Abstract\IIDXServiceManager.cs" />
    <Compile Include="Commands\Abstract\ILeadCommand.cs" />
    <Compile Include="Commands\Abstract\ILeadCommandFactory.cs" />
    <Compile Include="Commands\Abstract\IHomeValueAdsServiceManager.cs" />
    <Compile Include="Commands\Abstract\IListingLeadAdsServiceManager.cs" />
    <Compile Include="Commands\Abstract\IPageCreateServiceManager.cs" />
    <Compile Include="Commands\Abstract\ISearchAllianceServiceManager.cs" />
    <Compile Include="Commands\Abstract\IServiceCommandFactory.cs" />
    <Compile Include="Commands\Abstract\IServiceCommand.cs" />
    <Compile Include="Commands\Agent\AgentCommandBase.cs" />
    <Compile Include="Commands\Agent\AgentMlsCommand.cs" />
    <Compile Include="Commands\Lead\Hard\AgentContact\AgentContactActivityBaseCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\AgentContact\MessageAgentActivityCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\AgentContact\RequestDetailMarketAnalysisActivityCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\AgentContact\ScheduleShowingActivityCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\AgentPinCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\HardLeadActivityCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\AgentPinUpdateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\IDXPinCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\IDXPinUpdateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\PinActivityUpdateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\PromoPinUpdateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\PropertyPinUpdateCommand.cs" />
    <Compile Include="Commands\Lead\LeadActivityCreateCommand.cs" />
    <Compile Include="Commands\Lead\Soft\PageEngageReactionCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\PinActivityCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\PromoPinCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\Pins\PropertyPinCreateCommand.cs" />
    <Compile Include="Commands\Lead\Soft\SoftLeadActivityCreateCommand.cs" />
    <Compile Include="Commands\Lead\Soft\SoftSweepstakeFacebookApplicationCreateCommand.cs" />
    <Compile Include="Commands\Lead\Hard\SweepstakeApplicationCreateCommand.cs" />
    <Compile Include="Commands\Service\HomeValueServiceSetupCommand.cs" />
    <Compile Include="Commands\Service\IDXServiceManager.cs" />
    <Compile Include="Commands\Service\FeaturedListingsServiceSetupCommand.cs" />
    <Compile Include="Commands\Service\HomeValueAdsServiceManager.cs" />
    <Compile Include="Commands\Service\ContinuousAdsServiceManager.cs" />
    <Compile Include="Commands\Service\InstagramPageEngageServiceSetupCommand.cs" />
    <Compile Include="Commands\Service\LeadMagnetServiceSetupCommand.cs" />
    <Compile Include="Commands\Service\ListingLeadAdsServiceManager.cs" />
    <Compile Include="Commands\Service\PageEngageServiceSetupCommand.cs" />
    <Compile Include="Commands\Service\PageCreateServiceManager.cs" />
    <Compile Include="Commands\Service\PageManageServiceSetupCommand.cs" />
    <Compile Include="Commands\Service\SearchAllianceServiceManager.cs" />
    <Compile Include="Commands\Service\ServiceManagerBase.cs" />
    <Compile Include="Commands\Service\SweepstakesServiceSetupCommand.cs" />
    <Compile Include="Controllers\Agent\AgentListingSiteController.cs" />
    <Compile Include="Controllers\Agent\AgentLocationsController.cs" />
    <Compile Include="Controllers\Agent\AgentPostTagFilterController.cs" />
    <Compile Include="Controllers\Agent\AgentSettingsController.cs" />
    <Compile Include="Controllers\Integrations\RatePlug\RatePlugController.cs" />
    <Compile Include="Controllers\Integrations\RatePlug\RatePlugThumbnailResponse.cs" />
    <Compile Include="Controllers\LeadMagnet\LeadMagnetController.cs" />
    <Compile Include="Controllers\Email\EmailArchiveController.cs" />
    <Compile Include="Controllers\FacebookPermissionController.cs" />
    <Compile Include="Controllers\Email\EmailUnsubscribeMembershipController.cs" />
    <Compile Include="Controllers\Email\AgentContactBaseController.cs" />
    <Compile Include="Controllers\Email\AskMyHomeValueController.cs" />
    <Compile Include="Controllers\HomeFundItController.cs" />
    <Compile Include="Controllers\Image\ImageController.cs" />
    <Compile Include="Controllers\Integrations\ContactJunkie.cs" />
    <Compile Include="Controllers\Lead\SelectAgentActivityController.cs" />
    <Compile Include="Controllers\Analytics\SignupStepLogController.cs" />
    <Compile Include="Controllers\Analytics\LoginTrackingController.cs" />
    <Compile Include="Controllers\Authentication\BuyerAuthHandler.cs" />
    <Compile Include="Controllers\Authorization\PermissionsController.cs" />
    <Compile Include="Controllers\LendingTree\LendingTreeController.cs" />
    <Compile Include="Controllers\Listing\ListingAddressController.cs" />
    <Compile Include="Controllers\LoanOfficer\LoanOfficerController.cs" />
    <Compile Include="Controllers\Mls\DataValutSettingController.cs" />
    <Compile Include="Controllers\HomeInspectionController.cs" />
    <Compile Include="Controllers\Payment\AddOnProductController.cs" />
    <Compile Include="Controllers\Payment\CouponController.cs" />
    <Compile Include="Controllers\Payment\InvoiceController.cs" />
    <Compile Include="Controllers\Payment\StripeCheckoutController.cs" />
    <Compile Include="Controllers\Payment\StripeEventController.cs" />
    <Compile Include="Controllers\RealData\ListingReprocessingController.cs" />
    <Compile Include="Controllers\Service\FeaturedListingsServiceController.cs" />
    <Compile Include="Controllers\Payment\StripeCustomerController.cs" />
    <Compile Include="Controllers\Service\HomeValueServiceController.cs" />
    <Compile Include="Controllers\Service\HomeValueAdsServiceController.cs" />
    <Compile Include="Controllers\Service\InstagramPageEngageServiceController.cs" />
    <Compile Include="Controllers\Service\LeadMagnetServiceController.cs" />
    <Compile Include="Controllers\Service\ListingLeadAdsServiceController.cs" />
    <Compile Include="Controllers\Webhooks\MailgunController.cs" />
    <Compile Include="Controllers\WebToolsController.cs" />
    <Compile Include="Controllers\NotificationPreferenceController.cs" />
    <Compile Include="Controllers\Catalog\CatalogController.cs" />
    <Compile Include="Controllers\Facebook\BusinessManagerController.cs" />
    <Compile Include="Controllers\FacebookAds\FacebookAdsController.cs" />
    <Compile Include="Controllers\Heart\ContentGadgetController.cs" />
    <Compile Include="Controllers\Heart\SystemNotificationsController.cs" />
    <Compile Include="Controllers\Login\LoginController.cs" />
    <Compile Include="Controllers\Media\VideoMakerController.cs" />
    <Compile Include="Controllers\Membership\MembershipController.cs" />
    <Compile Include="Controllers\Authorization\RolesController.cs" />
    <Compile Include="Controllers\GatewayController.cs" />
    <Compile Include="Controllers\Contest\ContestController.cs" />
    <Compile Include="Controllers\Heart\DashboardController.cs" />
    <Compile Include="Controllers\Merge\AgentMergeController.cs" />
    <Compile Include="Controllers\Login\RegistrationController.cs" />
    <Compile Include="Controllers\Mls\MlsAgentAuditController.cs" />
    <Compile Include="Controllers\CampaignMonitorController.cs" />
    <Compile Include="Controllers\PromoPostFrequencyController.cs" />
    <Compile Include="Controllers\PageCreateRequestController.cs" />
    <Compile Include="Controllers\Agent\AgentSAReferralsController.cs" />
    <Compile Include="Controllers\Agent\AgentSMSController.cs" />
    <Compile Include="Controllers\Analytics\EventTrackingController.cs" />
    <Compile Include="Controllers\AgentPicker\BidController.cs" />
    <Compile Include="Controllers\PagePoster\PagePosterController.cs" />
    <Compile Include="Controllers\PagePosts\PagePostsController.cs" />
    <Compile Include="Controllers\PagePosts\TagsController.cs" />
    <Compile Include="Controllers\Team\TeamController.cs" />
    <Compile Include="Controllers\Payment\SubscriptionErrorsController.cs" />
    <Compile Include="Controllers\Webhooks\AdobeSignController.cs" />
    <Compile Include="Controllers\Webhooks\FacebookController.cs" />
    <Compile Include="Controllers\Employee\EmployeeController.cs" />
    <Compile Include="Controllers\Authentication\HeartAuthController.cs" />
    <Compile Include="Controllers\Heart\NotesController.cs" />
    <Compile Include="Controllers\HeatMap\HeatMapController.cs" />
    <Compile Include="Controllers\HeatMap\HeatMapIdentityProvider.cs" />
    <Compile Include="Controllers\HeatMap\HeatMapLeadCharger.cs" />
    <Compile Include="Controllers\AgentPicker\LeadTrafficController.cs" />
    <Compile Include="Controllers\Statistics\StatisticsController.cs" />
    <Compile Include="Controllers\BuyerNotificationController.cs" />
    <Compile Include="Controllers\Cookie\CookiesController.cs" />
    <Compile Include="Controllers\Agent\AgentFriendsController.cs" />
    <Compile Include="Controllers\Demographics\DemographicsController.cs" />
    <Compile Include="Controllers\AgentPicker\AgentPickerController.cs" />
    <Compile Include="Controllers\Email\SharePropertyController.cs" />
    <Compile Include="Controllers\HeatMap\AgentPickerSimulationResult.cs" />
    <Compile Include="Controllers\HeatMap\HeatMapTrafficCharger.cs" />
    <Compile Include="Controllers\JoinSearchAlliance\JoinSearchAllianceController.cs" />
    <Compile Include="Controllers\Lead\LeadsController.cs" />
    <Compile Include="Controllers\Listing\ListingSearchHistoryController.cs" />
    <Compile Include="Controllers\Listing\ListingViewController.cs" />
    <Compile Include="Controllers\Logging\LoggingController.cs" />
    <Compile Include="Controllers\MarketingCampaign\MarketingCampaignController.cs" />
    <Compile Include="Controllers\Mls\RETSCredentialController.cs" />
    <Compile Include="Controllers\Queues\CCProcessorController.cs" />
    <Compile Include="Controllers\FreeIdxAgentController.cs" />
    <Compile Include="Controllers\RealData\RealDataController.cs" />
    <Compile Include="Controllers\Service\PageCreateServiceController.cs" />
    <Compile Include="Controllers\Service\SearchAllianceServiceController.cs" />
    <Compile Include="Controllers\Tag\ListingTagController.cs" />
    <Compile Include="Controllers\GeocodeController.cs" />
    <Compile Include="Controllers\AgentReferralController.cs" />
    <Compile Include="Controllers\AgentLeadController.cs" />
    <Compile Include="Controllers\Agent\AgentPageEngageController.cs" />
    <Compile Include="Controllers\Agent\AgentSearchController.cs" />
    <Compile Include="Controllers\Agent\AgentSweepstakesController.cs" />
    <Compile Include="Controllers\Agent\AssociationController.cs" />
    <Compile Include="Controllers\Brokerage\SubscribingBrokerageController.cs" />
    <Compile Include="Controllers\Brokerage\BrokerageController.cs" />
    <Compile Include="Controllers\Agent\CredentialController.cs" />
    <Compile Include="Controllers\Agent\FocalAreaController.cs" />
    <Compile Include="Controllers\Agent\AgentMLSController.cs" />
    <Compile Include="Controllers\Agent\LanguageController.cs" />
    <Compile Include="Controllers\Authentication\BuyerAuthController.cs" />
    <Compile Include="Controllers\Brokerage\BrokerageLogoController.cs" />
    <Compile Include="Controllers\Lead\BaseLeadActivityActionFilter.cs" />
    <Compile Include="Controllers\Connections\ConnectionsController.cs" />
    <Compile Include="Controllers\Lead\BaseLeadActivityController.cs" />
    <Compile Include="Commands\Lead\Hard\HardSweepstakeCreateCommand.cs" />
    <Compile Include="Controllers\Lead\Hard\Pins\PropertyListingPinController.cs" />
    <Compile Include="Controllers\Listing\ListingByTagController.cs" />
    <Compile Include="Controllers\Map\MapController.cs" />
    <Compile Include="Controllers\BulkProcess\CartReminderController.cs" />
    <Compile Include="Controllers\Email\MessageAgentController.cs" />
    <Compile Include="Controllers\Payment\CreditCardBaseController.cs" />
    <Compile Include="Controllers\Email\RequestDetailMarketAnalysisController.cs" />
    <Compile Include="Controllers\Email\ScheduleShowingController.cs" />
    <Compile Include="Controllers\Support\SupportPageController.cs" />
    <Compile Include="Controllers\Templates\TemplatesController.cs" />
    <Compile Include="Controllers\TransactionLog\TransactionLogController.cs" />
    <Compile Include="Controllers\CSAdminController.cs" />
    <Compile Include="Controllers\UserActivity\UserActivityController.cs" />
    <Compile Include="Controllers\WallPostController.cs" />
    <Compile Include="Controllers\Media\VideoController.cs" />
    <Compile Include="Controllers\Payment\CreditCardDefaultController.cs" />
    <Compile Include="Controllers\Payment\CreditCardExpirationController.cs" />
    <Compile Include="Controllers\Payment\StripeChargeController.cs" />
    <Compile Include="Controllers\Payment\StripeSubscriptionController.cs" />
    <Compile Include="Controllers\Payment\FundingSourceController.cs" />
    <Compile Include="Controllers\CitySearchController.cs" />
    <Compile Include="Controllers\Support\SupportEmailController.cs" />
    <Compile Include="Controllers\SweepstakesController.cs" />
    <Compile Include="Controllers\School\SchoolController.cs" />
    <Compile Include="Controllers\SessionController.cs" />
    <Compile Include="Controllers\Webhooks\MandrillController.cs" />
    <Compile Include="Controllers\Yelp\YelpIntegrationController.cs" />
    <Compile Include="Controllers\Zoho\ZohoController.cs" />
    <Compile Include="Extensions\ModelStateExtensions.cs" />
    <Compile Include="Filters\AuthorizeAgentActionAttribute.cs" />
    <Compile Include="Filters\CORSHeaderExceptionFilter.cs" />
    <Compile Include="Filters\InvalidModelState.cs" />
    <Compile Include="Filters\NewRelicExceptionFilter.cs" />
    <Compile Include="Infrastructure\AgentCommandInstanceProvider.cs" />
    <Compile Include="Infrastructure\AgentUnitOfWork.cs" />
    <Compile Include="Infrastructure\LeadCommandInstanceProvider.cs" />
    <Compile Include="Infrastructure\ServiceCommandInstanceProvider.cs" />
    <Compile Include="Infrastructure\UnitOfWorkFactory.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Controllers\Authentication\AgentAuthController.cs" />
    <Compile Include="Controllers\Agent\AgentPageController.cs" />
    <Compile Include="Controllers\Buyer\BuyerController.cs" />
    <Compile Include="Controllers\Lead\Hard\Pins\AgentPinController.cs" />
    <Compile Include="Controllers\Agent\AdCampaignController.cs" />
    <Compile Include="Controllers\Agent\AgentController.cs" />
    <Compile Include="Controllers\Afordal\AgentAfordalController.cs" />
    <Compile Include="Controllers\Agent\AgentCoverPhotoController.cs" />
    <Compile Include="Controllers\Agent\AgentListingController.cs" />
    <Compile Include="Controllers\Agent\AgentPreferencesController.cs" />
    <Compile Include="Controllers\BaseApiController.cs" />
    <Compile Include="Controllers\Agent\AgentBrokerageController.cs" />
    <Compile Include="Controllers\BasePinController.cs" />
    <Compile Include="Controllers\Lead\Hard\HardLeadActivityController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\Lead\Hard\Pins\IDXPinController.cs" />
    <Compile Include="Controllers\Lead\LeadActivityController.cs" />
    <Compile Include="Controllers\Lead\Soft\PageEngageReactionController.cs" />
    <Compile Include="Controllers\Lead\Hard\Pins\PromoPinController.cs" />
    <Compile Include="Controllers\Listing\ListingController.cs" />
    <Compile Include="Controllers\Mls\MlsController.cs" />
    <Compile Include="Controllers\Payment\PaymentController.cs" />
    <Compile Include="Controllers\Payment\CartController.cs" />
    <Compile Include="Controllers\Payment\CreditCardController.cs" />
    <Compile Include="Controllers\Mls\MLSProductPlanController.cs" />
    <Compile Include="Controllers\Payment\StripeSettingsController.cs" />
    <Compile Include="Controllers\Product\ProductPlanController.cs" />
    <Compile Include="Controllers\Product\ProductSignupController.cs" />
    <Compile Include="Controllers\PropertyPinController.cs" />
    <Compile Include="Controllers\Product\ProductController.cs" />
    <Compile Include="Controllers\Service\IDXServiceController.cs" />
    <Compile Include="Controllers\Service\PageEngageServiceController.cs" />
    <Compile Include="Controllers\Service\PageManageServiceController.cs" />
    <Compile Include="Controllers\Service\ServiceController.cs" />
    <Compile Include="Controllers\Lead\Soft\SoftLeadActivityController.cs" />
    <Compile Include="Controllers\Lead\Soft\SoftSweepstakeFacebookApplicationController.cs" />
    <Compile Include="Controllers\Lead\Hard\SweepstakeApplicationController.cs" />
    <Compile Include="Controllers\Mls\StatesController.cs" />
    <Compile Include="Controllers\Service\SweepstakesServiceController.cs" />
    <Compile Include="Filters\ValidateModelAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Infrastructure\ProductSignupInstanceProvider.cs" />
    <Compile Include="Mappings\AutoMapperConfigurationProvider.cs" />
    <Compile Include="Mappings\DomainToViewModelMappingProfile.cs" />
    <Compile Include="Mappings\ModelUrlFactory.cs" />
    <Compile Include="Mappings\ProductSignup\ProductSignupMapping.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Controllers\Reporting\ReportingController.cs" />
    <Compile Include="Services\NinjectWebApiFilterProvider.cs" />
    <Compile Include="Tracing\ExceptionHandler.cs" />
    <Compile Include="Tracing\GlobalExceptionHandler.cs" />
    <Compile Include="Tracing\GlobalExceptionLogger.cs" />
    <Compile Include="Validators\Agent\AgentMlsValidator.cs" />
    <Compile Include="Validators\Helpers\PhoneNumberValidator.cs" />
    <Compile Include="Validators\Helpers\StateCodeValidator.cs" />
    <Compile Include="Validators\Helpers\ZipCodeValidator.cs" />
    <Compile Include="Validators\Lead\Hard\HardLeadActivityValidator.cs" />
    <Compile Include="Validators\Lead\Hard\HardSweepstakeFacebookApplicationValidator.cs" />
    <Compile Include="Validators\Lead\Hard\Pins\AgentPinValidator.cs" />
    <Compile Include="Validators\Lead\Hard\Pins\IDXPinValidator.cs" />
    <Compile Include="Validators\Lead\Hard\Pins\PinActivityValidator.cs" />
    <Compile Include="Validators\Lead\Hard\Pins\PromoPinValidator.cs" />
    <Compile Include="Validators\Lead\Hard\SweepstakeApplicationValidator.cs" />
    <Compile Include="Validators\Lead\LeadActivityValidator.cs" />
    <Compile Include="Validators\Lead\Soft\PageEngageReactionValidator.cs" />
    <Compile Include="Validators\Lead\Soft\SoftLeadActivityValidator.cs" />
    <Compile Include="Validators\Lead\Soft\SoftSweepstakeFacebookApplicationValidator.cs" />
    <Compile Include="Validators\Payment\CartValidator.cs" />
    <Compile Include="Validators\PolymorphicValidator.cs" />
    <Compile Include="Validators\Product\SignupBaseModelValidator.cs" />
    <Compile Include="Validators\Product\SignupContextValidator.cs" />
    <Compile Include="Validators\Service\IDXServiceValidator.cs" />
    <Compile Include="Validators\Service\HomeValueAdsServiceValidator.cs" />
    <Compile Include="Validators\Service\ListingLeadAdsServiceValidator.cs" />
    <Compile Include="Validators\Service\PageEngageServiceValidator.cs" />
    <Compile Include="Validators\Service\PageCreateServiceValidator.cs" />
    <Compile Include="Validators\Service\PageManageServiceValidator.cs" />
    <Compile Include="Validators\Lead\Hard\Pins\PropertyPinValidator.cs" />
    <Compile Include="Validators\Service\SweepstakesServiceValidator.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Areas\HelpPage\HelpPage.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="favicon.ico" />
    <Content Include="Scripts\screenshot\screenshot.js" />
    <Content Include="Services\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Areas\HelpPage\Views\_ViewStart.cshtml" />
    <Content Include="Areas\HelpPage\Views\Web.config" />
    <Content Include="Areas\HelpPage\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\ResourceModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Index.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\TextSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\SimpleTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Samples.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Parameters.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ModelDescriptionLink.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\KeyValuePairModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\InvalidSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ImageSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\HelpPageApiModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\EnumTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\DictionaryModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ComplexTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\CollectionModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ApiGroup.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Api.cshtml" />
    <None Include="Properties\PublishProfiles\dev.pubxml" />
    <None Include="Properties\PublishProfiles\prod.pubxml" />
    <None Include="Properties\PublishProfiles\test.pubxml" />
    <None Include="Scripts\jquery-1.10.2.intellisense.js" />
    <Content Include="Scripts\jquery-1.10.2.js" />
    <Content Include="Scripts\jquery-1.10.2.min.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Content\Site.css" />
    <Content Include="Scripts\_references.js" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Scripts\jquery-1.10.2.min.map" />
    <Content Include="Scripts\screenshot\package-lock.json" />
    <Content Include="Scripts\screenshot\package.json" />
    <Content Include="Scripts\screenshot\temp\.gitkeep" />
    <Content Include="Views\Gateway\Index.cshtml" />
    <None Include="Web.Test.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\MLS.Synchronization.Images\MLS.Synchronization.Images.csproj">
      <Project>{af6000a4-0b3a-461d-a9b6-fd098b54a9bc}</Project>
      <Name>MLS.Synchronization.Images</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\MLS.Synchronization.Models\MLS.Synchronization.Models.csproj">
      <Project>{b851fde4-b17c-4256-b296-9d8fece6a3bc}</Project>
      <Name>MLS.Synchronization.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\Ninject.Web.Mvc.FluentValidation\Ninject.Web.Mvc.FluentValidation.csproj">
      <Project>{fa0bcea6-57ef-4352-975e-f2211129fcba}</Project>
      <Name>Ninject.Web.Mvc.FluentValidation</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.BusinessLogic.AgentListingWebsite\NPlay.BusinessLogic.AgentListingWebsite.csproj">
      <Project>{aa7a0a10-c32f-449c-9f2b-231f77ed130f}</Project>
      <Name>NPlay.BusinessLogic.AgentListingWebsite</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.BusinessLogic.ApiHelper\NPlay.BusinessLogic.ApiHelper.csproj">
      <Project>{eec5c19e-464d-4175-8316-84ebec190338}</Project>
      <Name>NPlay.BusinessLogic.ApiHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.BusinessLogic.CampaignMonitor\NPlay.BusinessLogic.CampaignMonitor.csproj">
      <Project>{342cf37e-ad29-45d0-9c09-128f4a7d1f99}</Project>
      <Name>NPlay.BusinessLogic.CampaignMonitor</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.BusinessLogic.HomeInspection\NPlay.BusinessLogic.HomeInspection.csproj">
      <Project>{e39fead8-0ad5-44d1-adce-05883b28e5ff}</Project>
      <Name>NPlay.BusinessLogic.HomeInspection</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.BusinessLogic.PageEngage\NPlay.BusinessLogic.PageEngage.csproj">
      <Project>{530A6211-EE30-4EB1-8DC7-6F668AF69770}</Project>
      <Name>NPlay.BusinessLogic.PageEngage</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Affiliations\NPlay.Common.Affiliations.csproj">
      <Project>{ba654fed-1a86-40fc-8d66-05162acdf185}</Project>
      <Name>NPlay.Common.Affiliations</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.AgentSMS\NPlay.Common.AgentSMS.csproj">
      <Project>{8ecf79e3-931f-40af-a08c-65f65f745334}</Project>
      <Name>NPlay.Common.AgentSMS</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.BaseRepository\NPlay.Common.BaseRepository.csproj">
      <Project>{a8bf49bd-d362-4bd4-a7c9-61a21599a3c9}</Project>
      <Name>NPlay.Common.BaseRepository</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.CreditCardProcessorQueue\NPlay.Common.CreditCardProcessor.csproj">
      <Project>{80fbf0e2-4f43-4d31-bf0f-7e5853ea8bb2}</Project>
      <Name>NPlay.Common.CreditCardProcessor</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Cryptography\NPlay.Common.Cryptography.csproj">
      <Project>{49702267-357a-442c-ba6e-962a62e27942}</Project>
      <Name>NPlay.Common.Cryptography</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.EmailClient\NPlay.Common.EmailClient.csproj">
      <Project>{7327E117-F824-421E-9CAA-982ACF2BACCD}</Project>
      <Name>NPlay.Common.EmailClient</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Extensions\NPlay.Common.Extensions.csproj">
      <Project>{100f3c8d-7475-41b9-bca0-6e23b9d3b992}</Project>
      <Name>NPlay.Common.Extensions</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Facebook\NPlay.Common.Facebook.csproj">
      <Project>{0c4ebe03-42d9-4d33-8215-434b35133748}</Project>
      <Name>NPlay.Common.Facebook</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Geometry\NPlay.Common.Geometry.csproj">
      <Project>{b5a03f0a-29c2-4522-890a-6a75c6ca87d0}</Project>
      <Name>NPlay.Common.Geometry</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.FacebookModels\NPlay.Common.FacebookModels.csproj">
      <Project>{a20bc7b1-9f7c-48fc-86e4-a2b6a639be7f}</Project>
      <Name>NPlay.Common.FacebookModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Helpers\NPlay.Common.Helpers.csproj">
      <Project>{76637126-033B-4277-B677-C74B8C3B2E5D}</Project>
      <Name>NPlay.Common.Helpers</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Identity\NPlay.Common.Identity.csproj">
      <Project>{19f10ab5-d88c-440e-a8e7-97477ba07c38}</Project>
      <Name>NPlay.Common.Identity</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Logging\NPlay.Common.Logging\NPlay.Common.Logging.csproj">
      <Project>{c01dcec7-5c83-45f2-95bb-c410363b64c0}</Project>
      <Name>NPlay.Common.Logging</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Media\NPlay.Common.Media.csproj">
      <Project>{8b5392a2-cf00-4875-a27c-b43242a14830}</Project>
      <Name>NPlay.Common.Media</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Messaging.Abstract\NPlay.Common.Messaging.Abstract.csproj">
      <Project>{81039773-69EA-4A8E-AE16-147B47F3DD96}</Project>
      <Name>NPlay.Common.Messaging.Abstract</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Messaging.Models\NPlay.Common.Messaging.Models.csproj">
      <Project>{d10605b8-402d-4320-8947-ff66e03c7094}</Project>
      <Name>NPlay.Common.Messaging.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Messaging\NPlay.Common.Messaging.csproj">
      <Project>{F5086483-F4F6-4C2F-A309-0EF282658C3F}</Project>
      <Name>NPlay.Common.Messaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models.ExtendedServices\NPlay.Common.Models.ExtendedServices.csproj">
      <Project>{c8969695-2c5b-4276-98b6-5ac770a4e1b5}</Project>
      <Name>NPlay.Common.Models.ExtendedServices</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models.Mapping\NPlay.Common.Models.Mapping.csproj">
      <Project>{c610bb6a-ab29-43f3-aac3-cf1a37056116}</Project>
      <Name>NPlay.Common.Models.Mapping</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models.Services\NPlay.Common.Models.Services.csproj">
      <Project>{dcb7fefc-327d-4340-9094-585f7a17a02c}</Project>
      <Name>NPlay.Common.Models.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models\NPlay.Common.Models.csproj">
      <Project>{abe52228-12d2-4c13-a8d8-9ca9bda61b7d}</Project>
      <Name>NPlay.Common.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Mortgages\NPlay.Common.Mortgages.csproj">
      <Project>{a0b02756-2127-4810-91ef-6a28578bc738}</Project>
      <Name>NPlay.Common.Mortgages</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Ninject.FluentValidationModule\NPlay.Common.Ninject.FluentValidationModule.csproj">
      <Project>{52c6178a-4a90-4a78-8d8f-99733d4211aa}</Project>
      <Name>NPlay.Common.Ninject.FluentValidationModule</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Ninject.PaymentModule\NPlay.Common.Ninject.PaymentModule.csproj">
      <Project>{cb06ae5c-a12f-42ef-802e-26b26ad18444}</Project>
      <Name>NPlay.Common.Ninject.PaymentModule</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Pdf\NPlay.Common.Pdf.csproj">
      <Project>{cc435df6-c8db-4d07-a732-2a1490fb76bf}</Project>
      <Name>NPlay.Common.Pdf</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Repository\NPlay.Common.Repository.csproj">
      <Project>{1542547d-87c3-4e4c-96b0-e9694c0c427a}</Project>
      <Name>NPlay.Common.Repository</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Resources\NPlay.Common.Resources.csproj">
      <Project>{4a5ef707-94c8-46b0-88fc-ce765c48f9f7}</Project>
      <Name>NPlay.Common.Resources</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Search\NPlay.Common.Search.csproj">
      <Project>{a6e96fd0-3b8a-4a3d-86b6-91817d7f0721}</Project>
      <Name>NPlay.Common.Search</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.ServiceAgents\NPlay.Common.ServiceAgents.csproj">
      <Project>{6291ee8e-ded7-49af-ad28-3f08d66017f4}</Project>
      <Name>NPlay.Common.ServiceAgents</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Services.Abstract\NPlay.Common.Services.Abstract.csproj">
      <Project>{f06efa62-ef3a-4e42-8bf0-e6ce50d23907}</Project>
      <Name>NPlay.Common.Services.Abstract</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Stripe\NPlay.Common.Stripe.csproj">
      <Project>{b1321e49-7a48-43ae-94d3-f2dbe4577ae8}</Project>
      <Name>NPlay.Common.Stripe</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Templates\NPlay.Common.Templates.csproj">
      <Project>{16de9ca2-03d7-404a-bf60-6170b2e69389}</Project>
      <Name>NPlay.Common.Templates</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.TransactionLogging\NPlay.Common.TransactionLogging.csproj">
      <Project>{fd39866e-3c1d-478a-8601-3dd03d85e87d}</Project>
      <Name>NPlay.Common.TransactionLogging</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Web\NPlay.Common.Web.csproj">
      <Project>{e2223afc-22e5-451b-ade3-e1183d8b45a9}</Project>
      <Name>NPlay.Common.Web</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common\NPlay.Common.csproj">
      <Project>{931003bd-1c89-4e22-a66a-461ec1e845c2}</Project>
      <Name>NPlay.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\Nplay.Services.Data\Nplay.Services.Data.csproj">
      <Project>{51068129-4c26-4da4-a55e-c7267768287b}</Project>
      <Name>Nplay.Services.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\RealData.Mapping\RealData.Mapping.csproj">
      <Project>{BE26621C-206E-4598-9682-D37EA5F8DB63}</Project>
      <Name>RealData.Mapping</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\RETS.Models\RETS.Models.csproj">
      <Project>{27DC5F7A-79EC-41CF-A1EF-F9A3BF2E989F}</Project>
      <Name>RETS.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\NPlay.RealDataFlow\BusinessLogic\MLS.Synchronization.Abstract\MLS.Synchronization.Abstract.csproj">
      <Project>{3312698a-5134-4e58-b0b6-d76272f27a90}</Project>
      <Name>MLS.Synchronization.Abstract</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\NPlay.RealDataFlow\BusinessLogic\RETS.Parsing\RETS.Parsing.csproj">
      <Project>{f80949e7-5873-4662-ae7b-0350a0117f17}</Project>
      <Name>RETS.Parsing</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\NPlay.RealDataFlow\BusinessLogic\RET.Web\RETS.Web.csproj">
      <Project>{6f3fe308-3508-49fb-b676-401d7a5b31c9}</Project>
      <Name>RETS.Web</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\NPlay.RealDataFlow\BusinessLogic\RETS.Connector\RETS.Connector.csproj">
      <Project>{b238372a-a59b-4375-b6fb-0e895ba11909}</Project>
      <Name>RETS.Connector</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\NPlay.RealDataFlow\BusinessLogic\MLSAccounts\MLSAccounts.csproj">
      <Project>{a418175c-396e-4432-81f9-f340c3bebfec}</Project>
      <Name>MLSAccounts</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\NPlay.RealDataFlow\MLS.Synchronization\MLS.Synchronization.csproj">
      <Project>{ebfce86a-4321-428b-a5a3-bdffd51b61de}</Project>
      <Name>MLS.Synchronization</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Ads\NPlay.BusinessLogic.Ads.csproj">
      <Project>{3e71125a-1514-40d1-9b70-12c03acd9809}</Project>
      <Name>NPlay.BusinessLogic.Ads</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Authentication\NPlay.BusinessLogic.Authentication.csproj">
      <Project>{225c2321-3971-4b8e-812a-b09615f51269}</Project>
      <Name>NPlay.BusinessLogic.Authentication</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Bidding\NPlay.BusinessLogic.Asap.csproj">
      <Project>{67b79258-4483-428f-b267-4c32d9dc1f66}</Project>
      <Name>NPlay.BusinessLogic.Asap</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.FacebookPageAccessStatusCheck\NPlay.BusinessLogic.FacebookPageAccessStatusCheck.csproj">
      <Project>{ca1eab21-39a0-4f8d-9b44-8e129c63c33f}</Project>
      <Name>NPlay.BusinessLogic.FacebookPageAccessStatusCheck</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.FacebookWebhook\NPlay.BusinessLogic.FacebookWebhook.csproj">
      <Project>{203E15EB-A281-442F-9C06-F42EA34F450A}</Project>
      <Name>NPlay.BusinessLogic.FacebookWebhook</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Idx\NPlay.BusinessLogic.Idx.csproj">
      <Project>{ca089c92-54cf-4976-981d-d5ce32955ed7}</Project>
      <Name>NPlay.BusinessLogic.Idx</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Leads\NPlay.BusinessLogic.Leads.csproj">
      <Project>{6f8d528f-e5c5-4918-90f8-72d2d38431d8}</Project>
      <Name>NPlay.BusinessLogic.Leads</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.LoginRegistration\NPlay.BusinessLogic.LoginRegistration.csproj">
      <Project>{BD3735E2-C952-4E17-891E-AC964BEDB6D4}</Project>
      <Name>NPlay.BusinessLogic.LoginRegistration</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.MergeTool\NPlay.BusinessLogic.MergeTool.csproj">
      <Project>{e0f9d0ad-3855-411f-a8d1-a28621bb5edd}</Project>
      <Name>NPlay.BusinessLogic.MergeTool</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Ninject.Asap\NPlay.BusinessLogic.Ninject.Asap.csproj">
      <Project>{0b2b4cea-b60f-49b7-a3f5-d97c5604771d}</Project>
      <Name>NPlay.BusinessLogic.Ninject.Asap</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.NotificationPreferences\NPlay.BusinessLogic.NotificationPreferences.csproj">
      <Project>{26372261-905F-4BB0-A0B7-D183465A5BD1}</Project>
      <Name>NPlay.BusinessLogic.NotificationPreferences</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Payment\NPlay.BusinessLogic.Payment.csproj">
      <Project>{A1B3074F-C29E-41E0-B54F-4AC8764DC748}</Project>
      <Name>NPlay.BusinessLogic.Payment</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.PermissionsHelper\NPlay.BusinessLogic.PermissionsHelper.csproj">
      <Project>{5a756c4e-479b-4130-8a78-7537fbfd5228}</Project>
      <Name>NPlay.BusinessLogic.PermissionsHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\NPlay.BusinessLogic.Signup\NPlay.BusinessLogic.Signup.csproj">
      <Project>{2d8e99b3-0e2f-4b38-b454-3cf494a49034}</Project>
      <Name>NPlay.BusinessLogic.Signup</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Services\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Services\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Services\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>bin\NPlay.Services.NPlayApi.XML</DocumentationFile>
    <DefineConstants>TEST</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <DocumentationFile>bin\NPlay.Services.NPlayApi.XML</DocumentationFile>
    <NoWarn>1591,1592,1573,1571,1570,1572</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;RELEASE</DefineConstants>
    <DocumentationFile>bin\NPlay.Services.NPlayApi.XML</DocumentationFile>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|x64'">
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>bin\NPlay.Services.NPlayApi.XML</DocumentationFile>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <!--<Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />-->
  <Import Project="..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.12.0.4\tools\VSToolsPath\WebApplications\Microsoft.WebApplication.targets" Condition="Exists('..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.12.0.4\tools\VSToolsPath\WebApplications\Microsoft.WebApplication.targets')" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>51905</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://dev.fb.com/NPlay.Services.NPlayApi</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>