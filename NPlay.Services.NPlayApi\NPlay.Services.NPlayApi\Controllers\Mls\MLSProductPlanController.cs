﻿using NPlay.Common.BaseRepository;
using NPlay.Common.Models;
using NPlay.Common.Models.Services;
using NPlay.Services.NPlayApi.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AutoMapper.QueryableExtensions;
using NPlay.Common;
using NPlay.Common.Abstract;
using AutoMapper;
using NPlay.Common.Enums;
using NPlay.Common.Web.Security.Filters;

namespace NPlay.Services.NPlayApi.Controllers
{
    public class MLSProductPlanController : BaseApiController
    {
        IRepository<MultipleListingServicesProductPlan> MLSPPRepository;
        IRepository<MultipleListingService> MLSRepository;
        IRepository<BrokerageIdxPlan> BrokerPlanRepository;
        IRepository<ProductPlan> ProductPlanRepository;
        ISearchRepository SearchRepository;
        IUnitOfWork UnitOfWork;
        IMapper _mapper;

        public MLSProductPlanController(IRepository<MultipleListingServicesProductPlan> mlsppRepository,
                                        IRepository<MultipleListingService> mlsRepository,
                                        IRepository<BrokerageIdxPlan> brokerplanRepository,
                                        IRepository<ProductPlan> productPlanRepository,
                                        ISearchRepository searchRepository,
                                        IUnitOfWorkFactory uowFactory,
                                        IMapper mapper)
        {
            MLSPPRepository = mlsppRepository;
            MLSRepository = mlsRepository;
            BrokerPlanRepository = brokerplanRepository;
            ProductPlanRepository = productPlanRepository;
            SearchRepository = searchRepository;
            UnitOfWork = uowFactory.Create("Payment");
            _mapper = mapper;
        }

        #region Actions

        // Note: removed because brokerage plans are no longer supported.
        //
        //[Route("api/mls/plans/{mlsid}/{agentmlsid}")]
        //public IHttpActionResult Get(string mlsid, string agentmlsid)
        //{
        //    var agent = SearchRepository.Get<MLSAgent>(SearchIndex.MLSAgents)
        //                                .FirstOrDefault(a => a.MlsIdParsed == mlsid && a.MlsAgentIdParsed == agentmlsid);

        //    if (agent == null)
        //        return Get(mlsid);

        //    var brokerplans = BrokerPlanRepository.GetAll()
        //                                          .Where(b => b.MlsOfficeId == agent.OfficeIdParsed && b.MlsId == mlsid)
        //                                          .Select(b => b.ProductPlan)
        //                                          .Project<ProductPlan>()
        //                                          .To<ProductPlanModel>();

        //    if (brokerplans == null || brokerplans.Count() == 0)
        //        return Get(mlsid);

        //    return Ok(brokerplans);
        //}

        [Route("api/mls/plans/{mlsid}/product/{productid}/{agentmlsid}")]
        public IHttpActionResult Get(string mlsid, string productid, string agentmlsid)
        {
            int productId;
            if (int.TryParse(productid, out productId))
            {
                return Get(mlsid, productId);
            }
            else
            {
                return BadRequest("Invalid ProductId");
            }
        }

        [Route("api/mls/plans/{mlsid}/product/{productid}")]
        public IHttpActionResult Get(string mlsid, int productid = 0)
        {
            ProductType productType = GetProductType(productid);
            IQueryable<ProductPlanModel> productPlans = null;

            if (productType.Equals(ProductType.Bundle))
            {
                var mls = GetMls(mlsid);
                if (mls != null)
                {
                    if (mls.IsActive)
                    {
                        productPlans = ProductPlanRepository.GetAll()
                            .Where(p => p.ProductId == productid)
                            .Where(p => p.Enabled)
                            .Where(p => (p.ProductPlanTypeId == (byte)ProductPlanType.IDXincluded) ||
                                        (p.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded) ||
                                        (p.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded))
                            .ProjectTo<ProductPlanModel>(_mapper.ConfigurationProvider);
                    }
                    else
                    {
                        productPlans = ProductPlanRepository.GetAll()
                            .Where(p => p.ProductId == productid)
                            .Where(p => p.Enabled)
                            .Where(p => (p.ProductPlanTypeId == (byte)ProductPlanType.None) ||
                                        (p.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded))
                            .ProjectTo<ProductPlanModel>(_mapper.ConfigurationProvider);
                    }
                }
            }
            else
            {
                productPlans = MLSPPRepository.GetAll()
                    .Where(m => m.Mlsid == mlsid)
                    .Select(s => s.ProductPlan)
                    .Where(s => s.ProductId == productid)
                    .ProjectTo<ProductPlanModel>(_mapper.ConfigurationProvider);
            }

            if (productPlans == null || !productPlans.Any())
                return NotFound();

            return Ok(productPlans);
        }

        [Route("api/admin/mls/plans/{mlsid}/product/{productid}")]
        [PermissionAuthorize("subscription-management-read")]
        public IHttpActionResult GetProductPlans(string mlsid, int productid = 0)
        {
            return Get(mlsid, productid);
        }

        #endregion

        #region Helpers

        private MultipleListingService GetMls(string mlsId)
        {
            return MLSRepository.GetById(mlsId);
        }

        private ProductType GetProductType(int productid)
        {
            ProductType productType = (ProductType)productid;
            if (!Enum.IsDefined(typeof(ProductType), productType))
            {
                productType = ProductType.IDX;
            }
            return productType;
        }

        #endregion
    }
}