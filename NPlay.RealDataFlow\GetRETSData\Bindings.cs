﻿using MLS.Synchronization;
using MLS.Synchronization.Abstract;
using MLS.Synchronization.Models;
using Ninject.Modules;
using Nplay.Services.Data;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Logging;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Contextual;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Models.MLS;
using NPlay.Common.Queues;
using NPlay.Common.Repository;
using NPlay.Common.Search;
using NPlay.Common.ServiceAgents.Messaging;
using NPlay.Common.ServiceAgents.MLSQuery;
using NPlay.Common.ServiceAgents.NPlayApi;
using NPlay.Common.ServiceAgents.RETSConnections;
using NPlay.Common.ServiceAgents.RETSMetadata;
using NPlay.Common.Services.Abstract;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.ReadDataFlow.Data;
using NPlay.RealDataFlow.Mapping;
using NPlay.RealDataFlow.Mapping.Abstract;
using NPlay.RealDataFlow.Mapping.Models.Maps;
using NPlay.RealDataFlow.RETSConnector;
using NPlay.RealDataFlow.RETSConnector.Abstract;
using RESO.Connector;
using RESO.Connector.Abstract;
using RESO.Synchronization.RESOData;
using RETS.Parsing;
using RETS.Parsing.Abstract;
using RETS.Web;
using RETS.Web.Abstract;
using RETSDataSynchronization;

namespace MlsDataPoller
{
    public class Bindings : NinjectModule
    {
        public override void Load()
        {
            Bind<IRealDataMapper>().To<RealDataMapper>();

            //Setting context
            Bind<IDBFactory<SettingsContext>>().To<DBFactory<SettingsContext>>();
            Bind<IUnitOfWork>().To<UnitOfWork<SettingsContext>>();
            Bind<IRepository<Setting>>().To<EntityRepositoryBase<Setting, SettingsContext>>();

            // Mapping context
            Bind<IDBFactory<MapContext>>().To<DBFactory<MapContext>>();
            Bind<IRepository<Map>>().To<EntityRepositoryBase<Map, MapContext>>();
            Bind<IRepository<MapField>>().To<EntityRepositoryBase<MapField, MapContext>>();
            Bind<IRepository<MapNote>>().To<EntityRepositoryBase<MapNote, MapContext>>();
            Bind<IRepository<Related>>().To<EntityRepositoryBase<Related, MapContext>>();
            Bind<IRepository<MapPropertyType>>().To<EntityRepositoryBase<MapPropertyType, MapContext>>();

            //Mls Context
            Bind<IDBFactory<MlsContext>>().To<DBFactory<MlsContext>>();
            Bind<IRepository<MultipleListingService>>().To<EntityRepositoryBase<MultipleListingService, MlsContext>>();
            Bind<IRepository<RESOCredential>>().To<EntityRepositoryBase<RESOCredential, MlsContext>>();
            Bind<IRepository<RETSCredential>>().To<EntityRepositoryBase<RETSCredential, MlsContext>>();
            Bind<IRepository<MLSParameter>>().To<EntityRepositoryBase<MLSParameter, MlsContext>>();
            Bind<IRepository<DataFeed>>().To<EntityRepositoryBase<DataFeed, MlsContext>>();
            Bind<IRepository<MLSMaster>>().To<EntityRepositoryBase<MLSMaster, MlsContext>>();
            Bind<IMLSRepository>().To<MLSRepository>();
            Bind<IRepository<State>>().To<EntityRepositoryBase<State, MlsContext>>();

            Bind<IEventBroker>().To<PersistentEventBroker>().InSingletonScope();
            Bind<IEventPrismFactory>().To<RabbitEventPrismFactory>().InSingletonScope();
            Bind<IQueueFactory>().To<RabbitQueueFactory>().InSingletonScope();
            Bind<IContextualEventBroker>().To<ContextualEventBroker>().InSingletonScope();
            Bind<IProcessingContext>().To<ProcessingContext>().InThreadScope();

            Bind<IGetMLSFeedData>().To<GetRETSData>();
            Bind<IRETSSearchConnector>().To<RETSSearchConnector>();
            Bind<IFTPConnector>().To<FTPConnector>();
            Bind<IRealDataClient>().To<RealDataClient>();
            Bind<IDataFeedManager>().To<DataFeedManager>();
            Bind<IRETSParser>().To<RETSParser>();
            Bind<IRETSWebClientFactory>().To<RETSWebClientFactory>();
            Bind<IRETSConnectionManagerClient>().To<RETSConnectionManagerClient>();
            Bind<IRETSMetadataManagerClient>().To<RETSMetadataManagerClient>();

            //MLS Synchronization Context
            Bind<IMlsSynchronizationContextProvider>().ToMethod(p => MlsSynchronizationContextProvider.Instance).InSingletonScope();

            //RESO Data Feeds
            Bind<IGetRESOFeedData>().To<GetRESOData>();
            Bind<IRESOQueryConnectorFactory>().To<RESOQueryConnectorFactory>();
            Bind<ITrestleQueryConnector>().To<ThrottledTrestleQueryConnector>();
            Bind<IRapattoniQueryConnector>().To<RapattoniQueryConnector>();
            Bind<IBridgeQueryConnector>().To<BridgeQueryConnector>();
            Bind<IMlsGridListingQueryConnector>().To<ThrottledMlsGridListingQueryConnector>();
            Bind<IMLSQueryClient>().To<MLSQueryClient>();
            Bind<IUtahRealEstateQueryConnector>().To<UtahRealEstateQueryConnector>();
            Bind<IRMLSQueryConnector>().To<RMLSQueryConnector>();
            Bind<ISparkQueryConnector>().To<SparkQueryConnector>();
            Bind<IParagonQueryConnector>().To<ParagonQueryConnector>();
            Bind<IPerchwellQueryConnector>().To<PerchwellQueryConnector>();
            Bind<IOpenMLSQueryConnector>().To<OpenMLSQueryConnector>();

            //RETS Data Feeds
            Bind<IRETSQueryHandler>().To<RETSQueryHandler>();

            //SearchRepo
            Bind<ISearchRepository>().To<SearchRepository>();

            //Logging
            Bind<ILogger>().To<Log4NetLogger>().InThreadScope();

            //delayed event client
            Bind<IDelayedEventClient>().To<DelayedEventClient>();
        }
    }
}
