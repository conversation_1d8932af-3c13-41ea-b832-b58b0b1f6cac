﻿using NPlay.Common.Abstract;
using NPlay.Common.Models;
using System.Linq;
using System.Web.Http;
using NPlay.Common.Web.Security.Filters;
using NPlay.Common.Models.Payments;
using NPlay.Common;
using NPlay.Common.Models.Services;
using NPlay.BusinessLogic.Payment.Abstract;
using System.Collections.Generic;
using System;
using NPlay.Common.Identity.Abstract;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Messaging.Payloads;
using NPlay.Common.Enums;
using AutoMapper;

namespace NPlay.Services.NPlayApi.Controllers.Payment
{
    public class StripeSubscriptionController : ApiController
    {
        private readonly IPaymentSystem PaymentSystem;
        private readonly IRepository<ProductPlan> ProductPlanRepository;
        private readonly IEventBroker EventBroker;
        private readonly ILogger Logger;
        private readonly ICartHelper CartHelper;
        private readonly ISubscriptionHelper SubscriptionHelper;
        private readonly IInvoiceHelper InvoiceHelper;
        private readonly IChargeHelper ChargeHelper;
        private readonly IRepository<Agent> AgentRepository;
        private readonly INPlayIdentityProvider NPlayIdentiyProvider;
        private readonly IRepository<FundingSource> FundingSourceRepository;
        private readonly IMapper Mapper;
        public StripeSubscriptionController(IPaymentSystem paymentSystem,
            IRepository<ProductPlan> productPlanRepository,
            IEventBroker eventBroker,
            ILogger logger,
            ICartHelper cartHelper,
            ISubscriptionHelper subscriptionHelper,
            IInvoiceHelper invoiceHelper,
            IChargeHelper chargeHelper,
            IRepository<Agent> agentRepository,
            INPlayIdentityProvider nPlayIdentiyProvider,
            IRepository<FundingSource> fundingSourceRepository,
            IMapper mapper)
        {
            PaymentSystem = paymentSystem;
            ProductPlanRepository = productPlanRepository;
            EventBroker = eventBroker;
            Logger = logger;
            CartHelper = cartHelper;
            SubscriptionHelper = subscriptionHelper;
            InvoiceHelper = invoiceHelper;
            ChargeHelper = chargeHelper;
            AgentRepository = agentRepository;
            NPlayIdentiyProvider = nPlayIdentiyProvider;
            FundingSourceRepository = fundingSourceRepository;
            Mapper = mapper;
        }

        [Route("api/agent/creditcards/subscriptions")]
        public IHttpActionResult GetSubscriptions(int Limit = 100, string StartingAfter = "")
        {
            int agentid;
            if (!int.TryParse(User.Identity.Name, out agentid))
                return BadRequest();

            var customerId = PaymentSystem.Local.GetCustomerId(agentid);

            if (string.IsNullOrWhiteSpace(customerId))
                return NotFound();

            var subscriptions = PaymentSystem.Subscription.GetSubscriptions(customerId).ToList();

            if (subscriptions == null)
                return NotFound();

            for (int i = 0; i < subscriptions.Count; i++)
            {
                var subscription = subscriptions[i];
                SubscriptionHelper.FillProductPlanDetails(subscription);
            }

            return Ok(subscriptions);
        }

        /// <summary>
        /// Returns the agent's subscriptions.
        /// </summary>
        /// <param name="agentId">Agent id</param>
        /// <param name="all">Boolean representing if subscription of all the status should be returned. If false, cancelled subscriptions are not returned.</param>
        [HttpGet]
        [PermissionAuthorize("subscription-management-read")]
        [Route("api/admin/subscriptions/{agentId}")]
        public IHttpActionResult GetSubscriptionsAdmin(int agentId, bool all = false)
        {
            var customerId = PaymentSystem.Local.GetCustomerId(agentId);

            if (string.IsNullOrWhiteSpace(customerId))
                return NotFound();

            var subscriptions = PaymentSystem.Subscription.GetSubscriptions(customerId, all).ToList();

            if (subscriptions == null)
                return NotFound();

            for (int i = 0; i < subscriptions.Count; i++)
            {
                var subscription = subscriptions[i];
                SubscriptionHelper.FillProductPlanDetails(subscription);
            }

            return Ok(subscriptions);
        }

        [Route("api/agent/creditcards/subscription")]
        public IHttpActionResult GetSubscription(string id)
        {
            int agentid;
            if (!int.TryParse(User.Identity.Name, out agentid))
                return BadRequest();

            var customerId = PaymentSystem.Local.GetCustomerId(agentid);

            if (string.IsNullOrEmpty(customerId))
                return NotFound();

            Subscription subscription = PaymentSystem.Subscription.GetSubscription(customerId, id);

            if (subscription == null)
                return NotFound();

            return Ok(subscription);
        }

        [HttpGet]
        [PermissionAuthorize("subscription-management-read")]
        [Route("api/admin/subscription/{subscriptionId}")]
        public IHttpActionResult GetSubscriptionAdmin(string subscriptionId, [FromUri]string customerId)
        {
            var subscription = PaymentSystem.Subscription.GetSubscription(customerId, subscriptionId);

            if (subscription == null)
                return NotFound();

            SubscriptionHelper.FillProductPlanDetails(subscription);

            return Ok(subscription);
        }

        /// <summary>
        /// Returns subscriptions to be cancelled once a new proudct is purchased.
        /// </summary>
        /// <param name="agentId">Agent Id</param>
        /// <param name="newProductId">New product id. The reason why it is float is because 2.4 (AdPro) could possibly be passed.</param>
        /// <returns>Subscriptions to be cancelled.</returns>
        [HttpGet]
        [PermissionAuthorize("subscription-management-read")]
        [Route("api/admin/subscriptionstobecancelled/{agentId}")]
        public IHttpActionResult GetSubscriptionsToBeCancelledUponNewPurchase(int agentId, [FromUri]string newProductPlanId)
        {
            var subscriptionsToCancell = new List<Subscription>();
            var productPlan = ProductPlanRepository.GetAll().Where(p => p.PlanId == newProductPlanId).FirstOrDefault();
            if (productPlan == null)
                return BadRequest("Could not find the plan details");
            var newProductType = (ProductType)productPlan.ProductId;
            var bundledProducts = CartHelper.GetBundledProductsToCancel(newProductType,
                (productPlan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded ||
                productPlan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded ||
                productPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded));

            if (bundledProducts.Count > 0)
            {
                var customerId = PaymentSystem.Local.GetCustomerId(agentId);

                if (string.IsNullOrWhiteSpace(customerId))
                    return NotFound();

                var subscriptions = PaymentSystem.Subscription.GetSubscriptions(customerId, false).ToList();

                if (subscriptions == null)
                    return NotFound();

                for (int i = 0; i < subscriptions.Count; i++)
                {
                    var subscription = subscriptions[i];
                    SubscriptionHelper.FillProductPlanDetails(subscription);
                }

                foreach (var product in bundledProducts)
                {
                    var fs = PaymentSystem.Local.GetFirstFundingSource(agentId, product);
                    if (fs != null)
                    {
                        var subscription = subscriptions.SingleOrDefault(s => s.Id == fs.SubscriptionId);
                        if (subscription != null)
                            subscriptionsToCancell.Add(subscription);
                    }
                }
            }

            return Ok(subscriptionsToCancell);
        }

        /// <summary>
        /// Simply cancels subscription either immediately or at period end. This will not credit or refund.
        /// </summary>
        [HttpDelete]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/subscription/{subscriptionId}")]
        public IHttpActionResult CancelSubscriptionAdmin(string subscriptionId, [FromUri]bool atPeriodEnd, string homeasapCancelReason)
        {
            if (NPlayIdentiyProvider.EmployeeId == null)
                return Unauthorized();
            var employeeId = NPlayIdentiyProvider.EmployeeId.ToString();
            var fundingSource = PaymentSystem.Local.GetFundingSource(subscriptionId);
            if (fundingSource == null)
                return NotFound();

            var subscription = PaymentSystem.Subscription.GetSubscription(fundingSource.ProviderId, subscriptionId);
            if (subscription == null)
                return NotFound();


            if (!string.IsNullOrEmpty(homeasapCancelReason))
            {
                var metaData = new Dictionary<string, string>();
                metaData.Add(StripeMetaDataNames.HomeasapCancelReason, homeasapCancelReason);
                metaData.Add(StripeMetaDataNames.CancelledByEmployeeId, employeeId);
                subscription.Metadata = metaData;
                try
                {
                    var updateSubscription = PaymentSystem.Subscription.UpdateSubscriptionMetaData(subscription, fundingSource.ProviderId);
                }
                catch (Exception ex)
                {

                    Logger.Error($"Unable to set the metadata on the subscription. {ex.Message}", null, this, "CancelSubscriptionAdmin");
                }
            }

            var cancelResult = SubscriptionHelper.CancelSubscription(subscription, atPeriodEnd);
            if (!string.IsNullOrWhiteSpace(cancelResult.ErrorMessage))
                return BadRequest(cancelResult.ErrorMessage);

            try
            {
                DateTime? trialEnd = subscription.TrialEnd;
                Logger.Info($"Updating funding source trial end date from {fundingSource.TrialEnd?.ToString()} to {trialEnd?.ToString()}");
                fundingSource.TrialEnd = trialEnd;
                if (!atPeriodEnd)
                {
                    fundingSource.Active = false;
                }
                PaymentSystem.Local.SaveFundingSource(fundingSource);
            }
            catch (Exception ex)
            {
                Logger.Error("Unable to update funding source trial end date", null, ex, this, "CancelSubscriptionAdmin");
            }

            subscription.CancelAtPeriodEnd = atPeriodEnd;
            subscription.CanceledAt = cancelResult.DateTime;

            return Ok(subscription);
        }

        [HttpPost]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/createTksSubscription/")]
        public IHttpActionResult CreateTksSubscriptionAdmin(CreateTksSubscriptionRequest request)
        {
            if (NPlayIdentiyProvider.EmployeeId == null)
                return Unauthorized();
            int employeeId = Convert.ToInt32(NPlayIdentiyProvider.EmployeeId);

            if (request.NumberOfMonths <= 0)
                return BadRequest("Number of months needs to be greater than 0.");

            if (request.AgentId <= 0)
                return BadRequest("AgentId needs to be greater than 0.");

            if (DateTime.UtcNow.AddYears(2) < request.TrialEndDate)
                return BadRequest("You cannot set trial end date later than 2 years from now.");

            var agent = AgentRepository.GetById(request.AgentId);
            if (agent == null)
                return NotFound();

            var services = agent.GetProductServiceAbbreviations(false);
            if (services.Contains(ServiceType.PageManagement.GetAbbreviation()))
                return BadRequest("Agent already has TKS.");

            var cards = PaymentSystem.Card.GetCards(request.AgentId);
            var card = cards?.FirstOrDefault(c => c.IsDefault);

            if (card == null)
                return BadRequest("Default credit card is not set.");

            var productPlan = ProductPlanRepository.GetById(request.ProductPlanId);
            if (productPlan == null)
                return BadRequest("ProductPlan not found");

            var unitAmount = productPlan?.Amount ?? 0;
            var baseInvoiceAmount = unitAmount * request.NumberOfMonths;

            var viewModel = new CartDetailsModel();
            var membership = agent.Membership;

            //Step 1: create subscription
            var cartModel = new CartModel
            {
                MlsId = request.MlsId,
                SelectedCardId = card.Id,
                ProductPlanIds = new int[] { request.ProductPlanId },
                Details = new CartDetailsModel()
            };

            var trialEndDate = GetTrialEndDate(request, baseInvoiceAmount);
            // viewModel.ProductPlans gets populated here:
            CartHelper.MapPlansApplyCoupon(cartModel, viewModel, trialEndDate);

            if (viewModel.ProductPlans.Count == 0)
                return BadRequest();

            // Add metadata on the plan.  This metadata gets copied to the funding source in memory, and then
            //  onto the subscription itself when the subscription gets created.
            var plan = viewModel.ProductPlans[0];
            AddSubscriptionMetadata(plan.SubscriptionMetadata, request);
            var subscriptions = CartHelper.CreateFreeSubscriptions(viewModel, membership, request.MlsId, employeeId);

            if (!plan.Created)
                return BadRequest(plan.Error);

            var subscription = subscriptions[0];

            //Step 2: create invoice
            // TODO: Move the future dated invoices for split payments down to after the happy path succeeds in creating the sub and charging the invoice
            var createInvoiceResult = CreateInvoiceForNewTksSubscription(subscription, request, baseInvoiceAmount, employeeId);

            if (!string.IsNullOrWhiteSpace(createInvoiceResult.ErrorMessage))
            {
                //cancel new subscription since we failed to create invoice.
                var cancelResult = SubscriptionHelper.CancelSubscription(subscription, false);

                var text = string.Empty;
                if (string.IsNullOrWhiteSpace(cancelResult.ErrorMessage))
                    text = $"{createInvoiceResult.ErrorMessage} - Canceled new subscription ({subscription.Id}).";
                else
                    text = $"{createInvoiceResult.ErrorMessage} - Also {cancelResult.ErrorMessage} {subscription.Id}.";

                return BadRequest(text);
            }

            var invoice = createInvoiceResult.Invoice;

            //Step 3: finalize invoice
            var finalizeInvoiceResult = InvoiceHelper.FinalizeInvoice(invoice);

            if (!string.IsNullOrWhiteSpace(finalizeInvoiceResult.ErrorMessage))
            {
                //cancel new subscription since we failed to finalize invoice.
                var cancelResult = SubscriptionHelper.CancelSubscription(subscription, false);

                var text = string.Empty;
                if (string.IsNullOrWhiteSpace(cancelResult.ErrorMessage))
                    text = $"{finalizeInvoiceResult.ErrorMessage} - Canceled new subscription ({subscription.Id}). - However, we cannot delete draft invoice assoicated with subscription {invoice.Id} (stripe limitation).";
                else
                    text = $"{finalizeInvoiceResult.ErrorMessage} - Also {cancelResult.ErrorMessage} {subscription.Id}. - Also we cannot delete draft invoice assoicated with subscription {invoice.Id} (stripe limitation).";

                return BadRequest(text);
            }

            invoice = finalizeInvoiceResult.Invoice;

            // Paid should always be false at this point because the above workflow sets AutoAdvance = false, so Stripe doesn't automatically charge
            if (invoice.Paid != true)
            {
                //Step 4: charge invoice
                var chargeInvoiceResult = InvoiceHelper.ChargeInvoice(invoice);

                if (string.IsNullOrWhiteSpace(chargeInvoiceResult.ErrorMessage))
                {
                    invoice = chargeInvoiceResult.Invoice;
                }
                else
                {
                    //void finalized invoice since we were not able to charge.
                    var voidInvoiceResult = InvoiceHelper.VoidOpenInvoice(invoice);

                    //cancel new subscription since we failed to charge.
                    var cancelResult = SubscriptionHelper.CancelSubscription(subscription, false);

                    var text = string.Empty;
                    if (string.IsNullOrWhiteSpace(voidInvoiceResult.ErrorMessage) && string.IsNullOrWhiteSpace(cancelResult.ErrorMessage))
                    {
                        text = $"{chargeInvoiceResult.ErrorMessage} - Voided new invoice. Canceled new subscription ({subscription.Id}).";
                    }
                    else if (!string.IsNullOrWhiteSpace(voidInvoiceResult.ErrorMessage) && string.IsNullOrWhiteSpace(cancelResult.ErrorMessage))
                    {
                        text = $"{chargeInvoiceResult.ErrorMessage} - Also {voidInvoiceResult.ErrorMessage} {invoice.Id}. Canceld new subscription ({subscription.Id}).";
                    }
                    else if (string.IsNullOrWhiteSpace(voidInvoiceResult.ErrorMessage) && !string.IsNullOrWhiteSpace(cancelResult.ErrorMessage))
                    {
                        text = $"{chargeInvoiceResult.ErrorMessage} - Also {cancelResult.ErrorMessage} {subscription.Id}. Voided new invoice.";
                    }
                    else
                        text = $"{chargeInvoiceResult.ErrorMessage} - Also {cancelResult.ErrorMessage} {subscription.Id}. - Also {voidInvoiceResult.ErrorMessage} {invoice.Id}.";

                    return BadRequest(text);
                }
            }

            if (request.IsSplitPayment)
                CreateRemainingSplitPaymentInvoices(subscription, request, baseInvoiceAmount, employeeId);

            //Step 5: update charge description (optional)
            var chargeDescription = ChargeHelper.GetChargeDescriptionForTksSubscription(request.NumberOfMonths, false);
            if (!string.IsNullOrWhiteSpace(invoice.ChargeId))
                ChargeHelper.UpdateChargeDescription(invoice.ChargeId, chargeDescription);

            //Step 6: update payment intent description (optional)
            if (!string.IsNullOrWhiteSpace(invoice.PaymentIntentId))
                ChargeHelper.UpdatePaymentIntentDescription(invoice.PaymentIntentId, chargeDescription);

            cartModel.Details.ProductPlans = viewModel.ProductPlans;
            CartHelper.RegisterServices(cartModel, membership, false);

            return Ok(subscription);
        }

        private void AddSubscriptionMetadata(Dictionary<string, string> metadata, BaseTksSubscriptionRequest request)
        {
            if (request.IsSplitPayment)
            {
                metadata[StripeMetaDataNames.PendingPayments] = "True";
                metadata[StripeMetaDataNames.PendingChargeStatus] = "Pending";
                metadata[StripeMetaDataNames.PendingChargeAmount] = request.NextPayment?.Amount.ToString("#.##");
                metadata[StripeMetaDataNames.PendingChargeDate] = request.NextPayment?.PaymentDate.ToString("yyyy-MM-ddTHH:mm:ssZ");
                metadata[StripeMetaDataNames.PendingChargeEndDateOnSuccess] = request.TrialEndDate.ToString("yyyy-MM-ddTHH:mm:ssZ");
            }
        }

        private DateTime GetTrialEndDate(BaseTksSubscriptionRequest request, decimal baseInvoiceAmount)
        {
            if (!request.FirstSplitPaymentAmount.HasValue)
                return request.TrialEndDate;

            var percentagePaid = (double)(request.FirstSplitPaymentAmount.Value / baseInvoiceAmount);
            TimeSpan projectedTrialPeriod = request.TrialEndDate - request.TrialStartDate;
            var daysPaid = Math.Ceiling(projectedTrialPeriod.TotalDays * percentagePaid);

            return request.TrialStartDate.AddDays(daysPaid);
        }

        [HttpPut]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/RenewTksSubscription")]
        public IHttpActionResult RenewTksSubscriptionAdmin(RenewTksSubscriptionRequest request)
        {
            if (NPlayIdentiyProvider.EmployeeId == null)
                return Unauthorized();
            int employeeId = Convert.ToInt32(NPlayIdentiyProvider.EmployeeId);

            if (request.NumberOfMonths <= 0)
                return BadRequest("Number of months needs to be greater than 0.");

            if (DateTime.UtcNow.AddYears(2) < request.TrialEndDate)
                return BadRequest("You cannot set trial end date later than 2 years from now.");

            var fundingSource = PaymentSystem.Local.GetFundingSource(request.SubscriptionId);
            if (fundingSource == null)
                return NotFound();

            var subscription = PaymentSystem.Subscription.GetSubscription(fundingSource.ProviderId, request.SubscriptionId);
            if (subscription == null)
                return NotFound();

            var unitAmount = subscription.ProductPlan?.Amount ?? 0;
            var baseInvoiceAmount = unitAmount * request.NumberOfMonths;

            //Step 1: create invoice
            var createInvoiceResult = CreateInvoiceForTksRenewal(subscription, request, baseInvoiceAmount, employeeId);

            if (!string.IsNullOrWhiteSpace(createInvoiceResult.ErrorMessage))
                return BadRequest(createInvoiceResult.ErrorMessage);

            var invoice = createInvoiceResult.Invoice;

            //Step 2: finalize invoice
            var finalizeInvoiceResult = InvoiceHelper.FinalizeInvoice(invoice);

            if (!string.IsNullOrWhiteSpace(finalizeInvoiceResult.ErrorMessage))
                return BadRequest($"{finalizeInvoiceResult.ErrorMessage} - Also, we cannot delete draft invoice assoicated with subscription {invoice.Id} (stripe limitation).");

            invoice = finalizeInvoiceResult.Invoice;

            // Paid should always be false at this point because the above workflow sets AutoAdvance = false, so Stripe doesn't automatically charge
            if (invoice.Paid != true)
            {
                //Step 3: charge invoice (optional)
                var chargeInvoiceResult = InvoiceHelper.ChargeInvoice(invoice);

                if (string.IsNullOrWhiteSpace(chargeInvoiceResult.ErrorMessage))
                {
                    invoice = chargeInvoiceResult.Invoice;
                }
                else
                {
                    //void finalized invoice since we were not able to charge.
                    var voidInvoiceResult = InvoiceHelper.VoidOpenInvoice(invoice);

                    var text = string.Empty;
                    if (string.IsNullOrWhiteSpace(voidInvoiceResult.ErrorMessage))
                        text = $"{chargeInvoiceResult.ErrorMessage} - Voided new invoice.";
                    else
                        text = $"{chargeInvoiceResult.ErrorMessage} - Also {voidInvoiceResult.ErrorMessage} {invoice.Id}.";

                    return BadRequest(text);
                }
            }

            //Step 4: update trial end date
            DateTime trialEnd = GetTrialEndDate(request, baseInvoiceAmount);
            Logger.Info($"Updating funding source trial end date from {fundingSource.TrialEnd?.ToString()} to {trialEnd.ToString()}");
            fundingSource.TrialEnd = trialEnd;
            PaymentSystem.Local.SaveFundingSource(fundingSource);

            var updatedSubscriptionResult = SubscriptionHelper.UpdateTrialEndDate(fundingSource, false, false);

            if (!string.IsNullOrWhiteSpace(updatedSubscriptionResult.ErrorMessage))
                return BadRequest($"Succeeded in charging new invoice but {updatedSubscriptionResult.ErrorMessage}");

            if (request.IsSplitPayment)
            {
                //Step 5: split payment handling
                CreateRemainingSplitPaymentInvoices(subscription, request, baseInvoiceAmount, employeeId);
                AddSubscriptionMetadata(updatedSubscriptionResult.Subscription.Metadata, request);
                PaymentSystem.Subscription.UpdateSubscriptionMetaData(updatedSubscriptionResult.Subscription, updatedSubscriptionResult.Subscription.CustomerId);
            }

            //Step 6: update charge description (optional)
            var chargeDescription = ChargeHelper.GetChargeDescriptionForTksSubscription(request.NumberOfMonths, true);
            if (!string.IsNullOrWhiteSpace(invoice.ChargeId))
                ChargeHelper.UpdateChargeDescription(invoice.ChargeId, chargeDescription);

            //Step 7: update payment intent description (optional)
            if (!string.IsNullOrWhiteSpace(invoice.PaymentIntentId))
                ChargeHelper.UpdatePaymentIntentDescription(invoice.PaymentIntentId, chargeDescription);

            return Ok(updatedSubscriptionResult.Subscription);
        }


        [HttpPut]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/RenewSubscription")]
        public IHttpActionResult RenewSubscriptionAdmin(RenewSubscriptionRequest request)
        {
            if (NPlayIdentiyProvider.EmployeeId == null)
                return Unauthorized();
            int employeeId = Convert.ToInt32(NPlayIdentiyProvider.EmployeeId);

            if (request.NumberOfMonths <= 0)
                return BadRequest("Number of months needs to be greater than 0.");

            if (DateTime.UtcNow.AddYears(2) < request.TrialEndDate)
                return BadRequest("You cannot set trial end date later than 2 years from now.");

            var fundingSource = PaymentSystem.Local.GetFundingSource(request.SubscriptionId);
            if (fundingSource == null)
                return NotFound();

            var subscription = PaymentSystem.Subscription.GetSubscription(fundingSource.ProviderId, request.SubscriptionId);
            if (subscription == null)
                return NotFound();

            var unitAmount = subscription.ProductPlan?.Amount ?? 0;
            var monthlyPrice = subscription.ProductPlan?.GetMonthlyPrice();
            var baseInvoiceAmount = monthlyPrice * request.NumberOfMonths;

            //Step 1: create invoice
            var createInvoiceResult = CreateInvoiceForRenewal(subscription, request, Convert.ToDecimal(baseInvoiceAmount), employeeId, fundingSource.ProductTypeId);

            if (!string.IsNullOrWhiteSpace(createInvoiceResult.ErrorMessage))
                return BadRequest(createInvoiceResult.ErrorMessage);

            var invoice = createInvoiceResult.Invoice;

            //Step 2: finalize invoice
            var finalizeInvoiceResult = InvoiceHelper.FinalizeInvoice(invoice);

            if (!string.IsNullOrWhiteSpace(finalizeInvoiceResult.ErrorMessage))
                return BadRequest($"{finalizeInvoiceResult.ErrorMessage} - Also, we cannot delete draft invoice assoicated with subscription {invoice.Id} (stripe limitation).");

            invoice = finalizeInvoiceResult.Invoice;

            // Paid should always be false at this point because the above workflow sets AutoAdvance = false, so Stripe doesn't automatically charge
            if (invoice.Paid != true)
            {
                //Step 3: charge invoice (optional)
                var chargeInvoiceResult = InvoiceHelper.ChargeInvoice(invoice);

                if (string.IsNullOrWhiteSpace(chargeInvoiceResult.ErrorMessage))
                {
                    invoice = chargeInvoiceResult.Invoice;
                }
                else
                {
                    //void finalized invoice since we were not able to charge.
                    var voidInvoiceResult = InvoiceHelper.VoidOpenInvoice(invoice);

                    var text = string.Empty;
                    if (string.IsNullOrWhiteSpace(voidInvoiceResult.ErrorMessage))
                        text = $"{chargeInvoiceResult.ErrorMessage} - Voided new invoice.";
                    else
                        text = $"{chargeInvoiceResult.ErrorMessage} - Also {voidInvoiceResult.ErrorMessage} {invoice.Id}.";

                    return BadRequest(text);
                }
            }

         //   //Step 4: update trial end date
            DateTime trialEnd = request.TrialEndDate;
            Logger.Info($"Updating funding source trial end date from {fundingSource.TrialEnd?.ToString()} to {trialEnd.ToString()}");
            fundingSource.TrialEnd = trialEnd;
            PaymentSystem.Local.SaveFundingSource(fundingSource);

            var updatedSubscriptionResult = SubscriptionHelper.UpdateTrialEndDate(fundingSource, false, false);

            if (!string.IsNullOrWhiteSpace(updatedSubscriptionResult.ErrorMessage))
                return BadRequest($"Succeeded in charging new invoice but {updatedSubscriptionResult.ErrorMessage}");


            //Step 6: update charge description (optional)
            var chargeDescription = $"{fundingSource.ProductType.GetDescription()} early renewal";
            if (!string.IsNullOrWhiteSpace(invoice.ChargeId))
                ChargeHelper.UpdateChargeDescription(invoice.ChargeId, chargeDescription);

            //Step 7: update payment intent description (optional)
            if (!string.IsNullOrWhiteSpace(invoice.PaymentIntentId))
                ChargeHelper.UpdatePaymentIntentDescription(invoice.PaymentIntentId, chargeDescription);

            return Ok(updatedSubscriptionResult.Subscription);
        }

        /// <summary>
        /// Update subscription trial end date for subscription in "trialing" status.
        /// </summary>
        /// <param name="request">request</param>
        /// <returns>Updated subscription.</returns>
        [HttpPut]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/UpdateTrialEndDate")]
        public IHttpActionResult UpdateTrialEndDateAdmin(UpdateTrialEndDateRequest request)
        {
            if (NPlayIdentiyProvider.EmployeeId == null)
                return Unauthorized();
            int employeeId = Convert.ToInt32(NPlayIdentiyProvider.EmployeeId);

            if (request.NumberOfDays <= 0)
                return BadRequest("Number of days needs to be greater than 0");

            if (DateTime.UtcNow.AddYears(2) < request.TrialEndDate)
                return BadRequest("You cannot set trial end date later than 2 years from now.");

            var fundingSource = PaymentSystem.Local.GetFundingSource(request.SubscriptionId);
            if (fundingSource == null)
                return NotFound();

            var subscription = PaymentSystem.Subscription.GetSubscription(fundingSource.ProviderId, request.SubscriptionId);
            if (subscription == null)
                return NotFound();

            //Step 1: update trial end date
            DateTime trialEnd = request.TrialEndDate;
            Logger.Info($"Updating funding source trial end date from {fundingSource.TrialEnd?.ToString()} to {trialEnd.ToString()}");
            fundingSource.TrialEnd = trialEnd;
            var updateTrialEndDateResult = SubscriptionHelper.UpdateTrialEndDate(fundingSource, false, false);

            //Reset fail count when trail end date is updated
            fundingSource.FailCount = 0;
            FundingSourceRepository.Update(fundingSource);
            FundingSourceRepository.SaveChanges();

            if (!string.IsNullOrWhiteSpace(updateTrialEndDateResult.ErrorMessage))
                return BadRequest(updateTrialEndDateResult.ErrorMessage);

            var updatedSubscription = updateTrialEndDateResult.Subscription;

            //Step 2: issue $0 invoice for record
            var createInvoiceResult = CreateInvoiceForUpdateTrialEndDate(updatedSubscription, request, employeeId);

            if (!string.IsNullOrWhiteSpace(createInvoiceResult.ErrorMessage))
                return BadRequest($"Succeeded in updating trial end date but {createInvoiceResult.ErrorMessage}. ($0 invoice for record)");

            return Ok(updateTrialEndDateResult.Subscription);
        }


        [HttpPost]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/moveSubscriptions")]
        public IHttpActionResult MoveSubscriptions([FromBody] MoveSubscriptionsRequest request)
        {
            if (NPlayIdentiyProvider.EmployeeId == null)
                return Unauthorized();
            int employeeId = Convert.ToInt32(NPlayIdentiyProvider.EmployeeId);
            var result = new MoveSubscriptionResultModel();
            Subscription subscription = null;
            FundingSource fundingSource = null;
            bool updateAgentFundingSourceId = false;
            var agent = AgentRepository.GetById(request.AgentId);
            if(agent == null)
            {
                return BadRequest("Agent could not be found");
            }
            var customerAccount = agent?.Membership?.FundingAccountId;
            if(customerAccount  == null)
            {
                customerAccount = agent.Membership.FundingSources?.Count > 0 ? agent.Membership.FundingSources.Where(f => f.Active == true).OrderByDescending(x=>x.LastUpdated).First().ProviderId : string.Empty;
                updateAgentFundingSourceId = false;

            }
            if(string.IsNullOrWhiteSpace(customerAccount))
            {
                subscription = SubscriptionHelper.GetSubscription(request.SubscriptionId);
                var creditCard = PaymentSystem.Card.GetDefaultCard(request.AgentId);
                fundingSource = new FundingSource() { Active = true, MembershipId = request.AgentId, ProviderId = subscription.CustomerId, SubscriptionId = subscription.Id, ProductPlanId = subscription.ProductPlan.Id, ProductTypeId = Convert.ToByte(subscription?.ProductPlan?.ProductId), LastUpdated = DateTime.UtcNow, DateCreated = DateTime.UtcNow, CreditCard = creditCard };
                PaymentSystem.Local.SaveFundingSource(fundingSource);
                result.Result = $"Added funding source record for agent {request.AgentId}";
                return Ok(result);
            }
            if(updateAgentFundingSourceId == true)
            {
                subscription = SubscriptionHelper.GetSubscription(request.SubscriptionId);
                agent.Membership.FundingAccountId = subscription.CustomerId;
                var creditCard = PaymentSystem.Card.GetDefaultCard(request.AgentId);
                fundingSource = new FundingSource() { Active = true, MembershipId = request.AgentId, ProviderId = subscription.CustomerId, SubscriptionId = subscription.Id, ProductPlanId = subscription.ProductPlan.Id, ProductTypeId = Convert.ToByte(subscription?.ProductPlan?.ProductId), LastUpdated = DateTime.UtcNow, DateCreated = DateTime.UtcNow, CreditCard = creditCard };
                PaymentSystem.Local.SaveFundingSource(fundingSource);
                result.Result = $"Added funding source record and updated customerId for agent {request.AgentId} to {subscription.CustomerId}";
                return Ok(result);
            }

            try
            {
                result = SubscriptionHelper.MoveSubscription(customerAccount, request.SubscriptionId, request.AgentId, employeeId);

            }
            catch(Exception ex)
            {
                return BadRequest("Could not move subsciption");
            }

            fundingSource = new FundingSource() { Active = true, MembershipId = request.AgentId, ProviderId = subscription.CustomerId, SubscriptionId = subscription.Id, ProductPlanId = subscription.ProductPlan.Id, ProductTypeId = Convert.ToByte(subscription?.ProductPlan?.ProductId), LastUpdated = DateTime.UtcNow, DateCreated = DateTime.UtcNow };
            PaymentSystem.Local.SaveFundingSource(fundingSource);
            return Ok(result);
        }

        [HttpPut]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/fundingsource/deactivate")]
        public IHttpActionResult DeactivateFundingsource([FromUri]int fundingSourceId)
        {
            var fundingSource = FundingSourceRepository.GetById(fundingSourceId);
            if (fundingSource == null)
                return BadRequest("Could not find fundingsource record.");
            fundingSource.Active = false;
            fundingSource.LastUpdated = DateTime.UtcNow;
            fundingSource.CreatedBy = Enum.GetName(typeof(FundingSourceCreatedByType), FundingSourceCreatedByType.SubscriptionManagerManual);
            FundingSourceRepository.Update(fundingSource);
            FundingSourceRepository.SaveChanges();
            return Ok();
        }

        [HttpPost]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/fundingsource/add")]
        public IHttpActionResult AddFundingsource([FromUri] string subscriptionId,[FromUri] int agentId)
        {
            var subscription = SubscriptionHelper.GetSubscription(subscriptionId);
            SubscriptionHelper.FillProductPlanDetails(subscription);
            if (subscription == null)
                return BadRequest("Could not find the subscription");
            if(subscription.ProductPlan == null ||subscription.ProductPlan.Id == 0 || subscription.ProductPlan.ProductId == 0)
            {
                return BadRequest("Could not get product and plan details from subscription");
            }
            var creditCard = PaymentSystem.Card.GetDefaultCard(agentId);
            var fundingSource = FundingSourceRepository.GetAll().Where(f => f.SubscriptionId == subscriptionId && f.Active == true).FirstOrDefault();
            if(fundingSource != null)
            {
                return BadRequest($"Fundingsource with subscription id {subscriptionId} already exists.");
            }
            fundingSource = new FundingSource() {
                Active = true,
                SubscriptionId=subscriptionId,
                DateCreated = DateTime.UtcNow,
                CCType = creditCard?.Type,
                CCExpirationMonth = Convert.ToInt32(creditCard?.ExpirationMonth),
                CCExpirationYear = Convert.ToInt32(creditCard?.ExpirationYear),
                CCLast4 = creditCard?.Last4,
                ProductPlanId= subscription.ProductPlan?.Id,
                ProviderId = subscription.CustomerId,
                LastUpdated = DateTime.UtcNow,
                ProductTypeId = Convert.ToByte(subscription?.ProductPlan?.ProductId),
                MembershipId = agentId,
                CreatedBy = Enum.GetName(typeof(FundingSourceCreatedByType), FundingSourceCreatedByType.SubscriptionManagerManual),
                FundingSourceType = Common.FundingSourceType.Stripe
            };
            FundingSourceRepository.Add(fundingSource);
            FundingSourceRepository.SaveChanges();
            var result = Mapper.Map<FundingSourceModel>(fundingSource);
            return Ok(result);
        }

        [HttpGet]
        [PermissionAuthorize("subscription-management-read")]
        [Route("api/admin/getstripecustomers")]
        public IHttpActionResult GetStripeCustomersByEmail([FromUri] string email)
        {
            List<Customer> customers = null;
            try
            {
                customers = PaymentSystem.Customer.GetCustomers(email);
            }
            catch (Exception ex)
            {
                return BadRequest("Could not get customers");
            }

            return Ok(customers);
        }

        [HttpPut]
        [PermissionAuthorize("subscription-management-write")]
        [Route("api/admin/undocancelatperiodend")]
        public IHttpActionResult AdminResetCancelAtPeriodEnd([FromUri] string subscriptionId)
        {

            Subscription subscription = PaymentSystem.Subscription.GetSubscription(subscriptionId);
            if(subscription == null)
            {
                return BadRequest("Could not retrieve subscription details");
            }
            try
            {
                subscription = UndoCancelAtPeriodEnd(subscriptionId, subscription.CustomerId);
            }
            catch(Exception ex)
            {
                Logger.Error($"Could not reset cancel at period end for subscription {subscriptionId}", null, ex, this, "AdminResetCancelAtPeriodEnd");
                return BadRequest(ex.Message);
            }

            return Ok(subscription);
        }

        [HttpPut]
        [Authorize]
        [Route("api/undocancelatperiodend")]
        public IHttpActionResult ResetCancelAtPeriodEnd([FromUri] string subscriptionId)
        {
            int agentId;
            if (!int.TryParse(User.Identity.Name, out agentId))
            {
                return BadRequest();
            }
            var agent = AgentRepository.GetById(agentId);
            if (agent == null)
            {
                return BadRequest("Could not find agent");
            }
            var subscription = UndoCancelAtPeriodEnd(subscriptionId, agent?.Membership?.FundingAccountId);

            return Ok(subscription);
        }

        #region Private Methods

        private InvoiceRequestResult CreateInvoiceForTksSubscription(Subscription subscription, decimal negotiatedTotalAmount, decimal baseInvoiceAmount, int numberOfMonths, string invoiceMetadataNote, bool isNewSubscription, int employeeId, bool delayCharging = false, DateTime? dueDate = null, List<Tuple<string, string>> additionalMetadata = null)
        {
            var amountInCents = (int)(negotiatedTotalAmount * 100);

            var invoice = new Invoice
            {
                CustomerId = subscription.CustomerId,
                SubscriptionId = subscription.Id,
                //No auto collection
                AutoAdvance = false,
            };

            if (delayCharging)
            {
                invoice.CollectionMethod = "send_invoice";
                invoice.DueDate = dueDate;
            }

            if (additionalMetadata != null)
            {
                foreach (var metadataItem in additionalMetadata)
                {
                    invoice.Metadata.Add(metadataItem.Item1, metadataItem.Item2);
                }
            }

            invoice.Metadata.Add(StripeMetaDataNames.EmployeeId, employeeId.ToString());

            var baseInvoiceAmountInCents = (int)(baseInvoiceAmount * 100);
            invoice.Metadata.Add(StripeMetaDataNames.BaseInvoiceAmount, baseInvoiceAmountInCents.ToString());
            invoice.Metadata.Add(StripeMetaDataNames.MonthlyTerm, numberOfMonths.ToString());

            if (isNewSubscription)
                invoice.Metadata.Add(StripeMetaDataNames.ChargeType, StripeMetaDataValues.NewTksSubscription);
            else
                invoice.Metadata.Add(StripeMetaDataNames.ChargeType, StripeMetaDataValues.TksRenewal);

            if (!string.IsNullOrWhiteSpace(invoiceMetadataNote))
                invoice.Metadata.Add(StripeMetaDataNames.Note, invoiceMetadataNote);

            if (subscription.ProductPlan != null)
                invoice.Metadata.Add(StripeMetaDataNames.StripePlanId, subscription.ProductPlan.PlanId);

            var invoiceLineItemDescription = ChargeHelper.GetChargeDescriptionForTksSubscription(numberOfMonths, !isNewSubscription);

            //We were using number of months as quantity before but it caused a precision issue when amount cannot be divided by number of months evenly. Therefore we are using quantity of 1.
            var result = InvoiceHelper.CreateInvoice(invoice, amountInCents, 1, invoiceLineItemDescription);
            return result;
        }

        private InvoiceRequestResult CreateInvoiceForSubscription(Subscription subscription, decimal negotiatedTotalAmount, decimal baseInvoiceAmount, int numberOfMonths, string invoiceMetadataNote, bool isNewSubscription, int employeeId, int productId, bool delayCharging = false, DateTime? dueDate = null, List<Tuple<string, string>> additionalMetadata = null)
        {
            var amountInCents = (int)(negotiatedTotalAmount * 100);

            var invoice = new Invoice
            {
                CustomerId = subscription.CustomerId,
                SubscriptionId = subscription.Id,
                //No auto collection
                AutoAdvance = false,
            };

            if (delayCharging)
            {
                invoice.CollectionMethod = "send_invoice";
                invoice.DueDate = dueDate;
            }

            if (additionalMetadata != null)
            {
                foreach (var metadataItem in additionalMetadata)
                {
                    invoice.Metadata.Add(metadataItem.Item1, metadataItem.Item2);
                }
            }

            invoice.Metadata.Add(StripeMetaDataNames.EmployeeId, employeeId.ToString());

            var baseInvoiceAmountInCents = (int)(baseInvoiceAmount * 100);
            invoice.Metadata.Add(StripeMetaDataNames.BaseInvoiceAmount, baseInvoiceAmountInCents.ToString());
            invoice.Metadata.Add(StripeMetaDataNames.MonthlyTerm, numberOfMonths.ToString());
            invoice.Metadata.Add(StripeMetaDataNames.ChargeType, StripeMetaDataValues.EarlyRenewal);
            invoice.Metadata.Add(StripeMetaDataNames.Product, productId.ToString());

            if (!string.IsNullOrWhiteSpace(invoiceMetadataNote))
                invoice.Metadata.Add(StripeMetaDataNames.Note, invoiceMetadataNote);

            if (subscription.ProductPlan != null)
                invoice.Metadata.Add(StripeMetaDataNames.StripePlanId, subscription.ProductPlan.PlanId);

            var invoiceLineItemDescription = "Early renewal";

            //We were using number of months as quantity before but it caused a precision issue when amount cannot be divided by number of months evenly. Therefore we are using quantity of 1.
            var result = InvoiceHelper.CreateInvoice(invoice, amountInCents, 1, invoiceLineItemDescription);
            return result;
        }

        private InvoiceRequestResult CreateInvoiceForNewTksSubscription(Subscription subscription, CreateTksSubscriptionRequest request, decimal baseInvoiceAmount, int employeeId)
        {
            InvoiceRequestResult result;
            if (request.IsSplitPayment)
            {
                result = CreateInvoiceForTksSubscription(subscription, request.FirstSplitPaymentAmount.Value, baseInvoiceAmount, request.NumberOfMonths, request.InvoiceMetadataNote, true, employeeId);
            }
            else
            {
                result = CreateInvoiceForTksSubscription(subscription, request.Amount, baseInvoiceAmount, request.NumberOfMonths, request.InvoiceMetadataNote, true, employeeId);
            }

            return result;
        }

        private InvoiceRequestResult CreateInvoiceForTksRenewal(Subscription subscription, RenewTksSubscriptionRequest request, decimal baseInvoiceAmount, int employeeId)
        {
            InvoiceRequestResult result;
            if (request.IsSplitPayment)
            {
                result = CreateInvoiceForTksSubscription(subscription, request.FirstSplitPaymentAmount.Value, baseInvoiceAmount, request.NumberOfMonths, request.InvoiceMetadataNote, false, employeeId);
            }
            else
            {
                result = CreateInvoiceForTksSubscription(subscription, request.Amount, baseInvoiceAmount, request.NumberOfMonths, request.InvoiceMetadataNote, false, employeeId);
            }

            return result;
        }

        private InvoiceRequestResult CreateInvoiceForRenewal(Subscription subscription, RenewSubscriptionRequest request, decimal baseInvoiceAmount, int employeeId, int productId)
        {
            InvoiceRequestResult result;
            result = CreateInvoiceForSubscription(subscription, request.Amount, baseInvoiceAmount, request.NumberOfMonths, request.InvoiceMetadataNote, false, employeeId, productId);
            return result;
        }

        private void CreateRemainingSplitPaymentInvoices(Subscription subscription, BaseTksSubscriptionRequest request, decimal baseInvoiceAmount, int employeeId)
        {
            string methodName = "CreateRemainingSplitPaymentInvoices";

            foreach (var payment in request.RemainingPayments)
            {
                var pendingPaymentDateMetadata = new Tuple<string, string>(StripeMetaDataNames.PendingChargeDate, payment.PaymentDate.ToString("yyyy-MM-ddTHH:mm:ssZ"));
                var splitPaymentInvoiceResult = CreateInvoiceForTksSubscription(subscription, payment.Amount, baseInvoiceAmount, request.NumberOfMonths, request.InvoiceMetadataNote, true, employeeId, true, payment.PaymentDate, new List<Tuple<string, string>>(new Tuple<string, string>[] { pendingPaymentDateMetadata }));

                // If we ever do fully support multiple split payments, we will have to think through the error handling scenarios very carefully.
                //  We may need to revert all the operations that have happened so far.
                Logger.Info("Finalizing the invoice", null, this, methodName);
                var finalizedInvoiceResult = InvoiceHelper.FinalizeInvoice(splitPaymentInvoiceResult.Invoice);
                if (finalizedInvoiceResult.HasError)
                {
                    Logger.Error(finalizedInvoiceResult.ErrorMessage, null, this, methodName);

                    throw finalizedInvoiceResult.Exception;
                }

                EventBroker.Publish<TksSubscriptionInvoicePaymentDueEvent, TksSubscriptionInvoicePayload>(new TksSubscriptionInvoicePaymentDueEvent()
                {
                    Payload = new TksSubscriptionInvoicePayload()
                    {
                        InvoiceId = splitPaymentInvoiceResult.Invoice.Id,
                        NumberOfMonths = request.NumberOfMonths,
                        IsRenewal = request is RenewTksSubscriptionRequest,
                    }
                }, payment.PaymentDate);
            }
        }

        /// <summary>
        /// Issue $0 invoice for extending trial end date for record
        /// </summary>
        private InvoiceRequestResult CreateInvoiceForUpdateTrialEndDate(Subscription subscription, UpdateTrialEndDateRequest request, int employeeId)
        {
            var invoice = new Invoice
            {
                CustomerId = subscription.CustomerId,
                SubscriptionId = subscription.Id
            };

            invoice.Metadata.Add(StripeMetaDataNames.EmployeeId, employeeId.ToString());

            if (!string.IsNullOrWhiteSpace(request.InvoiceMetadataNote))
                invoice.Metadata.Add(StripeMetaDataNames.Note, request.InvoiceMetadataNote);

            if (subscription.ProductPlan != null)
                invoice.Metadata.Add(StripeMetaDataNames.StripePlanId, subscription.ProductPlan.PlanId);

            SubscriptionHelper.FillProductPlanDetails(subscription);
            var productName = subscription.ProductPlan != null ? subscription.ProductPlan.ProductType.Description() : "Unknown Product";

            var result = InvoiceHelper.CreateInvoice(invoice, 0, request.NumberOfDays, $"{productName} Trial");
            return result;
        }

        private Subscription UndoCancelAtPeriodEnd(string subscriptionId, string customerId)
        {
            var subscription = SubscriptionHelper.UndoCancelAtPeriodEnd(subscriptionId, customerId);
            return subscription;
        }

        #endregion
    }
}
