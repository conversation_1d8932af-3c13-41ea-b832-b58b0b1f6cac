﻿using NPlay.BusinessLogic.Payment.Abstract;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Enums;
using NPlay.Common.Identity;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Models;
using NPlay.Common.Models.Payments;
using NPlay.Common.Models.Services;
using NPlay.Common.Web.Abstract;
using Stripe.Constants;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace NPlay.BusinessLogic.Payment
{
    public class CartHelper : ICartHelper
    {
        private readonly IRepository<ProductPlan> ProductPlanRepository;
        private readonly IPaymentSystem PaymentSystem;
        private readonly IRepository<MultipleListingService> MlsRepository;
        private readonly IRepository<Agent> AgentRepository;
        private readonly IRepository<Product> ProductRepository;
        private readonly ICustomerHelper CustomerHelper;
        private readonly IUnitOfWork UnitOfWork;
        private readonly IEventBroker EventBroker;
        private readonly IInvoiceHelper InvoiceHelper;
        private readonly IPaymentIntentLogic PaymentIntentLogic;
        private readonly ILogger Logger;

        public CartHelper(IRepository<ProductPlan> productPlanRepository,
            IPaymentSystem paymentSystem,
            IRepository<MultipleListingService> mlsRepository,
            IRepository<Agent> agentRepository,
            IRepository<Product> productRepository,
            ICustomerHelper customerHelper,
            IUnitOfWorkFactory uowFactory,
            IEventBroker eventBroker,
            IInvoiceHelper invoiceHelper,
            IPaymentIntentLogic paymentIntentLogic,
            ILogger logger)
        {
            ProductPlanRepository = productPlanRepository;
            PaymentSystem = paymentSystem;
            MlsRepository = mlsRepository;
            AgentRepository = agentRepository;
            ProductRepository = productRepository;
            CustomerHelper = customerHelper;
            UnitOfWork = uowFactory.Create("Agent");
            EventBroker = eventBroker;
            InvoiceHelper = invoiceHelper;
            PaymentIntentLogic = paymentIntentLogic;
            Logger = logger;
        }

        public void MapPlansApplyCoupon(CartModel value, CartDetailsModel viewModel, DateTime? tksAdminTrialEndDate)
        {
            viewModel.ProductPlans = new List<CartPlanModel>();

            //obtain selected plans from db
            var plans = ProductPlanRepository.GetAll()
                                             .Where(p => value.ProductPlanIds.Contains(p.Id))
                                             .ToList();

            //map plans and apply coupon for each product
            foreach (var plan in plans)
            {
                var cartPlan = new CartPlanModel
                {
                    Id = plan.Id,
                    Amount = plan.Amount,
                    Description = plan.Description,
                    PostCouponAmount = plan.Amount,
                    ProductType = plan.ProductType,
                    Subscription = plan.Subscription,
                    ProductPlanTypeId = plan.ProductPlanTypeId
                };

                if (tksAdminTrialEndDate != null)
                    cartPlan.TrialEnd = tksAdminTrialEndDate.Value;
                else
                {
                    cartPlan.Fee = plan.Fee;
                    cartPlan.FeeDescription = plan.FeeDescription;
                    cartPlan.TrialEnd = (plan.TrialPeriodDays.HasValue && plan.TrialPeriodDays.Value > 0) ? (DateTime?)DateTime.Now.AddDays(plan.TrialPeriodDays.Value) : null;
                }

                var coupon = PaymentSystem.Local.GetEnabledCoupon(value.CouponCode, plan.ProductType);

                if (coupon != null)
                {
                    cartPlan.CouponId = coupon.Id;
                    cartPlan.CouponCode = coupon.PublicCode;
                    cartPlan.PostCouponAmount = PaymentSystem.Local.ApplyCoupon(coupon, plan);
                    cartPlan.CouponDescription = coupon.Description;
                }


                // Check if the MLS is pending, and if so set the trial end to way in the future
                if (tksAdminTrialEndDate == null && !string.IsNullOrEmpty(value.MlsId) && cartPlan.ProductType == ProductType.IDX)
                {
                    var mls = MlsRepository.GetById(value.MlsId);
                    if ((mls != null) && mls.IsPending)
                    {
                        cartPlan.TrialEnd = DateTime.UtcNow.AddYears(4);  // Arbitrary date in the future.  Stripe will not yet you set a trial to last for more than 5 years
                    }
                }

                viewModel.ProductPlans.Add(cartPlan);
            }
        }

        public void CreateSubscriptions(CartDetailsModel viewModel, CreditCard card, Membership agent, string mlsId, int? employeeId, string source)
        {
            var customer = CustomerHelper.GetCustomer(agent);
            CustomerHelper.UpdateCustomerMetadata(customer, agent, mlsId);

            foreach (var plan in viewModel.ProductPlans)
            {
                FundingSource fs;
                if (plan.Fee.HasValue && viewModel.TrialPeriodDays > 0)
                {
                    plan.Error = "Cannot create a trial subscription for a plan with one time fee";
                    plan.Created = false;
                    continue;
                }
                    if (!plan.Subscription || plan.ProductType == ProductType.PageCreate || plan.ProductType == ProductType.Ads)
                    fs = new FundingSource();
                else
                    fs = PaymentSystem.Local.GetFirstFundingSource(agent.Id, plan.ProductType) ?? new FundingSource();

                fs.MembershipId = agent.Id;
                fs.ProviderId = agent.FundingAccountId;
                fs.CreditCard = card;
                fs.ProductPlanId = plan.Id;
                fs.TrialEnd = plan.TrialEnd;
                fs.CouponId = plan.CouponId;
                fs.ProductType = plan.ProductType;

                if (card != null && !string.IsNullOrWhiteSpace(card.Id))
                {
                    fs.CCExpirationMonth = int.Parse(card?.ExpirationMonth);
                    fs.CCExpirationYear = int.Parse(card?.ExpirationYear);
                    fs.CCLast4 = card?.Last4;
                    fs.CCType = card?.Type;
                }
                fs.CreatedBy = source;

                PaymentIntent setupFeePaymentIntent = null;
                try
                {
                    decimal fee = 0;
                    string feedescription = string.Empty;
                    var isFeeInitialSetup = false;
                    int trialPeriodDays = viewModel.TrialPeriodDays ?? 0;
                    var cancelAtPeriodEnd = false;
                    if(plan.Amount == 0)
                    {
                        cancelAtPeriodEnd = true;
                    }

                    if (plan.Subscription)
                    {
                        if (plan.Fee.HasValue)
                        {
                            fee = plan.Fee.Value;
                            feedescription = plan.FeeDescription;
                            isFeeInitialSetup = true;
                        }
                    }
                    else
                    {
                        fee = plan.Amount;
                        feedescription = plan.Description;
                    }

                    // Step 1: Create setup fee payment intent with manual capture (if fee exists)
                    if (fee > 0)
                    {
                        var onetimefee = new NPlay.Common.Models.Payment
                        {
                            AgentId = fs.MembershipId,
                            Amount = fee,
                            ProductType = plan.ProductType,
                            Description = feedescription,
                            IsInitialSetupFee = isFeeInitialSetup
                        };
                        int feeInCents = (int)(fee * 100);

                        // Use manual capture to authorize but not charge until subscription succeeds
                        setupFeePaymentIntent = PaymentIntentLogic.CreatePaymentIntent(agent.FundingAccountId, card.Id, feeInCents, StripeCaptureMethods.Manual, employeeId, onetimefee);
                    }

                    // Step 2: Create subscription or handle non-subscription plans
                    if (plan.Subscription)
                    {
                        var subscription = PaymentSystem.Subscription.CreateSubscription(fs, employeeId, trialPeriodDays, cancelAtPeriodEnd);

                        if (employeeId != null && !string.IsNullOrWhiteSpace(subscription.LatestInvoiceId))
                        {
                            var invoice = PaymentSystem.Invoice.GetInvoice(subscription.LatestInvoiceId);
                            InvoiceHelper.UpdateInvoiceMetadataWithEmployeeId(invoice, employeeId.Value);
                        }

                        // Step 3: If subscription creation succeeded and we have a setup fee, capture it now
                        if (setupFeePaymentIntent != null)
                        {
                            try
                            {
                                PaymentIntentLogic.CapturePaymentIntent(setupFeePaymentIntent);
                            }
                            catch (Exception feeException)
                            {
                                // If fee capture fails, log error but don't fail the entire operation since subscription succeeded
                                // The payment intent will remain uncaptured and can be handled manually
                                Logger.Error($"Failed to capture setup fee for agent {agent.Id}, subscription {subscription.Id}. PaymentIntent: {setupFeePaymentIntent.Id}. Error: {feeException.Message}",
                                    new List<LoggingPair> {
                                        new LoggingPair(LoggingKey.AgentId, agent.Id),
                                        new LoggingPair(LoggingKey.SubscriptionId, subscription.Id),
                                        new LoggingPair(LoggingKey.PaymentIntentId, setupFeePaymentIntent.Id)
                                    }, feeException, this, "CreateSubscriptions");
                            }
                        }
                    }
                    else
                    {
                        // For non-subscription plans (one-time purchases), capture the setup fee immediately
                        // since there's no subscription creation that could fail
                        if (setupFeePaymentIntent != null)
                        {
                            try
                            {
                                PaymentIntentLogic.CapturePaymentIntent(setupFeePaymentIntent);
                            }
                            catch (Exception feeException)
                            {
                                // If fee capture fails for non-subscription plan, this is a failure since there's no other service being provided
                                Logger.Error($"Failed to capture setup fee for non-subscription plan for agent {agent.Id}. PaymentIntent: {setupFeePaymentIntent.Id}. Error: {feeException.Message}",
                                    new List<LoggingPair> {
                                        new LoggingPair(LoggingKey.AgentId, agent.Id),
                                        new LoggingPair(LoggingKey.PaymentIntentId, setupFeePaymentIntent.Id)
                                    }, feeException, this, "CreateSubscriptions");
                                throw; // Re-throw to trigger the catch block and mark plan.Created = false
                            }
                        }
                    }

                    fs.Active = true;
                    plan.Created = true;

                    PaymentSystem.Local.SaveFundingSource(fs);

                    if (plan.ProductType == ProductType.Bundle || plan.ProductType == ProductType.PageEngage || plan.ProductType == ProductType.PEInstagramBundle)
                        CancelBundledSubscriptions(agent, plan.ProductType, (plan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded) ||
                            (plan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded) ||
                            (plan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded));
                }
                catch (Exception error)
                {
                    // If plan creation failed and we have an uncaptured setup fee payment intent, cancel it
                    if (setupFeePaymentIntent != null)
                    {
                        try
                        {
                            PaymentIntentLogic.CancelPaymentIntent(setupFeePaymentIntent.Id);
                            Logger.Info($"Cancelled setup fee payment intent {setupFeePaymentIntent.Id} due to plan creation failure for agent {agent.Id}",
                                new List<LoggingPair> {
                                    new LoggingPair(LoggingKey.AgentId, agent.Id),
                                    new LoggingPair(LoggingKey.PaymentIntentId, setupFeePaymentIntent.Id)
                                }, this, "CreateSubscriptions");
                        }
                        catch (Exception cancelException)
                        {
                            Logger.Error($"Failed to cancel setup fee payment intent {setupFeePaymentIntent.Id} for agent {agent.Id}. Manual intervention may be required. Error: {cancelException.Message}",
                                new List<LoggingPair> {
                                    new LoggingPair(LoggingKey.AgentId, agent.Id),
                                    new LoggingPair(LoggingKey.PaymentIntentId, setupFeePaymentIntent.Id)
                                }, cancelException, this, "CreateSubscriptions");
                        }
                    }

                    plan.Error = error.Message;
                    plan.Created = false;
                    CardDeclined statusChanged = new CardDeclined { agentId = agent.Id, Reason = error.Message };
                    CardDeclinedEvent cardDeclinedEvent = new CardDeclinedEvent() { Payload = statusChanged };
                    EventBroker.Publish<CardDeclinedEvent, CardDeclined>(cardDeclinedEvent);
                }
            }
        }

        public List<Subscription> CreateFreeSubscriptions(CartDetailsModel viewModel, Membership agent, string mlsId, int? employeeId)
        {
            var customer = CustomerHelper.GetCustomer(agent);
            var subscriptions = new List<Subscription>();

            foreach (var plan in viewModel.ProductPlans)
            {
                FundingSource fs;
                if (plan.ProductType == ProductType.Ads || !plan.Subscription)
                    fs = new FundingSource();
                else
                    fs = PaymentSystem.Local.GetFirstFundingSource(agent.Id, plan.ProductType) ?? new FundingSource();

                fs.MembershipId = agent.Id;
                fs.ProductPlanId = plan.Id;
                fs.TrialEnd = plan.TrialEnd;
                fs.CouponId = plan.CouponId;
                fs.ProductType = plan.ProductType;

                // Merge all plan metadata values into the funding source metadata collection.
                // This propagates metadata values ultimately onto the subscription.
                foreach (var metadataItem in plan.SubscriptionMetadata)
                {
                    fs.Metadata[metadataItem.Key] = metadataItem.Value;
                }

                if (customer == null)
                {
                    customer = PaymentSystem.Customer.CreateCustomer(fs, mlsId);
                    PaymentSystem.Local.SaveCustomerId(agent.Id, customer.Id);
                }
                else
                    CustomerHelper.UpdateCustomerMetadata(customer, agent, mlsId);

                var defaultCard = customer.DefaultCard;
                if (defaultCard == null && !string.IsNullOrWhiteSpace(customer.DefaultCardId) && customer.CreditCards != null)
                    defaultCard = customer.CreditCards.FirstOrDefault(c => c.Id == customer.DefaultCardId);
                if (defaultCard != null)
                {
                    var expMonth = 0;
                    var expYear = 0;
                    int.TryParse(defaultCard.ExpirationMonth, out expMonth);
                    int.TryParse(defaultCard.ExpirationYear, out expYear);

                    if (expMonth != 0)
                        fs.CCExpirationMonth = expMonth;
                    if (expYear != 0)
                        fs.CCExpirationYear = expYear;
                    if (!string.IsNullOrWhiteSpace(defaultCard.Last4))
                        fs.CCLast4 = defaultCard.Last4;
                    if (!string.IsNullOrWhiteSpace(defaultCard.Type))
                        fs.CCType = defaultCard.Type;
                }

                fs.ProviderId = customer.Id;

                try
                {
                    if (plan.Subscription)
                    {
                        var subscription = PaymentSystem.Subscription.CreateSubscription(fs, employeeId);

                        if (employeeId != null && !string.IsNullOrWhiteSpace(subscription.LatestInvoiceId))
                        {
                            var invoice = PaymentSystem.Invoice.GetInvoice(subscription.LatestInvoiceId);
                            InvoiceHelper.UpdateInvoiceMetadataWithEmployeeId(invoice, employeeId.Value);
                        }

                        subscriptions.Add(subscription);

                        fs.Active = true;
                        plan.Created = true;

                        PaymentSystem.Local.SaveFundingSource(fs);

                        if (plan.ProductType == ProductType.Bundle || plan.ProductType == ProductType.PageEngage)
                            CancelBundledSubscriptions(agent, plan.ProductType, (plan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded) ||
                                (plan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded) ||
                                (plan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded));

                    }
                }
                catch (Exception error)
                {
                    plan.Error = error.Message;
                    plan.Created = false;
                }
            }
            return subscriptions;
        }

        public void CancelBundledSubscriptions(Membership agent, ProductType productType, bool? isInstagramIncluded)
        {
            var bundledProducts = GetBundledProductsToCancel(productType, isInstagramIncluded);

            foreach (var product in bundledProducts)
            {
                var fs = PaymentSystem.Local.GetFirstFundingSource(agent.Id, product);
                if (fs != null)
                {
                    try
                    {
                        var dateCancelled = PaymentSystem.Subscription.CancelSubscription(fs.Id);
                        fs.SubscriptionEndDate = dateCancelled;
                        fs.NextInvoiceDate = null;
                        fs.Active = false;
                        fs.LastUpdated = DateTime.UtcNow;
                        PaymentSystem.Local.SaveFundingSource(fs);
                    }
                    catch (Exception ex)
                    {
                        string msg = string.Format("Existing {0} subscription cancellation failed after TKS purchase. ExceptionType={1}, Message={2}",
                            product.ToString(), ex.GetType().Name, ex.Message);
                        Trace.TraceError(msg);
                    }
                }
            }
        }

        public List<ProductType> GetBundledProductsToCancel(ProductType productType, bool? isInstagramIncluded)
        {
            var bundledProducts = new List<ProductType>();

            if (productType == ProductType.Bundle)
            {
                bundledProducts = new List<ProductType> {
                    ProductType.IDX,
                    ProductType.PageEngage,
                    ProductType.Sweepstakes,
                    ProductType.HomeValue,
                    ProductType.FeaturedListings,
                    ProductType.DirectoryPro,
                };
                if (isInstagramIncluded == true)
                {
                    bundledProducts.Add(ProductType.InstagramPageEngage);
                }
            }
            else if (productType == ProductType.PageEngage)
            {
                bundledProducts = new List<ProductType> {
                    ProductType.FeaturedListings
                };
            }
            else if (productType == ProductType.PEInstagramBundle)
            {
                bundledProducts = new List<ProductType> {
                    ProductType.FeaturedListings,
                    ProductType.InstagramPageEngage,
                    ProductType.PageEngage
                };
            }

            return bundledProducts;
        }

        /// <summary>
        /// Creates an account event for each purchased service.
        /// </summary>
        /// <param name="cart">CartModel</param>
        /// <param name="member">Membership</param>
        public void RegisterServices(CartModel cart, Membership member, bool updateCookie)
        {
            try
            {
                Agent agent = AgentRepository.GetById(member.Id);
                if (agent != null)
                {
                    var tksPlan = cart.Details.ProductPlans.FirstOrDefault(p => p.ProductType == ProductType.Bundle);
                    var setIDXDate = false;
                    if (tksPlan != null)
                    {
                        setIDXDate = tksPlan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded ||
                            tksPlan.ProductPlanTypeId == (byte)ProductPlanType.IDXincluded ||
                            tksPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded;
                    }
                    var serviceTypes = GetServiceTypes(cart.Details);
                    foreach (var s in serviceTypes)
                    {
                        // Create registration account event
                        var accountEvent = GetAccountEventFor(s, cart, agent);
                        if (accountEvent != null)
                        {
                            var eventTypeIds = GetAllAccountEventTypeIdsFor(s);
                            var closeEvents = agent.AccountEvents.Where(e => e.Enabled && eventTypeIds.Contains(e.EventId)).ToList();
                            foreach (var c in closeEvents)
                                c.Enabled = false;

                            agent.AccountEvents.Add(accountEvent);
                        }

                        // Handle service-specific requirements
                        switch (s)
                        {
                            case ServiceType.IDX:
                                agent.ListingsImportedDateTime = DateTime.UtcNow;

                                EventBroker.Publish<IDXRequestedEvent, BasicAgent>(new IDXRequestedEvent()
                                {
                                    Payload = new BasicAgent()
                                    {
                                        Id = agent.Id
                                    }
                                });
                                break;
                            case ServiceType.PageManagement:
                                if (setIDXDate)
                                {
                                    agent.ListingsImportedDateTime = DateTime.UtcNow;
                                }
                                break;
                        }
                    }
                    UnitOfWork.Commit();

                    if (updateCookie)
                        UpdateAuthCookie(agent);
                }
            }
            catch (Exception e)
            {
                Trace.TraceError(String.Format("RegisterServices failed for MemberId: {0}, Error: {1}",
                    member.Id, e.Message));
            }
        }

        public void ComputeTotals(CartDetailsModel viewModel)
        {
            foreach (var plan in viewModel.ProductPlans)
            {
                viewModel.Total += plan.PostCouponAmount;
                viewModel.Discount += (plan.Amount - plan.PostCouponAmount);
            }
        }

        #region Private Methods

        /// <summary>
        /// Returns distinct list of purchased service types.
        /// </summary>
        /// <param name="viewModel">CartDetailsModel</param>
        /// <returns>List of ServiceType</returns>
        private List<ServiceType> GetServiceTypes(CartDetailsModel cartDetails)
        {
            List<ServiceType> serviceTypes = new List<ServiceType>();

            foreach (var plan in cartDetails.ProductPlans)
            {
                //Add only the services for which the purchase was successful
                if (plan.Created == true)
                {
                    var product = ProductRepository.GetById((int)plan.ProductType);
                    serviceTypes.AddRange(product.Services.Select(s => s.ServiceType).ToList());
                    if(plan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded ||
                        plan.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded ||
                        plan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded)
                    {
                        serviceTypes.Add(ServiceType.InstagramPageEngage);
                    }
                    if(plan.ProductPlanTypeId == (byte)ProductPlanType.None)
                    {
                        serviceTypes.Remove(ServiceType.IDX);
                    }
                }
            }

            return serviceTypes.Distinct().ToList();
        }

        /// <summary>
        /// Returns registration event for service type.
        /// </summary>
        /// <param name="serviceType">ServiceType</param>
        /// <returns>AccountEnvent</returns>
        private AccountEvent GetAccountEventFor(ServiceType serviceType, CartModel cart, Agent agent)
        {
            EventType eventType = EventType.Undefined;
            ICollection<AccountEventDetail> eventDetails = new List<AccountEventDetail>();

            switch (serviceType)
            {
                case ServiceType.IDX:
                    eventType = EventType.AgentRegisteredIDXService;
                    eventDetails.Add(new AccountEventDetail
                    {
                        AttributeName = "MlsId",
                        AttributeValue = cart.MlsId,
                        Created = DateTime.Now,
                        Enabled = true
                    });
                    break;
                case ServiceType.PageManagement:
                    eventType = EventType.AgentRegisteredTKSService;
                    break;
                case ServiceType.PageEngage:
                    eventType = EventType.AgentRegisteredPageEngageService;
                    break;
                case ServiceType.DreamSweeps:
                    eventType = EventType.AgentRegisteredDreamSweepsService;
                    break;
                case ServiceType.DirectoryPro:
                    eventType = EventType.AgentRegisteredPageCreateService;
                    break;
                case ServiceType.HomeValue:
                    eventType = EventType.AgentRegisteredHomeValueService;
                    break;
                case ServiceType.Ads:
                    eventType = EventType.AgentRegisteredAdsService;
                    break;
                case ServiceType.FeaturedListings:
                    eventType = EventType.AgentRegisteredFeaturedListingsService;
                    break;
                case ServiceType.ListingLeadAds:
                    eventType = EventType.AgentRegisteredListingLeadAdsService;
                    break;
                case ServiceType.HomeValueAds:
                    eventType = EventType.AgentRegisteredHomeValueAdsService;
                    break;
                case ServiceType.IDXPlus:
                    eventType = EventType.AgentRegisteredIdxPlus;
                    break;
            }

            return eventType != EventType.Undefined ?
                new AccountEvent
                {
                    AgentId = agent.Id,
                    Created = eventDetails.Count > 0 ? eventDetails.First().Created : DateTime.Now,
                    Enabled = true,
                    EventId = (int)eventType,
                    AccountEventDetails = eventDetails
                } : null;
        }

        /// <summary>
        /// Returns list of registration and setup event types for service type.
        /// </summary>
        /// <param name="serviceType">ServiceType</param>
        /// <returns></returns>
        private List<int> GetAllAccountEventTypeIdsFor(ServiceType serviceType)
        {
            List<int> eventTypeIds = new List<int>();

            switch (serviceType)
            {
                case ServiceType.IDX:
                    eventTypeIds.Add((int)EventType.AgentRegisteredIDXService);
                    eventTypeIds.Add((int)EventType.AgentSetupMlsForIDXService);
                    eventTypeIds.Add((int)EventType.AgentSetupIDXService);
                    break;
                case ServiceType.PageManagement:
                    eventTypeIds.Add((int)EventType.AgentRegisteredTKSService);
                    eventTypeIds.Add((int)EventType.AgentSetupTKSService);
                    break;
                case ServiceType.PageEngage:
                    eventTypeIds.Add((int)EventType.AgentRegisteredPageEngageService);
                    eventTypeIds.Add((int)EventType.AgentSetupPageEngageService);
                    break;
                case ServiceType.DreamSweeps:
                    eventTypeIds.Add((int)EventType.AgentRegisteredDreamSweepsService);
                    eventTypeIds.Add((int)EventType.AgentSetupDreamSweepsService);
                    break;
                case ServiceType.DirectoryPro:
                    eventTypeIds.Add((int)EventType.AgentRegisteredPageCreateService);
                    eventTypeIds.Add((int)EventType.AgentSetupPageCreateService);

                    eventTypeIds.Add((int)EventType.AgentRegisteredCreateBusinessPageService);
                    eventTypeIds.Add((int)EventType.AgentSetupCreateBusinessPageService);
                    break;
                case ServiceType.HomeValue:
                    eventTypeIds.Add((int)EventType.AgentRegisteredHomeValueService);
                    eventTypeIds.Add((int)EventType.AgentSetupHomeValueService);
                    break;
                case ServiceType.Ads:
                    eventTypeIds.Add((int)EventType.AgentRegisteredAdsService);
                    eventTypeIds.Add((int)EventType.AgentSetupAdsService);
                    break;
                case ServiceType.FeaturedListings:
                    eventTypeIds.Add((int)EventType.AgentRegisteredFeaturedListingsService);
                    eventTypeIds.Add((int)EventType.AgentSetupFeaturedListingsService);
                    break;
                case ServiceType.ListingLeadAds:
                    eventTypeIds.Add((int)EventType.AgentRegisteredListingLeadAdsService);
                    eventTypeIds.Add((int)EventType.AgentSetupListingLeadAdsService);
                    break;
                case ServiceType.HomeValueAds:
                    eventTypeIds.Add((int)EventType.AgentRegisteredHomeValueAdsService);
                    eventTypeIds.Add((int)EventType.AgentSetupHomeValueAdsService);
                    break;
                case ServiceType.IDXPlus:
                    eventTypeIds.Add((int)EventType.AgentRegisteredIdxPlus);
                    eventTypeIds.Add((int)EventType.AgentSetupIdxPlus);
                    break;
            }

            return eventTypeIds;
        }

        /// <summary>
        /// Update agent's auth ticket to include newly purchased service.
        /// </summary>
        /// <param name="agent">Purchasing agent</param>
        private void UpdateAuthCookie(Agent agent)
        {
            var currentUser = System.Web.HttpContext.Current.User as NPlayPrincipal;
            var cookies = AuthCookieHelper.GetAuthCookies(
                agent.Id,
                agent.Membership.FirstName,
                agent.Membership.LastName,
                agent.Membership.Email,
                agent.GetProfileImage(),
                agent.GetRegisteredServices(),
                true,
                agent.DirectoryMemberType,
                CookieConstants.AGENT_COOKIE_NAME,
                currentUser.IsImpersonated,
                currentUser.DelegateUserName,
                Settings.DefaultCookieExpiration,
                (agent.IsSearchAllianceMember && agent.ListingsImportedDateTime != null),
                agent.ListingsImportedDateTime,
                agent.IsSearchAllianceMember,
                false
                );

            cookies.ForEach(c => System.Web.HttpContext.Current.Response.Cookies.Add(c));
        }

        #endregion
    }
}
