﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.Enums;
using NPlay.Common.Models;
using NPlay.Common.Models.PageEngage;
using NPlay.Common.Models.Services;
using NPlay.Services.NPlayApi.Builders.Abstract;

namespace NPlay.Services.NPlayApi.Builders
{
    /// <summary>
    /// Builds view model for Page Engage service.
    /// </summary>
    public class PageEngageServiceModelBuilder : ServiceModelBuilderBase, IServiceModelBuilder<PageEngageServiceModel>
    {
        private readonly IRepository<MultipleListingService> MlsRepository;
        private readonly IRepository<AgentPostTagFilter> AgentPostTagFilterRepository;

        #region Constructors

        /// <summary>
        /// Injects dependencies
        /// </summary>
        public PageEngageServiceModelBuilder(
            IRepository<Agent> agentRepository,
            IRepository<Service> serviceRepository,
            IRepository<ProductPlan> productPlanRepository,
            IRepository<FundingSource> fundingSourceRepository,
            IFacebookAgent facebookAgent,
            IMapper mapper,
            IRepository<MultipleListingService> mlsRepository,
            IRepository<AgentPostTagFilter> agentPostTagFilterRepository)
            : base(
                agentRepository,
                serviceRepository,
                productPlanRepository,
                fundingSourceRepository,
                facebookAgent,
                mapper)
        {
            MlsRepository = mlsRepository;
            AgentPostTagFilterRepository = agentPostTagFilterRepository;
        }

        #endregion

        #region Protected Properties

        protected override ServiceType ServiceType
        {
            get
            {
                return ServiceType.PageEngage;
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// Builds PageEngage service model.
        /// </summary>
        /// <param name="agentId">Subscribing agent</param>
        /// <returns>PageEngageServiceModel</returns>
        public PageEngageServiceModel BuildForAgent(int agentId, bool basicInfoOnly = false, bool skipFacebookPermissions = false)
        {
            Agent agent;
            ProductPlan plan;
            FundingSource funding;
            var viewModel = CreateModel(agentId);

            // Check if agent exists
            if (AgentExists(agentId, out agent))
            {
                viewModel.Agent = agent;
                viewModel.Status = ServiceStatus.Available;
                viewModel.ServiceType = ServiceType.PageEngage;

                // Check if agent is registered for service
                if (AgentIsRegistered(out plan, out funding))
                {
                    viewModel.FundingSource = _mapper.Map<FundingSourceModel>(funding);
                    viewModel.ProductPlanId = plan.Id;
                    viewModel.Status = ServiceStatus.Registered;

                    // Check if agent's funding source is valid
                    if (IsValidFundingSource(funding))
                    {
                        viewModel.Status = ServiceStatus.Funded;

                        // Check if service is setup
                        var lastSetupEvent = GetLastSetupEvent();
                        viewModel.Status = GetSetupStatus(lastSetupEvent);

                        // Check service requirements
                        if (!basicInfoOnly)
                        {
                            GetPermissions(viewModel);
                            GetPages(viewModel);
                            GetProvisioning(viewModel, agent);
                            GetOperatingStatus(viewModel);
                        }
                    }
                }
            }

            SyncRegisteredService(viewModel, agent, basicInfoOnly);

            return viewModel;
        }

        /// <summary>
        /// Returns list of service-specific registration events.
        /// </summary>
        /// <remarks>
        /// Called by base class to execute GetLastRegistrationEvent.
        /// </remarks>
        /// <returns>List of EventType</returns>
        protected override List<EventType> GetRegistrationEvents()
        {
            return new List<EventType> {
                EventType.AgentRegisteredPageEngageService
            };
        }

        /// <summary>
        /// Returns list of service-specific setup events.
        /// </summary>
        /// <remarks>
        /// Called by base class to execute GetLastSetupEvent.
        /// </remarks>
        /// <returns>List of EventType</returns>
        protected override List<EventType> GetSetupEvents()
        {
            return new List<EventType> {
                EventType.AgentSetupPageEngageService
            };
        }

        #endregion

        #region Helpers

        /// <summary>
        /// Create setup completion event.
        /// </summary>
        /// <param name="agent">Subscribing agent</param>
        /// <param name="userSchedule">Agent's user schedule</param>
        protected void AddAccountEvent(Agent agent, UserSchedule userSchedule)
        {
            var timestamp = DateTime.Now;
            agent.AccountEvents.Add(new AccountEvent
            {
                AgentId = agent.Id,
                Created = timestamp,
                Enabled = true,
                EventId = (int)EventType.AgentSetupPageEngageService,
                FacebookPageId = userSchedule.PostToBusinessPage.HasValue && userSchedule.PostToBusinessPage.Value ?
                    userSchedule.FacebookPageToPostTo : null,
                AccountEventDetails = new List<AccountEventDetail>
                {
                    new AccountEventDetail
                    {
                        AttributeName = "PostToTimeline",
                        AttributeValue = userSchedule.PostToProfile.HasValue && userSchedule.PostToProfile.Value ?
                            "true" : "false",
                        Created = timestamp,
                        Enabled = true
                    }
                }
            });
        }

        /// <summary>
        /// Creates empty instance of model.
        /// </summary>
        /// <param name="agentId">Subscribing agent Id</param>
        /// <returns>PageEngageServiceModel</returns>
        private PageEngageServiceModel CreateModel(int agentId)
        {
            PageEngageServiceModel model = new PageEngageServiceModel
            {
                AgentId = agentId,
                Status = ServiceStatus.NotAuthorized
            };

            List<FacebookPermissionModel> permissions = new List<FacebookPermissionModel>
            {
                new FacebookPermissionModel(FacebookPermissionType.PageManageAds.GetApiName()),
                new FacebookPermissionModel(FacebookPermissionType.PagesManageEngagement.GetApiName()),
                new FacebookPermissionModel(FacebookPermissionType.PagesManageMetadata.GetApiName()),
                new FacebookPermissionModel(FacebookPermissionType.PagesManagePosts.GetApiName()),
                new FacebookPermissionModel(FacebookPermissionType.PagesReadEngagement.GetApiName()),
                new FacebookPermissionModel(FacebookPermissionType.PagesReadUserContent.GetApiName()),
                new FacebookPermissionModel(FacebookPermissionType.BusinessManagement.GetApiName())
            };
            model.Permissions.FacebookPermissions.AddRange(permissions);

            return model;
        }

       /// <summary>
        /// Populates operating state information.
        /// </summary>
        /// <param name="viewModel">PageEngageServiceProvisioningModel</param>
        private void GetOperatingStatus(PageEngageServiceModel viewModel)
        {
            if (viewModel.Status.Equals(ServiceStatus.SetupComplete))
            {
                var operatingState = viewModel.OperatingState;

                if (ValidateFundingSource(viewModel.FundingSource, operatingState))
                {
                    // TODO check for other invalid states
                    operatingState.Description = ServiceState.Active.ToString();
                }
            }
        }

        /// <summary>
        /// Builds service-specific provisioning component.
        /// </summary>
        /// <param name="viewModel">ServiceModel</param>
        private void GetProvisioning(PageEngageServiceModel viewModel, Agent agent)
        {
            PageEngageServiceProvisioningModel provisioning = viewModel.Provisioning as PageEngageServiceProvisioningModel;
            provisioning.AgentId = viewModel.Agent.Id;
            provisioning.BusinessPages = viewModel.Pages.BusinessPages;

            if(provisioning.AgentPostTagFilters == null)
            {
                provisioning.AgentPostTagFilters = AgentPostTagFilterRepository.GetAll().Where(a => a.AgentId == viewModel.Agent.Id).Select(s => s.TagId).ToList();
            }

            var fundingSources = agent?.Membership?.FundingSources?.Where(fs => (fs.ProductTypeId == (byte)ProductType.InstagramPageEngage && fs.Active == true)
                                                                             || (fs.ProductTypeId == (byte)ProductType.Bundle && fs.Active == true
                                                                             && (fs.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.InstagramIncluded ||
                                                                                fs.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded ||
                                                                                fs.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded))
                                                                             || (fs.ProductTypeId == (byte)ProductType.PEInstagramBundle && fs.Active == true)).ToList();
            provisioning.HasInstagramPageEngage = fundingSources?.Count() > 0;

            var pageEngageUserSchedules = viewModel.Agent.UserSchedules?.Where(u => u.ProductId == (int)ProductType.PageEngage).ToList();
            if (pageEngageUserSchedules != null && pageEngageUserSchedules.Count > 0)
            {
                UserSchedule userSchedule = new List<UserSchedule>(pageEngageUserSchedules).First();
                provisioning.UserSchedule = _mapper.Map<UserScheduleModel>(userSchedule);

                // if all setup requirements met, then "auto-complete"
                if (viewModel.Status == ServiceStatus.Funded && provisioning.IsValid())
                {
                    AddAccountEvent(viewModel.Agent, userSchedule);
                    UpdateAuthCookie(viewModel.Agent);
                    viewModel.Status = ServiceStatus.SetupComplete;
                }
            }
            else
            {
                // set default schedule days
                provisioning.UserSchedule.PostingDays[0] = true;    // Mon
                provisioning.UserSchedule.PostingDays[1] = true;    // Tue
                provisioning.UserSchedule.PostingDays[2] = true;    // Wed
                provisioning.UserSchedule.PostingDays[3] = true;    // Thur
                provisioning.UserSchedule.PostingDays[4] = true;    // Fri

                if (!string.IsNullOrWhiteSpace(viewModel.Agent.MLSOrgId) && !string.IsNullOrWhiteSpace(viewModel.Agent.MLSAgentId))
                {
                    var mls = MlsRepository.GetById(viewModel.Agent.MLSOrgId);
                    if (mls?.IsActive == true)
                        provisioning.UserSchedule.FeaturedListings = true;
                }
            }
        }

        #endregion
    }
}