using AutoMapper;
using Castle.DynamicProxy.Generators.Emitters.SimpleAST;
using Nest;
using NPlay.BusinessLogic.Asap;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.Messaging;
using NPlay.Common.Models;
using NPlay.Common.Models.Services;
using NPlay.Common.Mortgages.Abstract;
using NPlay.Common.ServiceAgents;
using NPlay.Common.Services.Abstract;
using NPlay.Common.Web.Security.Filters;
using NPlay.Services.NPlayApi.RatePlug;
using System;
using System.Collections.Generic;
using System.Device.Location;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.UI;
using WebGrease.Css.Extensions;
using NPlay.Common.Mortgages.Extensions;
using System.Threading.Tasks;

namespace NPlay.Services.NPlayApi.Controllers
{
    public class RatePlugController : BaseApiController
    {
        private const int MONTHS_IN_YEAR = 12;
        private const double SEARCH_RADIUS_KM = 500;
        private const string MAPTILER_KEY = "DZO1fDyqfJxIdQYxvu1q";
        private const string MAPTILER_MAP_ID = "97d8a407-1cdc-4de8-b36b-cb69d5b37494";
        private const int THUMBNAIL_DEFAULT_WIDTH_PIXELS = 320;
        private const int THUMBNAIL_DEFAULT_HEIGHT_PIXELS = 250;
        private ISearchRepository SearchRepository;
        private IAgentPicker AgentPicker;
        private IRepository<AgentlessAffiliation> AffiliationRepository;
        private IMortgageCalculator MortgageCalculator;
        private ILogger Logger;
        private Dictionary<int, double> MetersPerPixelAtEquatorByZoomLevel = new Dictionary<int, double>();
        private IMapper Mapper;
        private IRepository<MultipleListingService> MultipleListingServiceRepository;
        private object[] SupportedPropertyTypes = { PropertyType.MultiFamily, PropertyType.SingleFamily, PropertyType.TownHomeCondo };

        public RatePlugController(ISearchRepository searchRepository,
                                  IAgentPicker agentPicker,
                                  IRepository<AgentlessAffiliation> affiliationRepository,
                                  IMortgageCalculator mortgageCalculator,
                                  ILogger logger,
                                  IMapper mapper,
                                  IRepository<MultipleListingService> multipleListingServiceRepository)
        {
            SearchRepository = searchRepository;
            AgentPicker = agentPicker;
            AffiliationRepository = affiliationRepository;
            MortgageCalculator = mortgageCalculator;
            Logger = logger;
            Mapper = mapper;
            MultipleListingServiceRepository = multipleListingServiceRepository;

            // These are the approximate number of meters per pixel for each mapbox zoom level
            //  based on 512x512 pixel per tile maps when calculated at 0 degrees latitude
            //  To calculate the meters per pixel at any given Latitude, use the following formula:
            //  MetersPerPixelAtEquatorByZoomLevel[zoom] * Cos(latitude)
            //  Where latitude is in degrees
            MetersPerPixelAtEquatorByZoomLevel[0] = 78271.484;
            MetersPerPixelAtEquatorByZoomLevel[1] = 39135.742;
            MetersPerPixelAtEquatorByZoomLevel[2] = 19567.871;
            MetersPerPixelAtEquatorByZoomLevel[3] = 9783.936;
            MetersPerPixelAtEquatorByZoomLevel[4] = 4891.968;
            MetersPerPixelAtEquatorByZoomLevel[5] = 2445.984;
            MetersPerPixelAtEquatorByZoomLevel[6] = 1222.992;
            MetersPerPixelAtEquatorByZoomLevel[7] = 611.496;
            MetersPerPixelAtEquatorByZoomLevel[8] = 305.748;
            MetersPerPixelAtEquatorByZoomLevel[9] = 152.874;
            MetersPerPixelAtEquatorByZoomLevel[10] = 76.437;
            MetersPerPixelAtEquatorByZoomLevel[11] = 38.218;
            MetersPerPixelAtEquatorByZoomLevel[12] = 19.109;
            MetersPerPixelAtEquatorByZoomLevel[13] = 9.555;
            MetersPerPixelAtEquatorByZoomLevel[14] = 4.777;
            MetersPerPixelAtEquatorByZoomLevel[15] = 2.389;
            MetersPerPixelAtEquatorByZoomLevel[16] = 1.194;
            //MetersPerPixelAtEquatorByZoomLevel[17] = 0.597;
            //MetersPerPixelAtEquatorByZoomLevel[18] = 0.299;
            //MetersPerPixelAtEquatorByZoomLevel[19] = 0.149;
            //MetersPerPixelAtEquatorByZoomLevel[20] = 0.075;
            //MetersPerPixelAtEquatorByZoomLevel[21] = 0.037;
            //MetersPerPixelAtEquatorByZoomLevel[22] = 0.019;

        }

        [HttpGet]
        [Route("api/rateplug/listings")]
        public IHttpActionResult GetListings([FromUri] double latitude,
                                                     [FromUri] double longitude,
                                                     [FromUri] int minListingCount,
                                                     [FromUri] string mortgageProgramType,
                                                     [FromUri] double interestRate,
                                                     [FromUri] double apr,
                                                     [FromUri] int paymentRangeLow,
                                                     [FromUri] int paymentRangeHigh,
                                                     [FromUri] string buyerId,
                                                     [FromUri] string loGuid,
                                                     [FromUri] int? isVeteran = null,
                                                     [FromUri] int? lenderVALoanLimit = null,
                                                     [FromUri] int? payment = null,
                                                     [FromUri] int? onboarding = 1,
                                                     [FromUri] int? downPayment = null,
                                                     [FromUri] double? downPaymentPercentage = null,
                                                     [FromUri] int w = THUMBNAIL_DEFAULT_WIDTH_PIXELS,
                                                     [FromUri] int h = THUMBNAIL_DEFAULT_HEIGHT_PIXELS,
                                                     [FromUri] string q = null,
                                                     [FromUri] string theme = "more",
                                                     [FromUri] string preferredAgentIds = null,
                                                     [FromUri] int onboardingAgentId = 0
                    )
        {
            var startTime = DateTime.UtcNow;
            Logger.Info($"GetListings started at {startTime}");

            if (!downPayment.HasValue && !downPaymentPercentage.HasValue)
            {
                return BadRequest();
            }

            if (downPayment.HasValue && downPaymentPercentage.HasValue)
            {
                return BadRequest();
            }

            var midTime1 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 1 at {midTime1}. Duration: {midTime1 - startTime}");

            RatePlugThumbnailResponse result = new RatePlugThumbnailResponse();

            double decimalInterestRate = interestRate / 100;
            int numberOfMonths = MONTHS_IN_YEAR * 30;
            int listPriceLow = 0;
            int listPriceHigh = 0;

            if (downPaymentPercentage >= 1)
            {
                downPaymentPercentage = downPaymentPercentage / 100;
            }

            var midTime2 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 2 at {midTime2}. Duration: {midTime2 - midTime1}");

            if (downPayment.HasValue)
            {
                listPriceLow = MortgageCalculator.GetListPrice(downPayment.Value, paymentRangeLow, decimalInterestRate, numberOfMonths);
                listPriceHigh = MortgageCalculator.GetListPrice(downPayment.Value, paymentRangeHigh, decimalInterestRate, numberOfMonths);
            }
            else if (downPaymentPercentage.HasValue)
            {
                listPriceLow = MortgageCalculator.GetListPrice(downPaymentPercentage.Value, paymentRangeLow, decimalInterestRate, numberOfMonths);
                listPriceHigh = MortgageCalculator.GetListPrice(downPaymentPercentage.Value, paymentRangeHigh, decimalInterestRate, numberOfMonths);
            }

            var midTime3 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 3 at {midTime3}. Duration: {midTime3 - midTime2}");

            int sampleSize = minListingCount * 10;

            int effectiveListPriceLow = (int)(listPriceLow * 0.6);
            var sampleListings = GetListings(latitude, longitude, sampleSize, effectiveListPriceLow, listPriceHigh);
            IEnumerable<MortgagePaymentInfo> mortgagePaymentInfos = null;

            if (downPayment.HasValue)
            {
                mortgagePaymentInfos = MortgageCalculator.GetMortgageInfos(sampleListings, downPayment.Value, decimalInterestRate, numberOfMonths);
            }
            else if (downPaymentPercentage.HasValue)
            {
                mortgagePaymentInfos = MortgageCalculator.GetMortgageInfos(sampleListings, downPaymentPercentage.Value, decimalInterestRate, numberOfMonths);
            }

            var midTime4 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 4 at {midTime4}. Duration: {midTime4 - midTime3}");

            if (sampleListings == null || sampleListings.Count() == 0)
            {
                return Ok(GetZeroListingResponse(latitude,
                                                 longitude,
                                                 mortgageProgramType,
                                                 interestRate,
                                                 apr,
                                                 paymentRangeLow,
                                                 paymentRangeHigh,
                                                 buyerId,
                                                 loGuid,
                                                 isVeteran,
                                                 lenderVALoanLimit,
                                                 payment,
                                                 onboarding,
                                                 w,
                                                 h,
                                                 q));
            }

            var paymentRange = MortgageCalculator.GetPaymentRange(mortgagePaymentInfos, paymentRangeLow, paymentRangeHigh);

            GeoCoordinate userLocation = new GeoCoordinate(latitude, longitude);
            sampleListings = mortgagePaymentInfos.Where(p => p.TotalPayment >= paymentRangeLow && p.TotalPayment <= paymentRangeHigh)
                                                 .Select(p => p.Listing)
                                                 .OrderBy(l =>
                                                 {
                                                     var location = new GeoCoordinate(l.Location.Lat, l.Location.Lon);
                                                     return location.GetDistanceTo(userLocation);
                                                 });

            var midTime5 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 5 at {midTime5}. Duration: {midTime5 - midTime4}");

            if (sampleListings == null || sampleListings.Count() == 0)
            {
                return Ok(GetZeroListingResponse(latitude,
                                                 longitude,
                                                 mortgageProgramType,
                                                 interestRate,
                                                 apr,
                                                 paymentRangeLow,
                                                 paymentRangeHigh,
                                                 buyerId,
                                                 loGuid,
                                                 isVeteran,
                                                 lenderVALoanLimit,
                                                 payment,
                                                 onboarding,
                                                 w,
                                                 h,
                                                 q));
            }

            result.MLSListings = sampleListings.Take(minListingCount);
            var listings = result.MLSListings.ToList();

            var downPaymentValue = downPayment.HasValue ? (int)downPayment : 0;
            MortgageCalculator.PopulateMortgagePaymentInfo(result.MLSListings, downPaymentValue, decimalInterestRate, numberOfMonths);

            var midTime6 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 6 at {midTime6}. Duration: {midTime6 - midTime5}");

            result.Listings = Mapper.Map<IEnumerable<BasicListingModel>>(listings);
            var seedAffiliationIds = new List<int>();
            if (theme == "more") {
                var moomAffiliation = AffiliationRepository.GetAll().Where(af => af.Name == "MoOm").FirstOrDefault();
                var kwAffiliation = AffiliationRepository.GetAll().Where(af => af.Name == "MORE Keller Williams").FirstOrDefault();
                if (moomAffiliation != null) seedAffiliationIds.Add(moomAffiliation.Id);
                if (kwAffiliation != null) seedAffiliationIds.Add(kwAffiliation.Id);
            }
            else if(theme == "afordal")
            {
                var afordalAffiliation = AffiliationRepository.GetAll().Where(af => af.Name == "Afordal").FirstOrDefault();
                if (afordalAffiliation != null) seedAffiliationIds.Add(afordalAffiliation.Id);
            }

            var preferredAgentIdList = string.IsNullOrWhiteSpace(preferredAgentIds)
                ? Array.Empty<int>()
                : preferredAgentIds.Split(',')
                                .Select(s => int.TryParse(s, out var id) ? id : (int?)null)
                                .Where(id => id.HasValue)
                                .Select(id => id.Value)
                                .ToArray();

            var midTime7 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 7 at {midTime7}. Duration: {midTime7 - midTime6}");

            var mlsIdsByCount = GetAgentPickerMlsIds(result.MLSListings);
            MatchedAgent agent = null;
            foreach (var mlsIdGroup in mlsIdsByCount)
            {
                agent = AgentPicker.Pick(new AgentPickerContext()
                {
                    Location = new GeoCoordinate(latitude, longitude),
                    IDXQualificationMode = IDXQualificationMode.Approved,
                    MlsIds = mlsIdGroup.ToArray(),
                    SeedAffiliationIds = seedAffiliationIds.ToArray(),
                    PreferredAgentIds = preferredAgentIdList,
                    OnboardingAgentId = onboardingAgentId > 0? onboardingAgentId : (int?)null,
                    Theme = theme
                });

                if (agent != null)
                    break;
            }

            var midTime8 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 8 at {midTime8}. Duration: {midTime8 - midTime7}");

            if (agent == null)
            {
                return NotFound();
            }
            var mlsId = agent.MlsId;
            var mlsDetails = MultipleListingServiceRepository.GetAll().Where(m => m.Id == mlsId).FirstOrDefault();
            if (mlsDetails != null)
            {
                result.MlsRules = new MlsRulesModel()
                {
                    Id = mlsDetails.Id,
                    LabelForIdxListingBroker = mlsDetails.LabelForIdxListingBroker,
                    ListingAgentOnOff = mlsDetails.ListingAgentOnOff,
                    ShowOnlyRangeLivingSquare = mlsDetails.ShowOnlyRangeLivingSquare,
                    DisplayLandTenure = mlsDetails.DisplayLandTenure,
                    ShowLogoOnPropertyCard = mlsDetails.ShowLogoOnPropertyCard,
                    LogoUrl = mlsDetails.LogoUrl,
                    LogoUrlForCards = mlsDetails.LogoUrlForCards,
                    ShowLargeBrokerOnPropertyCard = mlsDetails.ShowLargeBrokerOnPropertyCard
                };
            }

            var midTime9 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 9 at {midTime9}. Duration: {midTime9 - midTime8}");

            result.HomeASAPAgentId = agent.Id;
            result.AgentBrokerageName = agent.BrokerName;
            result.AgentFirstName = agent.FirstName;
            result.AgentLastName = agent.LastName;
            result.AgentPhotoUrl = agent.ProfileImage;
            result.ListingCount = GetListingsWithinTargetRadius(result.MLSListings, sampleListings).Count();
            result.PaymentRangeLow = (int)paymentRange.LowPayment;
            result.PaymentRangeHigh = (int)paymentRange.HighPayment;
            result.ListingThumbnailUrl = GetThumbnail(result.MLSListings, latitude, longitude, w, h);
            result.ThumbnailAttribution = $"Presented by {result.AgentName.Trim()}, {result.AgentBrokerageName.Trim()}";

            var midTime10 = DateTime.UtcNow;
            Logger.Info($"Checkpoint 10 at {midTime10}. Duration: {midTime10 - midTime9}");

            result.HomeASAPLandingPageUrl = GetHomeASAPUrl(agent,
               result.MLSListings,
               sampleListings,
               interestRate,
               apr,
               downPayment,
               downPaymentPercentage,
               paymentRangeLow,
               paymentRangeHigh,
               mortgageProgramType,
               buyerId,
               loGuid,
               isVeteran,
               lenderVALoanLimit,
               payment,
               latitude,
               longitude,
               onboarding,
               q,
               theme);

            var endTime = DateTime.UtcNow;
            Logger.Info($"GetListings ended at {endTime}. Duration: {endTime - startTime}");

            Logger.Info($"Total Time Intervals: " +
                       $"Start to Checkpoint 1: {midTime1 - startTime}, " +
                       $"Checkpoint 1 to Checkpoint 2: {midTime2 - midTime1}, " +
                       $"Checkpoint 2 to Checkpoint 3: {midTime3 - midTime2}, " +
                       $"Checkpoint 3 to Checkpoint 4: {midTime4 - midTime3}, " +
                       $"Checkpoint 4 to Checkpoint 5: {midTime5 - midTime4}, " +
                       $"Checkpoint 5 to Checkpoint 6: {midTime6 - midTime5}, " +
                       $"Checkpoint 6 to Checkpoint 7: {midTime7 - midTime6}, " +
                       $"Checkpoint 7 to Checkpoint 8: {midTime8 - midTime7}, " +
                       $"Checkpoint 8 to Checkpoint 9: {midTime9 - midTime8}, " +
                       $"Checkpoint 9 to Checkpoint 10: {midTime10 - midTime9}, " +
                       $"Checkpoint 10 to End: {endTime - midTime10}");

            return Ok(result);
        }

        private RatePlugThumbnailResponse GetZeroListingResponse(double latitude,
                                             double longitude,
                                             string mortgageProgramType,
                                             double interestRate,
                                             double apr,
                                             int paymentRangeLow,
                                             int paymentRangeHigh,
                                             string buyerId,
                                             string loGuid,
                                             int? isVeteran,
                                             int? lenderVALoanLimit,
                                             int? payment,
                                             int? onboarding,
                                             int w = THUMBNAIL_DEFAULT_WIDTH_PIXELS,
                                             int h = THUMBNAIL_DEFAULT_HEIGHT_PIXELS,
                                             string q = null,
                                             string theme = "more")
        {
            // Special case where the mortgage parameters do not result in any listings.
            // In this case, we're just showing a basic home search site without picking an agent
            int downPayment = 0;
            double downPaymentPercentage = 0;

            return new RatePlugThumbnailResponse()
            {
                ListingCount = 0,
                HomeASAPLandingPageUrl = GetHomeASAPUrl(interestRate,
                                                        apr,
                                                        downPayment,
                                                        downPaymentPercentage,
                                                        paymentRangeLow,
                                                        paymentRangeHigh,
                                                        mortgageProgramType,
                                                        buyerId,
                                                        loGuid,
                                                        isVeteran,
                                                        lenderVALoanLimit,
                                                        payment,
                                                        latitude,
                                                        longitude,
                                                        onboarding,
                                                        q,
                                                        theme),
                ListingThumbnailUrl = GetThumbnail(latitude, longitude, w, h)
            };
        }

        private IEnumerable<MLSPropertyListing> GetListingsWithinTargetRadius(IEnumerable<MLSPropertyListing> resultListings,
                                                                              IEnumerable<MLSPropertyListing> allListings)
        {
            double boundingBoxCenterLatitude;
            double boundingBoxCenterLongitude;
            double minLatitude, maxLatitude, minLongitude, maxLongitude;
            minLatitude = resultListings.Min(l => l.Location.Lat);
            maxLatitude = resultListings.Max(l => l.Location.Lat);
            minLongitude = resultListings.Min(l => l.Location.Lon);
            maxLongitude = resultListings.Max(l => l.Location.Lon);
            boundingBoxCenterLatitude = minLatitude + (maxLatitude - minLatitude) / 2;
            boundingBoxCenterLongitude = minLongitude + (maxLongitude - minLongitude) / 2;
            GeoCoordinate lowerLeft = new GeoCoordinate(minLatitude, maxLongitude);
            GeoCoordinate upperRight = new GeoCoordinate(maxLatitude, minLongitude);
            GeoCoordinate center = new GeoCoordinate(boundingBoxCenterLatitude, boundingBoxCenterLongitude);
            var distance = lowerLeft.GetDistanceTo(upperRight);
            int radiusMeters = (int)Math.Ceiling(distance / 2);

            return allListings.Where(l => new GeoCoordinate(l.Location.Lat, l.Location.Lon).GetDistanceTo(center) <= radiusMeters);
        }

        private string GetHomeASAPUrl(MatchedAgent agent,
                                      IEnumerable<MLSPropertyListing> resultListings,
                                      IEnumerable<MLSPropertyListing> allListings,
                                      double interestRate,
                                      double apr,
                                      int? downPayment,
                                      double? downPaymentPercentage,
                                      int minMortgagePayment,
                                      int maxMortgagePayment,
                                      string mortgageProgramType,
                                      string buyerId,
                                      string loanOfficerId,
                                      int? isVeteran,
                                      int? lenderVALoanLimit,
                                      int? payment,
                                      double buyerLatitude,
                                      double buyerLongitude,
                                      int? onboarding,
                                      string q,
                                      string theme)
        {
            int radiusMeters;
            double boundingBoxCenterLatitude;
            double boundingBoxCenterLongitude;
            double minLatitude, maxLatitude, minLongitude, maxLongitude;
            string homePinLocationString = HttpUtility.UrlEncode(buyerLatitude + "," + buyerLongitude);
            minLatitude = resultListings.Min(l => l.Location.Lat);
            maxLatitude = resultListings.Max(l => l.Location.Lat);
            minLongitude = resultListings.Min(l => l.Location.Lon);
            maxLongitude = resultListings.Max(l => l.Location.Lon);
            boundingBoxCenterLatitude = minLatitude + (maxLatitude - minLatitude) / 2;
            boundingBoxCenterLongitude = minLongitude + (maxLongitude - minLongitude) / 2;
            string agentRoute = !string.IsNullOrWhiteSpace(agent.CustomURL) ? agent.CustomURL : agent.Id.ToString();
            GeoCoordinate lowerLeft = new GeoCoordinate(minLatitude, maxLongitude);
            GeoCoordinate upperRight = new GeoCoordinate(maxLatitude, minLongitude);
            GeoCoordinate lowerRight = new GeoCoordinate(minLatitude, minLongitude);
            int horizontalDistance = (int)lowerLeft.GetDistanceTo(lowerRight);
            int zoomLevel = GetZoomLevel(boundingBoxCenterLatitude, horizontalDistance);
            var distance = lowerLeft.GetDistanceTo(upperRight);
            radiusMeters = (int)Math.Floor(distance / 2);
            string isVeteranQsParam = null;
            string sfQueryString = String.Empty;
            if (isVeteran == 1)
            {
                isVeteranQsParam = "1";
                // sfQueryString = "VA";  // We do not want to auto select VA sf option.
            }

            if (resultListings.Count() < 2)
            {
                zoomLevel = 12;
                radiusMeters = 6200;
            }

            if (downPaymentPercentage < 1)
            {
                downPaymentPercentage = downPaymentPercentage * 100;
            }

            return $"{Settings.HomeSearchRootUrl}" +
                        $"{agentRoute}" +
                        $"/search/map/" +
                        $"{boundingBoxCenterLatitude}," +
                        $"{boundingBoxCenterLongitude}," +
                        $"{radiusMeters}," +
                        $"{zoomLevel}" +
                        $"?saleType=1" +
                        $"{GetSupportedPropertyTypesQs()}" +
                        $"&theme={theme}" +
                        $"&ob=1" +
                        $"&downPayment={downPayment ?? downPaymentPercentage}" +
                        $"&specialFinancing={sfQueryString}" +
                        $"&home_pin={homePinLocationString}" +
                        $"&minMortgagePayment={minMortgagePayment}" +
                        $"&maxMortgagePayment={maxMortgagePayment}" +
                        $"&rp_onboarding={onboarding}" +
                        $"&rp_rate={interestRate}" +
                        $"&rp_apr={apr}" +
                        $"&rp_program={HttpUtility.UrlEncode(mortgageProgramType)}" +
                        $"&rp_downpayment={downPayment ?? downPaymentPercentage}" +
                        $"&rp_buyer={buyerId}" +
                        $"&rp_lo={loanOfficerId}" +
                        $"&rp_veteran={isVeteranQsParam}" +
                        $"&rp_va_loan_limit={lenderVALoanLimit}" +
                        $"&rp_payment={payment}" +
                        $"&q={Uri.EscapeDataString(q)}";
        }

        private string GetHomeASAPUrl(double interestRate,
                                      double apr,
                                      int? downPayment,
                                      double? downPaymentPercentage,
                                      int minMortgagePayment,
                                      int maxMortgagePayment,
                                      string mortgageProgramType,
                                      string buyerId,
                                      string loanOfficerId,
                                      int? isVeteran,
                                      int? lenderVALoanLimit,
                                      int? payment,
                                      double buyerLatitude,
                                      double buyerLongitude,
                                      int? onboarding,
                                      string q,
                                      string theme)
        {
            int radiusMeters = 6200;
            double boundingBoxCenterLatitude;
            double boundingBoxCenterLongitude;
            string homePinLocationString = HttpUtility.UrlEncode(buyerLatitude + "," + buyerLongitude);
            boundingBoxCenterLatitude = buyerLatitude;
            boundingBoxCenterLongitude = buyerLongitude;
            int zoomLevel = GetZoomLevel(boundingBoxCenterLatitude, (int)(radiusMeters * 2.8));
            string isVeteranQsParam = null;
            string sfQueryString = String.Empty;
            if (isVeteran == 1)
            {
                isVeteranQsParam = "1";
                // sfQueryString = "VA";  // We do not want to auto select VA sf option.
            }

            if (downPaymentPercentage < 1)
            {
                downPaymentPercentage = downPaymentPercentage * 100;
            }

            return $"{Settings.HomeSearchRootUrl}" +
                        $"search/map/" +
                        $"{boundingBoxCenterLatitude}," +
                        $"{boundingBoxCenterLongitude}," +
                        $"{radiusMeters}," +
                        $"{zoomLevel}" +
                        $"?saleType=1" +
                        $"{GetSupportedPropertyTypesQs()}" +
                        $"&sortType=price_desc" +
                        $"&theme={theme}" +
                        $"&ob=1" +
                        $"&rp_onboarding={onboarding}" +
                        $"&rp_rate={interestRate}" +
                        $"&rp_apr={apr}" +
                        $"&rp_program={HttpUtility.UrlEncode(mortgageProgramType)}" +
                        $"&rp_downpayment={downPayment ?? downPaymentPercentage}" +
                        $"&downPayment={downPayment ?? downPaymentPercentage}" +
                        $"&specialFinancing={sfQueryString}" +
                        $"&minMortgagePayment={minMortgagePayment}" +
                        $"&maxMortgagePayment={maxMortgagePayment}" +
                        $"&rp_buyer={buyerId}" +
                        $"&rp_lo={loanOfficerId}" +
                        $"&rp_veteran={isVeteranQsParam}" +
                        $"&rp_va_loan_limit={lenderVALoanLimit}" +
                        $"&rp_payment={payment}" +
                        $"&home_pin={homePinLocationString}" +
                        $"&q={Uri.EscapeDataString(q)}";
        }

        private string GetSupportedPropertyTypesQs()
        {
            string supportedPropertyTypeQs = "";
            foreach (var supportedPropertyType in SupportedPropertyTypes)
            {
                supportedPropertyTypeQs += $"&homeTypesArray={(int)supportedPropertyType}";
            }

            return supportedPropertyTypeQs;
        }

        private int GetZoomLevel(double latitude, int horizontalDistanceMeters)
        {
            // We want to find a zoom level that covers the horizontal distance
            double desiredDistancePerPixel = horizontalDistanceMeters / THUMBNAIL_DEFAULT_WIDTH_PIXELS;
            int zoom = MetersPerPixelAtEquatorByZoomLevel.Count - 1;
            while (zoom >= 0)
            {
                double distancePerPixel = MetersPerPixelAtEquatorByZoomLevel[zoom] * Math.Cos(latitude * Math.PI / 180);
                if (distancePerPixel > desiredDistancePerPixel)
                {
                    break;
                }

                zoom--;
            }

            // The desired zoom level seems to be a little further in than the above calculation
            zoom = zoom + 2;

            // Don't return any zoom levels higher than the max possible value
            if (zoom > MetersPerPixelAtEquatorByZoomLevel.Keys.Max())
            {
                zoom = MetersPerPixelAtEquatorByZoomLevel.Keys.Max();
            }

            return zoom;
        }

        private string GetThumbnail(IEnumerable<MLSPropertyListing> listings, double buyerLatitude, double buyerLongitude, int widthPixels, int heightPixels)
        {
            string pinAnchor = "anchor:bottom";
            string pinScale = "scale:4";
            string userLocationPinAnchor = "anchor:center";
            string userLocationPinScale = "scale:4";
            string iconUrl = HttpUtility.UrlEncode("https://nplayassets.blob.core.windows.net/images/icons/pin-MORE-49x57.png");
            string userLocationIconUrl = HttpUtility.UrlEncode("https://nplayassets.blob.core.windows.net/images/icons/pin-home-64x64.png");
            string userLocationMarkersQs = $"icon:{userLocationIconUrl}|{userLocationPinAnchor}|{userLocationPinScale}|{buyerLongitude},{buyerLatitude}";
            string markersQs = $"icon:{iconUrl}|{pinAnchor}|{pinScale}|" + String.Join("|", listings.Select(l => $"{l.Location.Lon},{l.Location.Lat}").ToArray());
            return $"https://api.maptiler.com/maps/{MAPTILER_MAP_ID}/static/auto/{widthPixels}x{heightPixels}@2x.png?markers={userLocationMarkersQs}&markers={markersQs}&key={MAPTILER_KEY}";
        }

        private string GetThumbnail(double buyerLatitude, double buyerLongitude, int widthPixels, int heightPixels)
        {
            string userLocationPinAnchor = "anchor:center";
            string userLocationPinScale = "scale:4";
            string userLocationIconUrl = HttpUtility.UrlEncode("https://nplayassets.blob.core.windows.net/images/icons/pin-home-64x64.png");
            string userLocationMarkersQs = $"icon:{userLocationIconUrl}|{userLocationPinAnchor}|{userLocationPinScale}|{buyerLongitude},{buyerLatitude}";
            return $"https://api.maptiler.com/maps/{MAPTILER_MAP_ID}/static/{buyerLongitude},{buyerLatitude},12/{widthPixels}x{heightPixels}@2x.png?markers={userLocationMarkersQs}&key={MAPTILER_KEY}";
        }

        private IOrderedEnumerable<IGrouping<string, string>> GetAgentPickerMlsIds(IEnumerable<MLSPropertyListing> listings)
        {
            List<string> allMlsIds = new List<string>();
            foreach (string[] mlsSet in listings.Select(l => l.MlsIds))
            {
                allMlsIds.AddRange(mlsSet);
            }

            var mlsIdGroups = from id in allMlsIds
                              group id by id into g
                              orderby g.Count() descending
                              select g;

            return mlsIdGroups;
        }

        private IEnumerable<MLSPropertyListing> GetListings(double lat, double lon, int count, int listPriceLow, int listPriceHigh)
        {
            SearchCriteria<MLSPropertyListing> criteria = new SearchCriteria<MLSPropertyListing>(count);
            var allFilters = new List<FilterContainer>();
            var allQueries = new List<QueryContainer>();
            var containsCriteria = new List<ContainsCriteria<MLSPropertyListing>>();

            var mustNotFilters = new List<FilterContainer>();
            FilterContainer statusFilter = Filter<MLSPropertyListing>.Term(p => p.Status, "S");
            mustNotFilters.Add(statusFilter);

            FilterContainer internetFilter = Filter<MLSPropertyListing>.Term(p => p.ListOnInternet, true);
            allFilters.Add(internetFilter);

            // Property Types
            criteria.ContainsExpressions.Add(new ContainsCriteria<MLSPropertyListing>()
            {
                ContainsExpression = p => p.PropertyType,
                PossibleValues = SupportedPropertyTypes
            });

            QueryContainer minPriceQuery = Query<MLSPropertyListing>.Range(r => r.GreaterOrEquals(listPriceLow)
                                                                                .OnField(x => x.ListPrice));
            allQueries.Add(minPriceQuery);
            QueryContainer maxPriceQuery = Query<MLSPropertyListing>.Range(r => r.LowerOrEquals(listPriceHigh)
                                                                                .OnField(x => x.ListPrice));
            allQueries.Add(maxPriceQuery);

            bool sortByClosestDistance = true;
            var listings = SearchRepository.Distance<MLSPropertyListing>(SearchIndex.Listings, l => l.Location, new GeoCoordinate(lat, lon), SEARCH_RADIUS_KM, GeoUnit.Kilometers,
                sortByClosestDistance, null, count, allFilters, allQueries, null, null).Documents;

            return listings;
        }
    }
}
