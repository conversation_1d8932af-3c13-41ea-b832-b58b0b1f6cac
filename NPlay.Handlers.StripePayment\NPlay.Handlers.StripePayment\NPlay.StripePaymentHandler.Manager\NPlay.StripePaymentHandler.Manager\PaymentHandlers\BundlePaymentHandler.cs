﻿using System;
using System.Linq;
using System.Net.Mail;
using System.Web;
using NPlay.Common.Abstract;
using NPlay.Common.Abstract.Emailer;
using NPlay.Common.Models;
using NPlay.Common.Models.Emails;
using NPlay.Common.Models.PageEngage;
using NPlay.StripePaymentHandler.Core.Models;
using System.Collections.Generic;
using NPlay.Common;
using NPlay.Common.BaseRepository;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Models.Emails.Engine;
using NPlay.BusinessLogic.Payment.Abstract;
using NPlay.Common.Models.Payments;
using NPlay.Common.Enums;
using NPlay.Common.Models.ExtendedServices;
using NPlay.Common.Models.ExtendedServices.Abstract;

namespace NPlay.StripePaymentHandler.Core.PaymentHandlers
{
    public class BundlePaymentHandler : InvoicePaymentHandlerBase
    {
        private readonly IAgentRepository AgentRepository;
        private readonly ISplitPaymentHelper SplitPaymentHelper;
        private readonly IAfordalHelper AfordalHelper;
        public BundlePaymentHandler(IPaymentProcessor paymentProcessor,
                                    IRepositoryFactory repositories,
                                    ISplitPaymentHelper splitPaymentHelper,
                                    IEmailer emailer,
                                    IPaymentNotificationManagerOld notificationManager,
                                    IRepository<TransactionLog> transactionLogRepository,
                                    IUnitOfWorkFactory uowFactory,
                                    ITransactionLogManager transactionLogManager,
                                    IEmailGenerator emailGenerator,
                                    ICampaignMonitorSubscriberHandler campaignMonitorSubscriberHandler,
                                    IRepository<SubscriptionsMissingFundingSource> subscriptionsMissingFundingSourceRepository,
                                    IRepository<Membership> membershipRepository,
                                    IEventBroker eventBroker,
                                    ILogger logger,
                                    IPaymentSystem paymentSystem,
                                    ISubscriptionHelper subscriptionHelper,
                                    IAfordalHelper afordalHelper)
            : base(transactionLogRepository,
                   uowFactory,
                   transactionLogManager,
                   paymentProcessor,
                   campaignMonitorSubscriberHandler,
                   subscriptionsMissingFundingSourceRepository,
                   membershipRepository,
                   repositories,
                   emailer,
                   eventBroker,
                   logger,
                   notificationManager,
                   emailGenerator,
                   paymentSystem,
                   subscriptionHelper)
        {
            AgentRepository = Repositories.AgentRepository;
            SplitPaymentHelper = splitPaymentHelper;
            AfordalHelper = afordalHelper;
        }

        protected override ProductType ProductType
        {
            get
            {
                return ProductType.Bundle;
            }
        }

        protected override TransactionTypes TransactionType
        {
            get
            {
                return TransactionTypes.TurnKeySuiteSubscription;
            }
        }

        protected override bool DoNotProcessIfAmountIs0OrLessForPaymentSuccess
        {
            get
            {
                return false;
            }
        }

        protected override void PaymentSuccessful(Agent agent, FundingSource fundingSource, InvoicePaymentResponse paymentResult, WebHookHandlerResult result)
        {
            CheckAccountManager(agent);

            fundingSource.SuccessfulPayment();
            FundingSourceRepository.Save(fundingSource);

            Subscription subscription = PaymentSystem.Subscription.GetSubscription(fundingSource.SubscriptionId);
            if (subscription?.Metadata?.ContainsKey(StripeMetaDataNames.PendingPayments) == true)
            {
                var remainingDraftInvoices = PaymentSystem.Invoice.GetInvoices(fundingSource.ProviderId, 10, null, fundingSource.SubscriptionId, "draft");
                var remainingOpenInvoices = PaymentSystem.Invoice.GetInvoices(fundingSource.ProviderId, 10, null, fundingSource.SubscriptionId, "open");
                if (remainingOpenInvoices?.Count() == 0 && remainingDraftInvoices?.Count() == 0)
                {
                    SplitPaymentHelper.Finalize(subscription, fundingSource);
                }
            }
            if (fundingSource?.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.InstagramIDXIncluded ||
                fundingSource?.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.IDXincluded ||
                fundingSource?.ProductPlan?.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded)
            {
                agent.ListingsImportedDateTime = agent.ListingsImportedDateTime ?? DateTime.UtcNow;
            }
            AgentRepository.Save(agent);

            if (result.AttemptCount > 1)
                SendServiceResumedEmail(fundingSource, agent);

            Logger.Info($"Checking for Afordal subscription for agent {agent.Id}");
            if (AfordalHelper.CheckForAfordalSubscription(agent.Id))
            {
                Logger.Info($"Afordal subscription found for agent {agent.Id}. Calling AfordalActivated to add to Rateplug");
                AfordalHelper.AfordalActivated(agent.Id);
            }
            if (paymentResult?.ChargeAmount > 0)
            {
                EmailGenerator.SendProductThankYouEmail(fundingSource.MembershipId, fundingSource.Id, paymentResult.ChargeAmount);
            }
        }

        protected override void PaymentFailed(Agent agent, FundingSource fundingSource, InvoicePaymentResponse paymentResult, WebHookHandlerResult result)
        {
            CheckAccountManager(agent);

            fundingSource.FailedPayment();

            if (paymentResult.Data.InvoiceDetails.Closed)
                fundingSource.SubscriptionEndDate = DateTime.Now;

            Subscription subscription = PaymentSystem.Subscription.GetSubscription(fundingSource.SubscriptionId);
            if (subscription?.Metadata?.ContainsKey(StripeMetaDataNames.PendingPayments) == true)
                SplitPaymentHelper.HandleFailedPayment(subscription, fundingSource);

            FundingSourceRepository.Save(fundingSource);

            SendFailedPaymentEmail(fundingSource, agent, result, paymentResult);

            NotificationManager.Send(NotificationType.BundlePaymentFailed, agent, fundingSource.Id);
        }

        #region Private Methods

        private void CheckAccountManager(Agent agent)
        {
            //Assign acct manager if one is not already assigned. Please do not loose this a second time in a merge.
            var acctMgr = agent.AccountSpecialists.FirstOrDefault(s => s.Enabled);

            //not supported any longer, maybe?
            //if (acctMgr == null)
            //    acctMgr = Repositories.AgentSupportRepository.AssignAccountSpecialistToAgent(agent);
        }

        private static UserSchedule SetDefaultScheduleValues(UserSchedule userSchedule, bool userHasBusinessPage)
        {
            if (!userSchedule.Monday.HasValue && !userSchedule.Tuesday.HasValue && !userSchedule.Wednesday.HasValue &&
                    !userSchedule.Thursday.HasValue && !userSchedule.Friday.HasValue && !userSchedule.Saturday.HasValue &&
                    !userSchedule.Sunday.HasValue)
            {
                userSchedule.Monday = true;
                userSchedule.Tuesday = false;
                userSchedule.Wednesday = true;
                userSchedule.Thursday = false;
                userSchedule.Friday = false;
                userSchedule.Saturday = true;
                userSchedule.Sunday = false;
                userSchedule.PostToProfile = !userHasBusinessPage;
                userSchedule.PostToBusinessPage = userHasBusinessPage;
            }

            return userSchedule;
        }

        private void SendFailedPaymentEmail(FundingSource fundingSource, Agent agent, WebHookHandlerResult result, InvoicePaymentResponse paymentResult)
        {
            const string methodName = "SendFailedPaymentEmail";

            if (Emailer.IsValidEmailAddress(agent.Membership.Email))
            {
                var acctMgr = agent.AccountSpecialists.FirstOrDefault(s => s.Enabled);
                var subscriptionId = paymentResult.Data.InvoiceDetails.SubscriptionId;
                var cardType = fundingSource.CCType; ;
                var last4 = fundingSource.CreditCard.Last4;
                if (string.IsNullOrWhiteSpace(last4))
                {
                    FundingSource fundingSourceWithCardInfo = null;
                    try
                    {
                        fundingSourceWithCardInfo = PaymentProcessor.GetFundingSource(subscriptionId);
                        if (fundingSourceWithCardInfo != null)
                        {
                            cardType = fundingSourceWithCardInfo.CCType; ;
                            last4 = fundingSourceWithCardInfo.CreditCard.Last4;
                        }
                    }
                    catch (Exception)
                    {
                        //failed to retrieve but it is ok since we have fundingSource variable that is not null.
                    }
                }

                var chargeFailedEmail = new TurnKeySuiteChargeFailed();

                chargeFailedEmail.AgentName = agent.Membership.Name();
                chargeFailedEmail.AgentImageUrl = agent.GetProfileImage();
                chargeFailedEmail.BrokerName = agent.Brokerage.BrokerageName;
                chargeFailedEmail.DateRegistered = agent.Membership.DateRegistered.Value.ToShortDateString();
                chargeFailedEmail.Email = agent.Membership.Email;
                chargeFailedEmail.EmailLink = string.Format("mailto:{0}", agent.Membership.Email);
                chargeFailedEmail.FirstName = agent.Membership.FirstName;
                chargeFailedEmail.ReadScore = agent.Score.ToString();
                chargeFailedEmail.UpdateCardUrl = string.Format("{0}tksfailpayment", Settings.GatewayUrl);
                chargeFailedEmail.Template = "TurnKeySuiteChargeFailed.xslt";
                chargeFailedEmail.UnsubscribeUrl = string.Format("http://apps.facebook.com/{0}/UnsubscribeEmail.aspx?npuid={1}", Settings.AgentAppId, agent.Id);
                //was PaymentAmount
                chargeFailedEmail.PlanCost = fundingSource.ProductPlan.Amount.ToString("C2");
                chargeFailedEmail.Last4 = last4;

                chargeFailedEmail.AgentFacebookUrl = agent.ProfileUrl.AbsoluteUri;
                chargeFailedEmail.CardType = cardType;
                chargeFailedEmail.AccountManagerName = acctMgr != null && acctMgr.Membership != null ? acctMgr.Membership.FirstName : string.Empty;

                //************************
                //These properties are no longer on the object
                //************************
                //chargeFailedEmail.AccountManagerPhone = acctMgr.PhoneNumber;
                //chargeFailedEmail.AccountManagerEmail = acctMgr.Membership.Email;
                //chargeFailedEmail.UpdateFundingSourceUrl = string.Format("{0}tksfailpayment", Settings.GatewayUrl);
                //chargeFailedEmail.RenewalDate = paymentResult.SubscriptionPeriodForInvoice != null && paymentResult.SubscriptionPeriodForInvoice.Start != null ?
                //    paymentResult.SubscriptionPeriodForInvoice.Start.Value.ToShortDateString() : DateTime.Now.ToShortDateString();
                //chargeFailedEmail.FailureCountOrdinal = result.AttemptCount <= 1 ? string.Empty : result.AttemptCount.ToOrdinal();
                //chargeFailedEmail.PaymentAmount = fundingSource.ProductPlan.Amount.ToString("C2");

                if (agent.FacebookPage != null)
                    chargeFailedEmail.AgentFacebookName = agent.FacebookPage.Name;

                chargeFailedEmail.Subject = "TurnKey Suite Service - renewal failed. We need your help";
                chargeFailedEmail.To.Add(agent.Membership.Email);
                if (acctMgr != null && acctMgr.Membership != null && !string.IsNullOrWhiteSpace(acctMgr.Membership.Email))
                    chargeFailedEmail.Bcc.Add(acctMgr.Membership.Email);
                chargeFailedEmail.CC.Add(Settings.TurnKeySupportEmail);
                chargeFailedEmail.From = new MailAddress(Settings.MailgunFromEmailAddress, EmailFromDisplayName);
                chargeFailedEmail.ReplyTo = new MailAddressCollection { Settings.TurnKeySupportEmail };

                var transform = string.Empty;

                if (HttpContext.Current != null)
                    transform = HttpContext.Current.Server.MapPath(string.Format("~/bin/Transforms/{0}", chargeFailedEmail.Template));

                try
                {
                    Emailer.Send(transform, chargeFailedEmail, SmtpAccountType.Transactional);
                }
                catch (Exception exc)
                {
                    Logger.Warning("Failed to send failed payment email.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.FundingSourceId, fundingSource.Id)}, exc, methodName);
                }
            }
            else
                SendInvalidEmailFailedPaymentEmail(agent);
        }

        private void SendServiceResumedEmail(FundingSource fundingSource, Agent agent)
        {
            const string methodName = "SendServiceResumedEmail";

            if (Emailer.IsValidEmailAddress(agent.Membership.Email))
            {
                var acctMgr = agent.AccountSpecialists.FirstOrDefault(s => s.Enabled);
                var gatewayUrl = Settings.GatewayUrl;
                var reactivationEmail = new TurnKeySuiteReactivation();

                reactivationEmail.AccountManagerName = acctMgr != null && acctMgr.Membership != null ? acctMgr.Membership.FirstName : string.Empty;
                reactivationEmail.AgentImageUrl = agent.GetProfileImage();
                reactivationEmail.AgentName = agent.Membership.Name();
                reactivationEmail.BrokerName = agent.Brokerage.BrokerageName;
                reactivationEmail.DateRegistered = agent.Membership.DateRegistered.Value.ToShortDateString();
                reactivationEmail.Email = agent.Membership.Email;
                reactivationEmail.EmailLink = string.Format("mailto:{0}", agent.Membership.Email);
                reactivationEmail.FirstName = agent.Membership.FirstName;
                reactivationEmail.PaymentAmount = fundingSource.ProductPlan.Amount.ToString("C2");
                reactivationEmail.ReadScore = agent.Score.ToString();
                reactivationEmail.TurnKeySuiteUrl = string.Format("{0}tkspostsignup", gatewayUrl);
                reactivationEmail.UpdateCardUrl = string.Format("{0}tksfailpayment", gatewayUrl);

                reactivationEmail.Template = "TurnKeySuiteReactivation.xslt";
                reactivationEmail.Subject = "TurnKey Suite is Reactivated - Thank you for your payment";
                reactivationEmail.To.Add(agent.Membership.Email);
                if (acctMgr != null && acctMgr.Membership != null && !string.IsNullOrWhiteSpace(acctMgr.Membership.Email))
                    reactivationEmail.Bcc.Add(acctMgr.Membership.Email);
                reactivationEmail.CC.Add(Settings.TurnKeySupportEmail);
                reactivationEmail.From = new MailAddress(Settings.MailgunFromEmailAddress, EmailFromDisplayName);
                reactivationEmail.ReplyTo = new MailAddressCollection { Settings.TurnKeySupportEmail };

                var transform = string.Empty;

                if (HttpContext.Current != null)
                    transform = HttpContext.Current.Server.MapPath(string.Format("~/bin/Transforms/{0}", reactivationEmail.Template));

                try
                {
                    Emailer.Send(transform, reactivationEmail, SmtpAccountType.Transactional);
                }
                catch (Exception exc)
                {
                    Logger.Warning("Failed to send service resumed email.", new LoggingPair[] { new LoggingPair(LoggingKey.AgentId, agent.Id), new LoggingPair(LoggingKey.FundingSourceId, fundingSource.Id) }, exc, methodName);
                }
            }
        }

        private void SendInvalidEmailFailedPaymentEmail(Agent agent)
        {
            var toAddress = Settings.TurnKeySupportEmail;
            var email = new AutoEmail
            {
                Template = Emails.GenericWithoutUserId.ToString(),
                UseAgentContactTime = false,
                Subject = "TKS payment failed but agent's email is invalid",
                ToAddress = new List<string> { toAddress },
                Parameters = $"We were not able to notify agent.<br />AgentId: {agent.Id}, Name: {agent.Membership.Name()}, Email: {agent.Membership.Email}"
            };

            var autoEmailEvent = new TransactionalAutoEmailEvent { Payload = email };
            EventBroker.Publish<TransactionalAutoEmailEvent, AutoEmail>(autoEmailEvent);
        }

        #endregion
    }
}