﻿using MLS.Synchronization.Models;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Messaging.Payloads;
using NPlay.Common.Models;
using System.Threading.Tasks;

namespace RealData.Simulation
{
    public interface IDataFeedSynchronizationSimulator
    {
        Task ProcessDataFeedAsync(DataFeed feed, bool forceImages, bool forceGeoCode);
        Task ProcessWithQueryRETSAsync(MLSFeedSchedulePayload schedulePayload, string query, bool forceImages = false, bool forceGeoCode = false);
        Task ProcessWithQueryRESOAsync(MLSFeedSchedulePayload schedulePayload, string query, bool forceImages = false, bool forceGeoCode = false);
        Task ProcessRETSAgentWithQueryAsync(MLSFeedSchedulePayload schedulePayload, string query);
        Task ProcessAgentWithQueryAsync(MultipleListingService mls, DataFeed feed, string query = null);
        Task ProcessWithQueryAsync(DataFeed feed, string query = null, bool forceImages = false, bool forceGeoCode = false);
        Task ProcessAgentAsync(MultipleListingService mls, DataFeed feed, string mlsAgentId);
        Task ProcessListingAsync(string mlsId, string internalId);
        Task ProcessListingAsync(MultipleListingService mls, DataFeed feed, string propertyListingId);
        Task ReindexListingAsync(RETSRawDataEvent ev);
        Task ProcessListingAsync(RESORawDataEvent ev, bool clearEvents = true);
        Task ProcessAgentAsync(string mlsId, string internalId);
        Task ProcessListingImagesAsync(string mlsId, string internalId);
        void ProcessListingSiteImages(string mlsId, string internalId);
        Task ProcessListingDataAsync(string mlsId, string internalId);
    }
}
