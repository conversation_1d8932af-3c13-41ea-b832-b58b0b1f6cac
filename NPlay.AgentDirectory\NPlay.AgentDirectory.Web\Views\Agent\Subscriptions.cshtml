﻿@{
    ViewBag.Title = "Subscriptions";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<template data-template-key="READ_Dashboard_Banner">
</template>

<div class="page-header">
    <h2 class="pull-left">My Subscriptions</h2>
</div>
<div class="mt10">
    <h4><strong>Questions? Contact: <a href="mailto:<EMAIL>"><EMAIL></a></strong></h4>
</div>


<div id="divMain">

    <div id="loadingDiv" class="spinner" style="display:none;position:absolute;left:50%;top:40%;"><div></div></div>
    <div id="divNoSubscriptions" style="display:none;"><br /><br /><h3>You have no subscriptions</h3></div>
    <ul id="ulSubscriptions" class="list-group list-group-lg mt15 fade" data-bind="foreach: subscriptions" style="display:none">
        <li class="list-group-item" data-bind="if: [16].indexOf(product.id()) === -1">
            <div class="row">
                <div class="col-md-2 col-sm-2 col-xs-4 icon product-avatar">
                    <img class="img-responsive" data-bind="attr: { src: product.thumbnail_url().replace('http://', '//') }">
                </div>
                <div class="col-md-22 col-sm-22 col-xs-20">
                    <div class="row">
                        <div class="col-md-14 col-sm-14">
                            <p class="heading"><span data-bind="text: product.name"></span> <small data-bind="visible: product.id() === 2 || (product.id() === 1 && productPlan.name().match(/(ads|lead|afordal)/i))"><em>(<span data-bind="text: productPlan.name"></span>)</em></small></p>
                            <p class="description">Status: <strong data-bind="text: displayStatus"></strong>
                            <a class="ml10 text-danger" data-bind="visible: ['Unpaid', 'PastDue'].includes(displayStatus())" href="@(NPlay.Settings.AgentDirectoryURL)Billing">Update Card</a></p>
                            <p class="description" data-bind="visible: product.id() === 1 && productPlan.name().match(/afordal/i)">
                                <em>Afordal Contact Limit: <span data-bind="text: ($parent.agent && $parent.agent.agentSettings && $parent.agent.agentSettings.find(s => s.Key === 'afordal_contact_limit')) ? $parent.agent.agentSettings.find(s => s.Key === 'afordal_contact_limit').Value : 500"></span></em>
                            </p>
                            <p class="description" data-bind="visible: product.id() === 1 && !($parent.agent && $parent.agent.homeSearchRegisteredDateTime)">
                                <em>NOTE: Pending IDX activation process. Please contact <a href="mailto:<EMAIL>"><EMAIL></a> for assistance.</em>
                            </p>
                        </div>
                        <div class="col-md-10 col-sm-10">
                            <p class="text-right text-left-xs subscriptions-plan small">Originally Purchased on: <strong class="ml5" data-bind="text: moment(dateCreated()).format('L')"></strong></p>
                            <p class="text-right text-left-xs subscriptions-plan small" data-bind="if:nextInvoiceDate()!=null ">Next Invoice Date: <strong class="ml5" data-bind="text: moment(nextInvoiceDate()).format('L')"></strong></p>
                            <p class="text-right text-left-xs subscriptions-plan small" data-bind="if:nextInvoiceDate()!=null ">Next Invoice Amount: <strong class="ml5" data-bind="currency: nextInvoiceAmount, symbol: '$'"></strong></p>
                            <p class="text-right text-left-xs subscriptions-plan small" data-bind="visible: $data.stripeSubscription && $data.stripeSubscription.CancelAtPeriodEnd()">
                                Service End Date: <strong class="ml5" data-bind="text: $data.stripeSubscription && moment($data.stripeSubscription.PeriodEnd()).format('L')"></strong>
                            </p>
                            <p class="text-right text-left-xs subscriptions-plan small" data-bind="visible: $data.stripeSubscription && $data.stripeSubscription.CancelAtPeriodEnd() && nextInvoiceAmount() && nextInvoiceAmount() > 0">
                                <a target="_blank" href="mailto:<EMAIL>?subject=CAPE Service Credit Request">Service credit available</a>
                            </p>
                        </div>
                    </div>
                    <div data-bind="visible: $data.stripeSubscription && $data.stripeSubscription.CancelAtPeriodEnd()">
                        <div data-bind="visible: !$parent.agent.accountInReview">
                            <a data-bind="attr: { 'data-fs-id': $data.id() }, click: $parent.cancelSubUndo"
                               type="button" class="btn btn-link pn">Undo Cancellation</a>  <a class="btn btn-link pln prn" href="#" data-toggle="popover" data-content="This subscription is set to cancel at period end. Click here to undo."><span class="profile-icon profile-icon-info"></span></a>
                        </div>
                        <div data-bind="visible: $parent.agent.accountInReview">
                            <label class="label label-danger">Dispute in Process</label> <a class="btn btn-link pln prn" href="#" data-toggle="popover" data-content="Please contact <NAME_EMAIL>"><span class="profile-icon profile-icon-info"></span></a>
                        </div>
                    </div>
                    <div id="changePlan" data-bind="visible: active() == true && product.id() > 0 && product.id() != 7 && !(product.id() === 1 && productPlan.name().match(/(ads|lead)/i))">
                        <div data-bind="visible: product.id() != 1 && (nextInvoiceDate() != null && nextInvoiceAmount() && nextInvoiceAmount() > 0) && false">
                            <div class="actions"><a href="#" data-bind="click: window.subscriptions.showChangePlan.bind($data)">Change Your Subscription Plan</a></div>
                            <div data-bind="attr: {'id': window.subscriptions.getChangePlanDivId($data) }" class="cart-edit-plan " style="display:none;">
                                <div class="row">

                                    <div class="col-md-10 col-sm-13">
                                        <div class="form-group mb5">

                                            <select class="form-control"
                                                    data-bind="selectedPlan: dropdownValue,
                                                                        options: product_plans,
                                                                        optionsText: function(item) {
                                                                                return item.description
                                                                        },
                                                                        optionsValue: function(item) {
                                                                                return item.id
                                                                        },
                                                                        selectedOptions:  selectedPlan,
                                                                        attr: {'onChange': window.subscriptions.getChangePlanButtonEnabledScript($data) }
                                                                        "></select>
                                        </div>
                                    </div>
                                    <div class="col-md-14 col-sm-11"><button type="button" data-bind="attr: {'id': window.subscriptions.getChangePlanButtonId($data) }, click:window.subscriptions.changePlanClick.bind($data,dropdownValue())" class="btn btn-sm btn-wide btn-success"><span aria-hidden="true" class="glyphicon glyphicon-shopping-cart mr5"></span>Change Plan</button></div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="row">
                                <div class="col-md-24 col-sm-24">
                                    <a data-bind="attr: { 'data-fs-id': $data.id() }, click: $parent.startCancelSub, visible: [1, 3, 4, 9, 10, 11, 12, 14, 15, 17, 18, 19].indexOf(product.id()) > -1 && $data.stripeSubscription && ($data.stripeSubscription.Status() === 2 || $data.stripeSubscription.Status() === 3) && !$data.stripeSubscription.CancelAtPeriodEnd()"
                                       type="button" class="btn btn-link pn mr20">
                                        Cancel Plan
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </li>
    </ul>

    <div id="cancel-sub-modal" class="modal fade cancel-sub" tabindex="-1" role="dialog">
        <div class="modal-dialog" data-bind="with: $root.selectedCancelSub">
            <div class="modal-content col-xs-23 col-sm-16 col-md-8 col-lg-6">
                <div class="modal-body text-center">
                    <button type="button" class="close large pull-right" data-dismiss="modal">×</button>
                    <p>
                        <h3>
                            <strong>
                                Are you sure you want to cancel
                                <span data-bind="text: $data.product && $data.product.name"></span>?
                            </strong>
                        </h3>
                    </p>
                    <div class="cancel-panels cancel-panel-1">
                        <p class="text-primary text-center">Please select a reason below for cancelling your service:</p>
                        <p>
                            <select class="form-control" id="cancel-sub-reason">
                                <option value=''>Select a reason</option>
                                <option value='No ROI/No Benefit'>No ROI/no benefit</option>
                                <option value='Understanding of Product/Not what expected'>Product not acceptable</option>
                                <option value='Retired/Not In Real Estate'>Retired/No longer in business</option>
                                <option value='Poor lead quality/quantity'>Poor lead quality/quantity</option>
                                <option value='Unrecognized charge'>Unrecognized charge</option>
                                <option value='Agent refuses auto-renewal services without pre-notice/will only pay by invoice'>Stop auto-renewal</option>
                                <option value='Various/Other'>Other (please explain)</option>
                            </select>
                        </p>
                        <br>
                        <a class="btn btn-primary" data-bind="click: $root.cancelPlanContinueClick">Continue</a>
                    </div>
                    <div class="cancel-panels cancel-panel-2" style="display:none">
                        <p>Cancel Reason: <em class="selected-cancel-reason"></em></p>

                        <textarea class="form-control mb10" id="other-cancel-reason" rows="4" maxlength="480"></textarea>

                        <div class="cancel-product-save text-left" data-bind="visible: $data.product && $data.product.id() === 1">
                            <p>We are sorry to learn that you want to cancel your IDX Home Search service on your Facebook page.</p>
                            <p>Here's what you'll lose:</p>
                            <ul>
                                <li>Capture leads with #1 home search on Facebook</li>
                                <li>Real time notification of leads with CRM integration (<em class="text-primary">new!</em>)</li>
                                <li>Preferred by 8 of 10 consumers over portal sites</li>
                                <li>Search is promoted automatically on your page up to 4x a month with zero maintenance by you</li>
                                <li>Check out these best practices to maximize the value of your service: <a href="https://helpcenter.homeasap.com/idx-help-center/">IDX Home Search User Guide</a></li>
                            </ul>
                        </div>

                        <div class="cancel-product-save text-left" data-bind="visible: $data.product && $data.product.id() === 3">
                            <p>We are sorry to learn that you want to cancel your Page Engage auto-posting service to your Facebook page.</p>
                            <p>Here's what you'll lose:</p>
                            <ul>
                                <li>Content is customizable with 23 categories (<em class="text-primary">new feature!</em>)</li>
                                <li>Your active listings are also posted automatically in an attractive carousel of up to 5 properties</li>
                                <li>Look like a social pro while you can spend more time connecting with new buyers and sellers</li>
                                <li>Check out these best practices to maximize the value of your service: <a href="https://helpcenter.homeasap.com/page-engage/">Page Engage User Guide</a></li>
                            </ul>
                        </div>

                        <div class="cancel-product-save text-left" data-bind="visible: $data.product && $data.product.id() === 4">
                            <p>We are sorry to learn that your wish to cancel your Dream Sweeps service on your Facebook page.</p>
                            <p>Here's what you'll lose:</p>
                            <ul>
                                <li>Increased engagement and lead capture from your Facebook Page</li>
                                <li>Real time notification of leads with CRM integration (<em class="text-primary">new!</em>)</li>
                                <li>Dream Sweeps is promoted automatically on your page up to 4x a month</li>
                                <li>We pay the $500 prize, and if your customer is selected, you’ll receive a bonus $100 credit toward another service!</li>
                                <li>Find more best practices here: <a href="https://helpcenter.homeasap.com/dream-sweeps-help-center/">Dream Sweeps User Guide </a></li>
                            </ul>
                        </div>

                        <div class="cancel-product-save text-left" data-bind="visible: $data.product && $data.product.id() === 9">
                            <p>We are sorry to learn that your wish to cancel your Directory Pro Membership service on your Facebook page.</p>
                            <p>Here's what you'll lose:</p>
                            <ul>
                                <li>Access to member-only webinars and resources for social media marketing and lead generation</li>
                                <li>Rank higher in the #1 Agent Directory on Facebook</li>
                                <li>Post and receive agent referrals on the largest customer referral group on Facebook</li>
                                <li>Receive special discounts and promotions on other services</li>
                            </ul>
                        </div>

                        <div class="cancel-product-save text-left" data-bind="visible: $data.product && $data.product.id() === 11">
                            <p>We are sorry to learn that you want to cancel your Home Value Leads Tool on your Facebook page.</p>
                            <p>Here's what you'll lose:</p>
                            <ul>
                                <li>Capture home seller leads from your Facebook page</li>
                                <li>Real time notification of leads with CRM integration (<em class="text-primary">new!</em>)</li>
                                <li>Promoted automatically on your page up to 4x each month</li>
                                <li>Find more best practices here: <a href="https://helpcenter.homeasap.com/home-value/">Home Value Leads Tool Guide</a></li>
                            </ul>
                        </div>

                        <div class="cancel-product-save text-left" data-bind="visible: $data.product && $data.product.id() === 12">
                            <p>We are sorry to learn that your wish to cancel your Property Poster service on your Facebook page.</p>
                            <p>Here's what you'll lose:</p>
                            <ul>
                                <li>Your listings are automatically posted to your Facebook page twice a month</li>
                                <li>Up to 5 of the most recent listings are displayed in an engaging carousel format</li>
                                <li>Additional listings from your brokerage can also be posted</li>
                                <li>Listing are automatically updated, added or removed – requiring zero maintenance by you!</li>
                            </ul>
                        </div>

                        <div class="text-left">
                            <label>
                                <small>
                                    <input class="cancel-confirm-checkbox" data-bind="checked: $root.confirmCancel" type="checkbox" />  I understand that by canceling below, my service will continue until  the current period end and I will not receive a refund.  You can apply your unused subscription value to another product. Contact Customer Support at 904-549-7600 or email: <a href="mailto:<EMAIL>"><EMAIL></a> for more details.
                                </small>
                            </label>
                        </div>

                        <div class="mt10">
                            <a class="btn btn-primary" data-bind="click: $root.stopCancelSub">Keep Service</a>
                            &nbsp;
                            <button class="btn btn-primary" data-bind="click: $root.cancelPlanClick(true), enable: $root.confirmCancel">Cancel At Period End</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@section scriptloader {

    $script(['@Scripts.Url("~/Scripts/lib/nplay/agent/services/subscriptions.js")',
    '@Scripts.Url("~/Scripts/lib/moment/moment.js")'], function() {
    $(document).ready(function() {

    var subscriptions = new nplay.controllers.subscriptions();
    window.subscriptions = subscriptions;

    window.subscriptions.initialize();
    });
    });
}
