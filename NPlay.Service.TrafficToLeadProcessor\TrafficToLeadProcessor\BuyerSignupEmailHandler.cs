﻿using NPlay;
using NPlay.BusinessLogic.Asap.Models;
using NPlay.Common.Abstract;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Models;
using NPlay.Common.Models.Emails.Engine;

namespace TrafficToLeadProcessor
{
    public class BuyerSignupEmailHandler : IBuyerSignupEmailHandler
   {
        public BuyerSignupEmailHandler(IEventBroker eventBroker,
            ILogger logger)
        {
            EventBroker = eventBroker;
            Logger = logger;
        }

        private readonly IEventBroker EventBroker;
        private readonly ILogger Logger;


        public void Send(BuyerLogin buyerLogin, int agentId, string savedSearchId = null, string rateplugBuyerId = null)
        {
            if (buyerLogin.Partner == "afordal")
            {
                Logger.Info("BuyerSignup email suppressed for afordal partner.", new LoggingPair[] { new LoggingPair(LoggingKey.BuyerId, buyerLogin.BuyerId), new LoggingPair(LoggingKey.CookieId, buyerLogin.CookieId) }, this, "Send");
                return;
            }

            var email = new AutoEmail
            {
                UserId = buyerLogin.BuyerId.Value,
                
                UseAgentContactTime = false
            };

            if (buyerLogin.Partner == "more")
            {
                email.Template = "BuyerSignup_MORE";
                email.SetParameters(new
                {
                    agentId = agentId,
                    destinationUrl = savedSearchId == null ? string.Format(Settings.HomeSearchAgentLandingUrl, agentId) : 
                                     $"{ Settings.HomeSearchRootUrl}saved-search/{savedSearchId}",
                    forLeadAds = buyerLogin.FromLeadAds,
                    ratePlugBuyerId = rateplugBuyerId
                });
            }
            else
            {
                email.Template = NPlay.Common.Emails.BuyerSignup.ToString();
                email.SetParameters(new
                {
                    agentId = agentId,
                    destinationUrl = buyerLogin.WelcomeEmailDestinationUrl,
                    forLeadAds = buyerLogin.FromLeadAds
                });
            }

            var autoEmailEvent = new TransactionalAutoEmailEvent { Payload = email };
            EventBroker.Publish<TransactionalAutoEmailEvent, AutoEmail>(autoEmailEvent);
            Logger.Info("BuyerSignup email has been queued.", new LoggingPair[] { new LoggingPair(LoggingKey.BuyerId, buyerLogin.BuyerId), new LoggingPair(LoggingKey.CookieId, buyerLogin.CookieId) }, this, "Send");
        }
    }
}
