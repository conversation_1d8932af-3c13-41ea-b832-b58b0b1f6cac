﻿using MLS.Synchronization.Abstract;
using Nest;
using Newtonsoft.Json;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Messaging.Payloads;
using NPlay.Common.Models;
using NPlay.Common.ServiceAgents;
using NPlay.Common.Services.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace RealEstateListingApp
{
    /// <summary>
    /// Iterates over all listings in batches and calls the RatePlug special finance endpoint to populate the special finance information for each listing
    /// </summary>
    /// <remarks>
    /// This code was largely generated by ChatGPT 4 using the following system message:
    ///•	You are a C# programmer.
    ///•	You're building a console application targeted at .NET Framework 4.8 using C# 7.
    ///•	Your data store is an elasticsearch 1.7 index that contains real estate listings differentiated by MLS.
    ///•	You can use the NEST NuGet Package to interact with the elasticsearch 1.7 index
    ///•	The listing documents have a string identifier called Id
    ///•	listings also have addresses, geocodes an array of MLS IDs and many other fields.
    ///•	Ask clarifying questions when additional information is needed about the listing data.
    ///•	Your ultimate goal is to create a console application with the requirements outlined below.
    ///•	Suspend responses until you hear the command "Program"
    ///
    ///Requirements:
    ///•	query the listing index in batches.
    ///•	Using each batch of listings, assemble a request to get additional information from a 3rd party RESTFUL Web Service.
    ///•	Use the result of the web service to augment the listing data, and save it
    ///
    ///  ChatGPT was subsequently fed an example request and response to the RatePlug special finance bulk endpoint.  Minor modifications were made.
    /// </remarks>
    class BulkSpecialFinanceUpdate
    {
        private static ElasticClient _client;
        private static HttpClient _httpClient;

        public static async Task UpdateAssumableStatusForAllListings(IRatePlugClient ratePlugClient, IActiveListingRepository activeListingRepository, IEventBroker eventBroker, string mlsId = null)
        {
            // Initialize Elasticsearch client  
            var settings = new ConnectionSettings(new Uri("http://es01.home.asap:9200")).SetDefaultIndex("mlspropertylisting");
            _client = new ElasticClient(settings);

            // Initialize HttpClient for Web Service  
            _httpClient = new HttpClient();

            DateTime start = DateTime.UtcNow;

            // Get all listings for a given FIPS code.  This means we'll need to probably get all listings into memory.
            //     Then group by FIPS code which gets applied based on county-state combo.
            var fipsDictionary = ratePlugClient.GetFHALoanLimits();
            var allListings = (await activeListingRepository.GetAllListings(mlsId, new string[]
                                                                            {
                                                                                "id",
                                                                                "address.cityName",
                                                                                "address.state",
                                                                                "countyCode",
                                                                                "status",
                                                                                "propertyType",
                                                                                "listPrice",
                                                                                "address.zipCode",
                                                                                "address.streetAddressNormalized"
                                                                            })).ToList();

            var rpListings = allListings.Where(l => l.Address?.StreetAddressNormalized != null &&
                                                    l.PropertyType != NPlay.Common.PropertyType.Commercial &&
                                                    l.PropertyType != NPlay.Common.PropertyType.Rental &&
                                                    l.PropertyType != NPlay.Common.PropertyType.LotsLand)
                                        .Select(l => new SpecialFinancingResponse()
                                        {
                                            HomeASAPID = l.Id,
                                            City = l.Address.CityName,
                                            State = l.Address.State,
                                            County = l.CountyCode,
                                            ListingStatus = l.Status,
                                            PropertyType = (int)l.PropertyType,
                                            ListPrice = l.ListPrice,
                                            Zip = l.Address.ZipCode,
                                            FIPS = ratePlugClient.GetFIPS(l),
                                            StreetAddressComplete = l.Address.StreetAddressNormalized?.Trim()
                                        });

            DateTime endGetListings = DateTime.UtcNow;

            var fipsGroups = from l in rpListings
                             group l by new { l.FIPS, l.Zip } into g
                             select g;

            var fipsCodeList = fipsGroups.Select(g => g.Key).ToList();

            List<string> fipsWithAssumables = new List<string>();
            foreach (var f in fipsGroups)
            {
                // Process listings in batches  
                int batchSize = 500;
                int currentBatch = 0;
                int processed = 0;

                while (processed < f.Count())
                {
                    if (f.Count() < batchSize)
                    {
                        batchSize = f.Count();
                    }

                    var listings = f.Skip(processed).Take(batchSize).ToList();
                    var webServiceResponses = await GetAssumablesWebServiceResponses(listings);
                    foreach (var r in webServiceResponses)
                    {
                        var listingSource = listings.FirstOrDefault(l => l.Id.ToLower() == r.HomeASAPID.ToLower());
                        if (listingSource != null)
                        {
                            r.HomeASAPID = listingSource.Id;
                        }
                    }

                    if (webServiceResponses.Any())
                    {
                        await UpdateListingsAssumables(listings, webServiceResponses, eventBroker);
                    }

                    currentBatch++;
                    processed += batchSize;
                }
            }
        }

        public static async Task UpdateNormalizedAddressesForAllListings(IActiveListingRepository activeListingRepository, IEventBroker eventBroker, string mlsId)
        {
            // Initialize Elasticsearch client  
            var settings = new ConnectionSettings(new Uri("http://es01.home.asap:9200")).SetDefaultIndex("mlspropertylisting");
            _client = new ElasticClient(settings);

            // Process listings in batches  
            var listings = (await activeListingRepository.GetAllListings(mlsId, new string[]
                                                                        {
                                                                            "id"
                                                                        })).ToList();

            foreach (var listing in listings)
            {
                eventBroker.Publish<AddressNormalizationEvent, AddressNormalizationPayload>(new AddressNormalizationEvent()
                {
                    Payload = new AddressNormalizationPayload()
                    {
                        NormalizeAddressOnly = true,
                        ForceUpdate = false,
                        InternalId = listing.Id
                    }
                });
            }
        }

        public static async Task UpdateFHACondoStatusForAllListings(IRatePlugClient ratePlugClient, IActiveListingRepository activeListingRepository, IEventBroker eventBroker, string mlsId = null)
        {
            // Initialize Elasticsearch client  
            var settings = new ConnectionSettings(new Uri("http://es01.home.asap:9200")).SetDefaultIndex("mlspropertylisting");
            _client = new ElasticClient(settings);

            // Initialize HttpClient for Web Service  
            _httpClient = new HttpClient();

            DateTime start = DateTime.UtcNow;

            // Get all listings for a given FIPS code.  This means we'll need to probably get all listings into memory.
            //     Then group by FIPS code which gets applied based on county-state combo.
            var fipsDictionary = ratePlugClient.GetFHALoanLimits();
            var allListings = (await activeListingRepository.GetAllListings(mlsId, new string[]
                                                                            {
                                                                                "id",
                                                                                "address.cityName",
                                                                                "address.state",
                                                                                "countyCode",
                                                                                "status",
                                                                                "propertyType",
                                                                                "listPrice",
                                                                                "address.zipCode",
                                                                                "address.streetAddressNormalized"
                                                                            })).ToList();

            var rpListings = allListings.Where(l => l.Address?.StreetAddressNormalized != null &&
                                                    l.PropertyType != NPlay.Common.PropertyType.Commercial &&
                                                    l.PropertyType != NPlay.Common.PropertyType.Rental &&
                                                    l.PropertyType != NPlay.Common.PropertyType.LotsLand)
                                        .Select(l => new SpecialFinancingResponse()
                                        {
                                            HomeASAPID = l.Id,
                                            City = l.Address.CityName,
                                            State = l.Address.State,
                                            County = l.CountyCode,
                                            ListingStatus = l.Status,
                                            PropertyType = (int)l.PropertyType,
                                            ListPrice = l.ListPrice,
                                            Zip = l.Address.ZipCode?.Split(new char[] { '-' })?.First(),
                                            FIPS = ratePlugClient.GetFIPS(l),
                                            StreetAddressComplete = l.Address.StreetAddressNormalized?.Trim()
                                        });

            DateTime endGetListings = DateTime.UtcNow;

            var fipsGroups = from l in rpListings
                             group l by l.Zip into g
                             select g;

            List<string> fipsWithAssumables = new List<string>();
            foreach (var f in fipsGroups)
            {
                // Process listings in batches  
                int batchSize = 500;
                int currentBatch = 0;
                int processed = 0;

                while (processed < f.Count())
                {
                    var listings = f.Skip(processed).Take(batchSize).ToList();
                    var webServiceResponses = await GetFHACondoWebServiceResponses(listings);
                    foreach (var r in webServiceResponses)
                    {
                        var listingSource = listings.FirstOrDefault(l => l.Id.ToLower() == r.HomeASAPID.ToLower());
                        if (listingSource != null)
                        {
                            r.HomeASAPID = listingSource.Id;
                        }
                    }

                    if (webServiceResponses.Any())
                    {
                        await UpdateListingsFHACondo(listings, webServiceResponses, eventBroker);
                    }

                    currentBatch++;
                    processed += batchSize;
                }
            }
        }

        public static async Task UpdateSpecialFinancingForAllListings()
        {
            // Initialize Elasticsearch client  
            var settings = new ConnectionSettings(new Uri("http://es01.home.asap:9200")).SetDefaultIndex("mlspropertylisting");
            _client = new ElasticClient(settings);

            // Initialize HttpClient for Web Service  
            _httpClient = new HttpClient();

            // Process listings in batches  
            int batchSize = 5000;
            int currentBatch = 0;
            while (true)
            {
                var listings = await GetListings(currentBatch, batchSize);
                if (!listings.Any())
                {
                    break;
                }

                var webServiceResponses = await GetWebServiceResponses(listings);
                await UpdateListings(listings, webServiceResponses);

                currentBatch++;
            }
        }

        private static async Task<List<SpecialFinancingResponse>> GetListings(int currentBatch, int batchSize)
        {
            var response = await _client.SearchAsync<MLSPropertyListing>(s => s
                .From(currentBatch * batchSize)
                .Size(batchSize)
                .Query(q => q.MatchAll()));

            return response.Documents.Where(l => l.Address != null).Select(l => new SpecialFinancingResponse()
            {
                HomeASAPID = l.Id,
                City = l.Address.CityName,
                State = l.Address.State,
                County = l.CountyCode,
                ListingStatus = l.Status,
                PropertyType = (int)l.PropertyType,
                ListPrice = l.ListPrice,
                Zip = l.Address.ZipCode,
                StreetAddressComplete = l.Address.FullStreetAddress
            }).ToList();
        }

        private static async Task<List<AssumablesMortgageResponse>> GetAssumablesWebServiceResponses(List<SpecialFinancingResponse> listings)
        {
            var request = PrepareAssumablesWebServiceRequest(listings);

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<AssumablesMortgageResponse>>(responseContent);
        }

        private static async Task<List<FHACondoResponse>> GetFHACondoWebServiceResponses(List<SpecialFinancingResponse> listings)
        {
            var request = PrepareFHACondoWebServiceRequest(listings);

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<FHACondoResponse>>(responseContent);
        }

        private static async Task<List<WebServiceResponse>> GetWebServiceResponses(List<SpecialFinancingResponse> listings)
        {
            var request = PrepareWebServiceRequest(listings);
            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<WebServiceResponse>>(responseContent);
        }

        private static HttpRequestMessage PrepareWebServiceRequest(List<SpecialFinancingResponse> listings)
        {
            var requestContent = JsonConvert.SerializeObject(listings);

            var request = new HttpRequestMessage(HttpMethod.Post, "https://app.rateplug.com/SpecFinDataAPI/api/RP/ProcessListings")
            {
                Content = new StringContent(requestContent, Encoding.UTF8, "application/json")
            };

            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            request.Headers.Add("RP-ApiKey", "d!2kuzOCHLcLsuxu_rOsW0yis");

            return request;
        }

        private static HttpRequestMessage PrepareAssumablesWebServiceRequest(List<SpecialFinancingResponse> listings)
        {
            var requestContent = JsonConvert.SerializeObject(listings);

            var request = new HttpRequestMessage(HttpMethod.Post, "https://app.rateplug.com/SpecFinDataAPI/api/RP/ProcessAssumableMatches")
            {
                Content = new StringContent(requestContent, Encoding.UTF8, "application/json")
            };

            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            request.Headers.Add("RP-ApiKey", "d!2kuzOCHLcLsuxu_rOsW0yis");

            return request;
        }

        private static HttpRequestMessage PrepareFHACondoWebServiceRequest(List<SpecialFinancingResponse> listings)
        {
            var requestContent = JsonConvert.SerializeObject(listings);

            var request = new HttpRequestMessage(HttpMethod.Post, "https://app.rateplug.com/SpecFinDataAPI/api/RP/ProcessFHACondoAddresses")
            {
                Content = new StringContent(requestContent, Encoding.UTF8, "application/json")
            };

            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            request.Headers.Add("RP-ApiKey", "d!2kuzOCHLcLsuxu_rOsW0yis");

            return request;
        }

        private static async Task UpdateListings(List<SpecialFinancingResponse> listings, List<WebServiceResponse> webServiceResponses)
        {
            var updatedListings = listings.Zip(webServiceResponses, (listing, wsResponse) =>
            {
                // Augment the listing data with the information from the Web Service response  
                listing.VAEligible = wsResponse.VAEligible;
                listing.USDAEligible = wsResponse.USDAEligible;
                listing.FHAEligible = wsResponse.FHAEligible;
                listing.FHACondoEligible = wsResponse.FHACondoEligible;
                listing.CRAEligible = wsResponse.CRAEligible;

                return listing;
            });

            if (updatedListings.Any())
            {
                var bulkUpdate = new BulkDescriptor();
                foreach (var updatedListing in updatedListings)
                {
                    bulkUpdate.Update<SpecialFinancingResponse>(u => u.Index("specialfinancing")
                        .Id(updatedListing.HomeASAPID)
                        .DocAsUpsert(true)
                        .Doc(updatedListing));
                }

                await _client.BulkAsync(b => bulkUpdate);
            }
        }

        private static async Task UpdateListingsAssumables(List<SpecialFinancingResponse> listings, List<AssumablesMortgageResponse> webServiceResponses, IEventBroker eventBroker)
        {
            // Pick out only the items that actually had a record returned in the response
            var matches = from l in listings
                          join r in webServiceResponses
                          on l.HomeASAPID equals r.HomeASAPID
                          select (l, r);

            listings = (from m in matches
                        select m.l).ToList();

            webServiceResponses = (from m in matches
                                   select m.r).ToList();

            var updatedListings = listings.Zip(webServiceResponses, (listing, wsResponse) =>
            {
                // Augment the listing data with the information from the Web Service response  
                listing.AssumableMortgageInfo = new AssumableMortgageInfo(wsResponse);

                return listing;
            });

            if (updatedListings.Any())
            {
                var bulkUpdate = new BulkDescriptor();
                foreach (var updatedListing in updatedListings)
                {
                    bulkUpdate.Update<SpecialFinancingResponse>(u => u.Index("specialfinancing")
                        .Id(updatedListing.HomeASAPID)
                        .DocAsUpsert(true)
                        .Doc(updatedListing));
                }

                await _client.BulkAsync(b => bulkUpdate);

                // Wait for elastic
                await Task.Delay(1000);
                foreach (var updatedListing in updatedListings)
                {
                    eventBroker.Publish<AssumableMortgageDataEvent, AssumableMortgagePayload>(new AssumableMortgageDataEvent()
                    {
                        Payload = new AssumableMortgagePayload()
                        {
                            InternalId = updatedListing.HomeASAPID,
                        }
                    });
                }
            }
        }

        private static async Task UpdateListingsFHACondo(List<SpecialFinancingResponse> listings, List<FHACondoResponse> webServiceResponses, IEventBroker eventBroker)
        {
            // Pick out only the items that actually had a record returned in the response
            var matches = from l in listings
                          join r in webServiceResponses
                          on l.HomeASAPID equals r.HomeASAPID
                          select (l, r);

            listings = (from m in matches
                        select m.l).ToList();

            webServiceResponses = (from m in matches
                                   select m.r).ToList();

            var updatedListings = listings.Zip(webServiceResponses, (listing, wsResponse) =>
            {
                // Augment the listing data with the information from the Web Service response  
                listing.FHACondoInfo = new FHACondoInfo(wsResponse);

                return listing;
            });

            if (updatedListings.Any())
            {
                var bulkUpdate = new BulkDescriptor();
                foreach (var updatedListing in updatedListings)
                {
                    bulkUpdate.Update<SpecialFinancingResponse>(u => u.Index("specialfinancing")
                        .Id(updatedListing.HomeASAPID)
                        .DocAsUpsert(true)
                        .Doc(updatedListing));
                }

                await _client.BulkAsync(b => bulkUpdate);

                // Wait for elastic
                await Task.Delay(1000);
                foreach (var updatedListing in updatedListings)
                {
                    eventBroker.Publish<FHACondoDataEvent, FHACondoPayload>(new FHACondoDataEvent()
                    {
                        Payload = new FHACondoPayload()
                        {
                            InternalId = updatedListing.HomeASAPID,
                        }
                    });
                }
            }
        }

        public static async Task UpdateUSDAEligibilityForAllListings(
            IRatePlugClient ratePlugClient,
            IActiveListingRepository activeListingRepository,
            IEventBroker eventBroker,
            string mlsId = null)
        {
            // Initialize Elasticsearch client  
            var settings = new ConnectionSettings(new Uri("http://es01.home.asap:9200")).SetDefaultIndex("mlspropertylisting");
            _client = new ElasticClient(settings);

            // Initialize HttpClient for Web Service  
            _httpClient = new HttpClient();

            // Get all listings for a given FIPS code and Zip
            var allListings = (await activeListingRepository.GetAllListings(mlsId, new string[]
            {
                "id",
                "address.cityName",
                "address.state",
                "countyCode",
                "status",
                "propertyType",
                "listPrice",
                "address.zipCode",
                "address.streetAddressNormalized"
            })).ToList();

            var rpListings = allListings
                .Where(l => l.Address?.StreetAddressNormalized != null &&
                            l.PropertyType != NPlay.Common.PropertyType.Rental)
                .Select(l => new SpecialFinancingResponse()
                {
                    HomeASAPID = l.Id,
                    City = l.Address.CityName,
                    State = l.Address.State,
                    County = l.CountyCode,
                    ListingStatus = l.Status,
                    PropertyType = (int)l.PropertyType,
                    ListPrice = l.ListPrice,
                    Zip = l.Address.ZipCode,
                    FIPS = ratePlugClient.GetFIPS(l),
                    StreetAddressComplete = l.Address.StreetAddressNormalized?.Trim()
                });

            var fipsZipGroups = from l in rpListings
                                group l by new { l.FIPS, l.Zip } into g
                                select g;

            foreach (var group in fipsZipGroups)
            {
                int batchSize = 500;
                int processed = 0;

                while (processed < group.Count())
                {
                    var batchListings = group.Skip(processed).Take(batchSize).ToList();
                    var usdaResponses = await GetUSDAEligibilityWebServiceResponses(batchListings);

                    foreach (var r in usdaResponses)
                    {
                        var listingSource = batchListings.FirstOrDefault(l => l.Id.ToLower() == r.HomeASAPID.ToLower());
                        if (listingSource != null)
                        {
                            r.HomeASAPID = listingSource.Id;
                        }
                    }

                    if (usdaResponses.Any())
                    {
                        await UpdateListingsUSDAEligibility(batchListings, usdaResponses, eventBroker);
                    }

                    processed += batchSize;
                }
            }
        }

        // Helper method to call the USDA eligibility endpoint
        private static async Task<List<WebServiceResponse>> GetUSDAEligibilityWebServiceResponses(List<SpecialFinancingResponse> listings)
        {
            var requestContent = JsonConvert.SerializeObject(listings);

            var request = new HttpRequestMessage(HttpMethod.Post, "https://app.rateplug.com/SpecFinDataAPI/api/RP/ProcessListingsUSDA")
            {
                Content = new StringContent(requestContent, Encoding.UTF8, "application/json")
            };

            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            request.Headers.Add("RP-ApiKey", "d!2kuzOCHLcLsuxu_rOsW0yis");

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<WebServiceResponse>>(responseContent);
        }

        // Helper method to update listings with USDA eligibility and publish events
        private static async Task UpdateListingsUSDAEligibility(
            List<SpecialFinancingResponse> listings,
            List<WebServiceResponse> usdaResponses,
            IEventBroker eventBroker)
        {
            // Match responses to listings
            var matches = from l in listings
                          join r in usdaResponses
                          on l.HomeASAPID equals r.HomeASAPID
                          select (l, r);

            listings = matches.Select(m => m.l).ToList();
            usdaResponses = matches.Select(m => m.r).ToList();

            var updatedListings = listings.Zip(usdaResponses, (listing, wsResponse) =>
            {
                listing.USDAEligible = wsResponse.USDAEligible;
                return listing;
            });

            updatedListings = updatedListings.Where(l => l.USDAEligible == true).ToList();
            if (updatedListings.Any())
            {
                var bulkUpdate = new BulkDescriptor();
                foreach (var updatedListing in updatedListings)
                {
                    bulkUpdate.Update<SpecialFinancingResponse>(u => u.Index("specialfinancing")
                        .Id(updatedListing.HomeASAPID)
                        .DocAsUpsert(true)
                        .Doc(updatedListing));
                }

                await _client.BulkAsync(b => bulkUpdate);

                // Publish USDADataEvent for each updated listing
                foreach (var updatedListing in updatedListings)
                {
                    eventBroker.Publish<USDADataEvent, USDAPayload>(new USDADataEvent()
                    {
                        Payload = new USDAPayload()
                        {
                            InternalId = updatedListing.HomeASAPID
                        }
                    });
                }
            }
        }

        private class Listing
        {
            public string HomeASAPID { get; set; }
            public int PropertyType { get; set; }
            public string ListingStatus { get; set; }
            public double? ListPrice { get; set; }
            public string StreetAddressCompleteStandard { get; set; }
            public string StreetAddressComplete { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Zip { get; set; }
            public string FIPS { get; set; }
            public string County { get; set; }
            public bool? VAEligible { get; set; }
            public bool? USDAEligible { get; set; }
            public bool? FHAEligible { get; set; }
            public bool? FHACondoEligible { get; set; }
            public bool? CRAEligible { get; set; }
            public bool? FHAAssumable { get; set; }
            public bool? VAAssumable { get; set; }
            public bool? USDAAssumable { get; set; }
        }

        private class WebServiceResponse
        {
            public string HomeASAPID { get; set; }
            public bool? VAEligible { get; set; }
            public bool? USDAEligible { get; set; }
            public bool? FHAEligible { get; set; }
            public bool? FHACondoEligible { get; set; }
            public bool? CRAEligible { get; set; }
            public bool? FHAAssumable { get; set; }
            public bool? VAAssumable { get; set; }
            public bool? USDAAssumable { get; set; }
        }
    }
}
