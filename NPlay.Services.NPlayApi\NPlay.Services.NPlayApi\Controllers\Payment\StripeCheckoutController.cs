﻿using AutoMapper;
using Newtonsoft.Json;
using NPlay.Common;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Enums;
using NPlay.Common.Models;
using NPlay.Common.Models.Payments;
using NPlay.Common.Models.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace NPlay.Services.NPlayApi.Controllers
{
    public class StripeCheckoutController : ApiController
    {
        private IPaymentSystem PaymentSystem;
        private IRepository<Agent> AgentRepository;
        private IRepository<ProductPlan> ProductPlanRepository;
        private IRepository<FundingSource> FundingSourceRepository;
        private IMapper Mapper;
        private ILogger Logger;
        IUnitOfWork UnitOfWork;
        public StripeCheckoutController(IPaymentSystem paymentSystem,
            IRepository<Agent> agentRepository,
            IRepository<ProductPlan> productPlanRepository,
            IRepository<FundingSource> fundingSourceRepository,
            I<PERSON>apper mapper,
            ILogger logger,
            IUnitOfWorkFactory uowFactory)
        {
            PaymentSystem = paymentSystem;
            AgentRepository = agentRepository;
            ProductPlanRepository = productPlanRepository;
            FundingSourceRepository = fundingSourceRepository;
            Logger = logger;
            Mapper = mapper;
            UnitOfWork = uowFactory.Create("Agent");
        }

        [HttpPost]
        [Authorize]
        [Route("api/checkoutsession")]
        public IHttpActionResult CreateCheckoutSession([FromBody] StripeSessionModel model)
        {
            int agentid;

            if (!int.TryParse(User.Identity.Name, out agentid))
            {
                return BadRequest();
            }

            var agent = AgentRepository.GetById(agentid);
            if(agent == null)
            {
                return BadRequest();
            }

            Customer customer = null;
            var customerId = agent.Membership?.FundingAccountId;
            if(string.IsNullOrEmpty(customerId))
            {
               customer = PaymentSystem.Customer.CreateCustomer(agent, 0);
               customerId = customer?.Id;
               agent.Membership.FundingAccountId = customerId;
               UnitOfWork.Commit();
            }
            else
            {
                try
                {
                    customer = PaymentSystem.Customer.GetCustomer(customerId);
                }
                catch(Exception ex)
                {
                    Logger.Error($"Could not get the customer for id {customerId} for agent {agent.Id}", new List<LoggingPair> { new LoggingPair(LoggingKey.AgentId, agent.Id),
                                                                                                                                 new LoggingPair(LoggingKey.CustomerId, customerId)}, ex, this, "CreateCheckoutSession");
                }
            }

            ProductPlan productPlan = null;
            if (model.Mode == "subscription")
            {
                productPlan = ProductPlanRepository.GetAll().Where(p => p.PlanId == model.PriceId).FirstOrDefault();
                if(productPlan != null)
                {
                    var isBundled = ((ProductType)productPlan.ProductId).IsBundledProduct();
                    var activePlan = FundingSourceRepository.GetAll().Where(fs => fs.ProductTypeId == productPlan.ProductId && fs.MembershipId == agentid && fs.Active == true).FirstOrDefault();
                    var hasTKS = false;
                    if (isBundled)
                    {
                        hasTKS = FundingSourceRepository.GetAll().Where(fs => fs.ProductTypeId == (int)ProductType.Bundle && fs.MembershipId == agentid && fs.Active == true).FirstOrDefault() != null;
                    }

                    // Allow checkout if purchasing Afordal IDX products
                    // ProductId is IDX (1) and ProductPlanTypeId is AfordalIncluded or AfordalInstagramIDXIncluded
                    var shouldAllowCheckoutOverride = productPlan.ProductId == 1 &&
                                            (productPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalIncluded ||
                                            productPlan.ProductPlanTypeId == (byte)ProductPlanType.AfordalInstagramIDXIncluded);

                    if ((activePlan != null || (isBundled && hasTKS)) && !shouldAllowCheckoutOverride)
                    {
                        return BadRequest("Product already exists for the user");
                    }
                    var metaData = new Dictionary<string, string>();
                    if (customer != null)
                    {
                        if (customer.Metadata.ContainsKey(StripeMetaDataNames.LastCheckoutSessionProductPlanName))
                        {
                            customer.Metadata[StripeMetaDataNames.LastCheckoutSessionProductPlanName] = productPlan.Name;
                        }
                        else
                        {
                            customer.Metadata.Add(StripeMetaDataNames.LastCheckoutSessionProductPlanName, productPlan.Name);
                        }

                        if (customer.Metadata.ContainsKey(StripeMetaDataNames.LastCheckoutSessionPrice))
                        {
                            customer.Metadata[StripeMetaDataNames.LastCheckoutSessionPrice] = productPlan.Amount.ToString();
                        }
                        else
                        {
                            customer.Metadata.Add(StripeMetaDataNames.LastCheckoutSessionPrice, productPlan.Amount.ToString());
                        }

                        if (customer.Metadata.ContainsKey(StripeMetaDataNames.LastCheckoutSessionDate))
                        {
                            customer.Metadata[StripeMetaDataNames.LastCheckoutSessionDate] = DateTime.UtcNow.ToString("G");
                        }
                        else
                        {
                            customer.Metadata.Add(StripeMetaDataNames.LastCheckoutSessionDate, DateTime.UtcNow.ToString("G"));
                        }

                        PaymentSystem.Customer.UpdateCustomerMetadata(customer.Id, customer.Metadata);
                    }
                }
            }

            StripeCheckoutSessionResult response = null;
            try
            {
                // Include one-time fee if it exists for subscription mode
                if (model.Mode == "subscription" && productPlan?.Fee.HasValue == true && productPlan.Fee.Value > 0)
                {
                    response = PaymentSystem.Checkout.CreateSession(customerId, model.CancelUrl, model.SuccessUrl, model.PriceId, model.Mode, model.CouponCodeId, productPlan.Fee.Value, productPlan.FeeDescription);
                }
                else
                {
                    response = PaymentSystem.Checkout.CreateSession(customerId, model.CancelUrl, model.SuccessUrl, model.PriceId, model.Mode, model.CouponCodeId);
                }
            }
            catch(Exception ex)
            {
                Logger.Error($"Could not create checkout session for agent {agentid}. {ex.Message}",null, ex, this, "CreateCheckoutSession");
                return BadRequest(ex.Message);
            }
            if (!string.IsNullOrWhiteSpace(response.error_message))
            {
                return BadRequest(response.error_message);
            }

            return Ok(response);

        }

    }
}
