﻿using MLS.Synchronization.Abstract;
using MLS.Synchronization.Models;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Runtime.Caching;
using System.Xml.Linq;

namespace MLS.Synchronization
{
    public class MlsSynchronizationContextProvider : IMlsSynchronizationContextProvider
    {
        public ICachedMLSRepository MLSCache { get; set; }
        ConcurrentDictionary<string, MlsSynchronizationContext> ContextDictionary = new ConcurrentDictionary<string, MlsSynchronizationContext>();

        private MlsSynchronizationContextProvider()
        {
            ContextDictionary["akmls"] = new MlsSynchronizationContext()
            {
                MlsId = "akmls",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["albcmls"] = new MlsSynchronizationContext()
            {
                MlsId = "albcmls",
                DataProviderMlsId = "bk20",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Paragon,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["almmmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "almmmls-a",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["alnaris"] = new MlsSynchronizationContext()
            {
                MlsId = "alnaris",
                AttributionContactFieldName = "VALLEYMLS_L_AttributionContact",
                DataProviderMlsId = "valleymls",
                DataProviderListingPrefix = null,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["arfsmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "arfsmls-a",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["arrbor-y"] = new MlsSynchronizationContext()
            {
                MlsId = "arrbor-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["azlhaor"] = new MlsSynchronizationContext()
            {
                MlsId = "azlhaor",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["aznaz-y"] = new MlsSynchronizationContext()
            {
                MlsId = "aznaz-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["azpaar-y"] = new MlsSynchronizationContext()
            {
                MlsId = "azpaar-y",
                DataProviderMlsId = "paar",
                DataProviderListingPrefix = "PAR",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["azrmls"] = new MlsSynchronizationContext()
            {
                MlsId = "azrmls",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["azwar"] = new MlsSynchronizationContext()
            {
                MlsId = "azwar",
                DataProviderMlsId = "wardex",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["cabak"] = new MlsSynchronizationContext()
            {
                MlsId = "cabak",
                DataProviderMlsId = "BAK",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                DataProvider = DataProviders.Rapattoni,
                DataRestrictions = MlsDataRestrictions.IDXWebsiteOnly
            };
            ContextDictionary["cabareis"] = new MlsSynchronizationContext()
            {
                MlsId = "cabareis",
                ProtocolType = DataFeedProtocol.RETS,
                ImageLocationType = ImageLocationType.MediaFeed,
                MediaFeedKeyFieldName = "ListingRid",
                MediaFeedClassName = "PPIC",
                MediaMetadataInfo = new MediaMetadataInfo()
                {
                    DescriptionFieldName = "LongDescription",
                    OrderFieldName = "SortOrder",
                    MediaUrlFieldName = "URL",
                }
            };
            ContextDictionary["caebrd-f"] = new MlsSynchronizationContext()
            {
                MlsId = "caebrd-f",
                AttributionContactFieldName = "BECCAR_L_AttributionContact",
                DataProviderMlsId = "bridgemls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["cahum-y"] = new MlsSynchronizationContext()
            {
                MlsId = "cahum-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["caiamls-b"] = new MlsSynchronizationContext()
            {
                MlsId = "caiamls-b",
                ProtocolType = DataFeedProtocol.RETS,
                MetadataTypeParameterName = "Type",
                RequiresXMLFormatInMetadataQuery = true,
            };
            ContextDictionary["camax-y"] = new MlsSynchronizationContext()
            {
                MlsId = "camax-y",
                AttributionContactFieldName = "BECCAR_L_AttributionContact",
                DataProviderMlsId = "beccar",
                DataProviderListingPrefix = null,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["camax-y2"] = new MlsSynchronizationContext()
            {
                MlsId = "camax-y2",
                AttributionContactFieldName = "BECCAR_L_AttributionContact",
                DataProviderMlsId = "beccar",
                DataProviderListingPrefix = null,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["camrmls"] = new MlsSynchronizationContext()
            {
                MlsId = "camrmls",
                DataProviderMlsId = "crmls",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["caramls-b"] = new MlsSynchronizationContext()
            {
                MlsId = "caramls-b",
                ProtocolType = DataFeedProtocol.RETS,
                MetadataTypeParameterName = "Type",
                RequiresXMLFormatInMetadataQuery = true,
            };
            ContextDictionary["careil-r"] = new MlsSynchronizationContext()
            {
                MlsId = "careil-r",
                ProtocolType = DataFeedProtocol.RETS,
                ImageLocationType = ImageLocationType.MediaFeed,
                AuthenticationActionResponseStyle = AuthenticationActionResponseStyle.Custom,
                ValidateAuthenticationAction = (responseString) =>
                {
                    XDocument doc = XDocument.Parse(responseString);
                    return doc.Element("Messages") != null;
                },
                MediaFeedKeyFieldName = "ResourceRecordKeyNumeric",
            };
            ContextDictionary["casar"] = new MlsSynchronizationContext()
            {
                MlsId = "casar",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["casbaor-y"] = new MlsSynchronizationContext()
            {
                MlsId = "casbaor-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["catcmls"] = new MlsSynchronizationContext()
            {
                MlsId = "catcmls",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["cavcrds"] = new MlsSynchronizationContext()
            {
                MlsId = "cavcrds",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["coaspen"] = new MlsSynchronizationContext()
            {
                MlsId = "coaspen",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["coires-y"] = new MlsSynchronizationContext()
            {
                MlsId = "coires-y",
                AttributionContactFieldName = "IRESDS_AttributionContact",
                DataProviderMlsId = "iresds",
                DataProviderListingPrefix = null,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["comlmls-y"] = new MlsSynchronizationContext()
            {
                MlsId = "comlmls-y",
                AttributionContactFieldName = "REC_AttributionContact",
                DataProviderMlsId = "recolorado",
                DataProviderListingPrefix = "REC",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["covbr"] = new MlsSynchronizationContext()
            {
                MlsId = "covbr",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["flbayc"] = new MlsSynchronizationContext()
            {
                MlsId = "flbayc",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["flbrevc"] = new MlsSynchronizationContext()
            {
                MlsId = "flbrevc",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["flcitrus"] = new MlsSynchronizationContext()
            {
                MlsId = "flcitrus",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["fldbaa"] = new MlsSynchronizationContext()
            {
                MlsId = "fldbaa",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["flecar-y"] = new MlsSynchronizationContext()
            {
                MlsId = "flecar-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["flftmyers"] = new MlsSynchronizationContext()
            {
                MlsId = "flftmyers",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["flmfr"] = new MlsSynchronizationContext()
            {
                MlsId = "flmfr",
                AttributionContactFieldName = "MFR_AttributionContact",
                DataProviderMlsId = "mfrmls",
                DataProviderListingPrefix = "MFR",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["flnemls"] = new MlsSynchronizationContext()
            {
                MlsId = "flnemls",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["flpar"] = new MlsSynchronizationContext()
            {
                MlsId = "flpar",
                AttributionContactFieldName = "PENSACOLA_L_AttributionContact",
                DataProviderMlsId = "pensacola",
                DataProviderListingPrefix = null,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["flrairc"] = new MlsSynchronizationContext()
            {
                MlsId = "flrairc",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["flsemls-r"] = new MlsSynchronizationContext()
            {
                MlsId = "flsemls-r",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["fltbr"] = new MlsSynchronizationContext()
            {
                MlsId = "fltbr",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["gaabor"] = new MlsSynchronizationContext()
            {
                MlsId = "gaabor",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["gafmls"] = new MlsSynchronizationContext()
            {
                MlsId = "gafmls",
                DataProviderMlsId = "fmls",
                DataProviderListingPrefix = null,
                ModificationTimestampFieldName = "MajorChangeTimestamp",
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["gagaar"] = new MlsSynchronizationContext()
            {
                MlsId = "gagaar",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["gamls"] = new MlsSynchronizationContext()
            {
                MlsId = "gamls",
                ImageLocationType = ImageLocationType.MediaFeed,
                DataProvider = DataProviders.ConnectMLS,
                MediaFeedKeyFieldName = "MediaResourceId",
                MediaMetadataInfo = new MediaMetadataInfo()
                {
                    DescriptionFieldName = "MediaTitle",
                    OrderFieldName = "MediaOrder",
                    MediaUrlFieldName = "MediaURL",
                }
            };
            ContextDictionary["gasbr"] = new MlsSynchronizationContext()
            {
                MlsId = "gasbr",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["hihis"] = new MlsSynchronizationContext()
            {
                MlsId = "hihis",
                ProtocolType = DataFeedProtocol.RETS,
                MetadataTypeParameterName = "Type"
            };
            ContextDictionary["hihonbor-y"] = new MlsSynchronizationContext()
            {
                MlsId = "hihonbor-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["himaui"] = new MlsSynchronizationContext()
            {
                MlsId = "himaui",
                DataProviderMlsId = "bk1",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Paragon,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["iacent"] = new MlsSynchronizationContext()
            {
                MlsId = "iacent",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["iacraor"] = new MlsSynchronizationContext()
            {
                MlsId = "iacraor",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["iadmaar"] = new MlsSynchronizationContext()
            {
                MlsId = "iadmaar",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["iaincmls"] = new MlsSynchronizationContext()
            {
                MlsId = "iaincmls",
                DataProviderMlsId = "nocoast",
                DataProviderListingPrefix = "NOC",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["iarmlsa"] = new MlsSynchronizationContext()
            {
                MlsId = "iarmlsa",
                AttributionContactFieldName = "RMA_AttributionContact",
                DataProviderMlsId = "rmlsa",
                DataProviderListingPrefix = "RMA",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["idswrmls-y"] = new MlsSynchronizationContext()
            {
                MlsId = "idswrmls-y",
                DataProviderMlsId = "imls2",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["ilmlsni-r"] = new MlsSynchronizationContext()
            {
                MlsId = "ilmlsni-r",
                DataProviderMlsId = "mred",
                DataProviderListingPrefix = "MRD",
                UseOwnershipForCondoMapping = true,
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["ingniar"] = new MlsSynchronizationContext()
            {
                MlsId = "ingniar",
                AttributionContactFieldName = "GNR_AttributionContactEmail",
                DataProviderMlsId = "nira",
                DataProviderListingPrefix = "NRA",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["inmibor"] = new MlsSynchronizationContext()
            {
                MlsId = "inmibor",
                AttributionContactFieldName = "MBR_AttributionContact",
                DataProviderMlsId = "mibor",
                DataProviderListingPrefix = "MBR",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["insira-y"] = new MlsSynchronizationContext()
            {
                MlsId = "insira-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["ksheart"] = new MlsSynchronizationContext()
            {
                MlsId = "ksheart",
                AttributionContactFieldName = "HMS_AttributionContact",
                DataProviderMlsId = "hmls",
                DataProviderListingPrefix = "HMS",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["kssc"] = new MlsSynchronizationContext()
            {
                MlsId = "kssc",
                AttributionContactFieldName = "SCK_AttributionContact",
                DataProviderMlsId = "sckansas",
                DataProviderListingPrefix = "SCK",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["kyglar-y"] = new MlsSynchronizationContext()
            {
                MlsId = "kyglar-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["kylbar"] = new MlsSynchronizationContext()
            {
                MlsId = "kylbar",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["kynkar"] = new MlsSynchronizationContext()
            {
                MlsId = "kynkar",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["lagbrar"] = new MlsSynchronizationContext()
            {
                MlsId = "lagbrar",
                DataProviderMlsId = "gbrmls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["mabcmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "mabcmls-a",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["maccia"] = new MlsSynchronizationContext()
            {
                MlsId = "maccia",
                DataProviderMlsId = "ccimls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["malinkmls"] = new MlsSynchronizationContext()
            {
                MlsId = "malinkmls",
                DataProviderMlsId = "link_vineyard",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["mamvmls"] = new MlsSynchronizationContext()
            {
                MlsId = "mamvmls",
                DataProviderMlsId = "mvmls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["mamlspin"] = new MlsSynchronizationContext()
            {
                MlsId = "mamlspin",
                DataProviderMlsId = "mlspin",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["mdmris-y"] = new MlsSynchronizationContext()
            {
                MlsId = "mdmris-y",
                ProtocolType = DataFeedProtocol.RETS,
                PhotoDownloadType = "Photo-HD"
            };
            ContextDictionary["memreis-y"] = new MlsSynchronizationContext()
            {
                MlsId = "memreis-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["mimc-v"] = new MlsSynchronizationContext()
            {
                MlsId = "mimc-v",
                ProtocolType = DataFeedProtocol.RETS,
                ProvidesImageCount = false,
            };
            ContextDictionary["miswmar"] = new MlsSynchronizationContext()
            {
                MlsId = "miswmar",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["mnrmls-y"] = new MlsSynchronizationContext()
            {
                MlsId = "mnrmls-y",
                AttributionContactFieldName = "NST_AttributionContact",
                DataProviderMlsId = "northstar",
                DataProviderListingPrefix = "NST",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["mocbor"] = new MlsSynchronizationContext()
            {
                MlsId = "mocbor",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["momaris"] = new MlsSynchronizationContext()
            {
                MlsId = "momaris",
                DataProviderMlsId = "maris2",
                DataProviderListingPrefix = "MIS",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["moselmo"] = new MlsSynchronizationContext()
            {
                MlsId = "moselmo",
                DataProviderMlsId = "somo",
                DataProviderListingPrefix = "SOM",
                AttributionContactFieldName = "ListOfficePhone",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["mshmmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "mshmmls-a",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["msmlsu"] = new MlsSynchronizationContext()
            {
                MlsId = "msmlsu",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["mtnmaor-y"] = new MlsSynchronizationContext()
            {
                MlsId = "mtnmaor-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["nccmls"] = new MlsSynchronizationContext()
            {
                MlsId = "nccmls",
                AttributionContactFieldName = "CAR_AttributionContact",
                DataProviderMlsId = "carolina",
                DataProviderListingPrefix = "CAR",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["ncfar"] = new MlsSynchronizationContext()
            {
                MlsId = "ncfar",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["ncrmls-y"] = new MlsSynchronizationContext()
            {
                MlsId = "ncrmls-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["nctriad"] = new MlsSynchronizationContext()
            {
                MlsId = "nctriad",
                DataProviderMlsId = "triadmls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["nctriangle"] = new MlsSynchronizationContext()
            {
                MlsId = "nctriangle",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["ndfmar"] = new MlsSynchronizationContext()
            {
                MlsId = "ndfmar",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["neoabr"] = new MlsSynchronizationContext()
            {
                MlsId = "neoabr",
                DataProviderMlsId = "bk9",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Paragon,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["nhnne"] = new MlsSynchronizationContext()
            {
                MlsId = "nhnne",
                DataProviderMlsId = "neren",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.OpenMLS,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["njgsmls"] = new MlsSynchronizationContext()
            {
                MlsId = "njgsmls",
                ProtocolType = DataFeedProtocol.RETS,
                MetadataDownloadStyle = MetadataDownloadStyle.Ftp
            };
            ContextDictionary["njmcmls"] = new MlsSynchronizationContext()
            {
                MlsId = "njmcmls",
                DataProviderMlsId = "jerseymls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["njmomls-r"] = new MlsSynchronizationContext()
            {
                MlsId = "njmomls-r",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["nmsfar"] = new MlsSynchronizationContext()
            {
                MlsId = "nmsfar",
                DataProviderMlsId = "sfaor",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["nmswmls"] = new MlsSynchronizationContext()
            {
                MlsId = "nmswmls",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["nvnnrmls"] = new MlsSynchronizationContext()
            {
                MlsId = "nvnnrmls",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["nyccb-y"] = new MlsSynchronizationContext()
            {
                MlsId = "nyccb-y",
                ProtocolType = DataFeedProtocol.RETS,
                MetadataDownloadStyle = MetadataDownloadStyle.Ftp
            };
            ContextDictionary["nycrmls-y"] = new MlsSynchronizationContext()
            {
                MlsId = "nycrmls-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["nyokmls"] = new MlsSynchronizationContext()
            {
                MlsId = "nyokmls",
                AttributionContactFieldName = "ONE_AttributionContact",
                DataProviderMlsId = "onekey2",
                DataProviderListingPrefix = "KEY",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["nyrebny-y"] = new MlsSynchronizationContext()
            {
                MlsId = "nyrebny-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["nysamls"] = new MlsSynchronizationContext()
            {
                MlsId = "nysamls",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["nyucbor"] = new MlsSynchronizationContext()
            {
                MlsId = "nyucbor",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["ohcincy"] = new MlsSynchronizationContext()
            {
                MlsId = "ohcincy",
                DataProviderMlsId = "cincymls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                DataProvider = DataProviders.Perchwell,
                DataRestrictions = MlsDataRestrictions.IDXWebsiteOnly
            };
            ContextDictionary["ohcmmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "ohcmmls-a",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["ohnomls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "ohnomls-a",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["ohtbor-y"] = new MlsSynchronizationContext()
            {
                MlsId = "ohtbor-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["okcmbr-y"] = new MlsSynchronizationContext()
            {
                MlsId = "okcmbr-y",
                DataProviderMlsId = "mlsok",
                DataProviderListingPrefix = "OKC",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["oknores"] = new MlsSynchronizationContext()
            {
                MlsId = "oknores",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["ontrreb"] = new MlsSynchronizationContext()
            {
                MlsId = "ontrreb",
                ProtocolType = DataFeedProtocol.RETS,
                MetadataTypeParameterName = "Type",
                ProvidesImageCount = false,
                Country = "Canada",
            };
            ContextDictionary["orcoaor"] = new MlsSynchronizationContext()
            {
                MlsId = "orcoaor",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["orlcmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "orlcmls-a",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["orrmls"] = new MlsSynchronizationContext()
            {
                MlsId = "orrmls",
                DataProvider = DataProviders.RMLS,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
            };
            ContextDictionary["pawpml"] = new MlsSynchronizationContext()
            {
                MlsId = "pawpml",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["rimls"] = new MlsSynchronizationContext()
            {
                MlsId = "rimls",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["scbmmls"] = new MlsSynchronizationContext()
            {
                MlsId = "scbmmls",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["scccar"] = new MlsSynchronizationContext()
            {
                MlsId = "scccar",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["scctmls"] = new MlsSynchronizationContext()
            {
                MlsId = "scctmls",
                DataProviderMlsId = "chsmls",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Bridge,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["scgga-y"] = new MlsSynchronizationContext()
            {
                MlsId = "scgga-y",
                DataProviderMlsId = "bk4",
                DataProviderListingPrefix = null,
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.Paragon,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["scspart"] = new MlsSynchronizationContext()
            {
                MlsId = "scspart",
                DataProviderMlsId = "spartanburg",
                DataProviderListingPrefix = "SPN",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["scwumls"] = new MlsSynchronizationContext()
            {
                MlsId = "scwumls",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["tncar"] = new MlsSynchronizationContext()
            {
                MlsId = "tncar",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["tngsmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "tngsmls-a",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["tnknox-y"] = new MlsSynchronizationContext()
            {
                MlsId = "tnknox-y",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["tnmtr-r"] = new MlsSynchronizationContext()
            {
                MlsId = "tnmtr-r",
                AttributionContactFieldName = "RTC_AttributionContact",
                DataProviderMlsId = "realtrac",
                DataProviderListingPrefix = "RTC",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["tnvamls"] = new MlsSynchronizationContext()
            {
                MlsId = "tnvamls",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["txaustin"] = new MlsSynchronizationContext()
            {
                MlsId = "txaustin",
                AttributionContactFieldName = "ACT_AttributionContact",
                DataProviderMlsId = "actris",
                DataProviderListingPrefix = "ACT",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["txccar"] = new MlsSynchronizationContext()
            {
                MlsId = "txccar",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["txctmls-y"] = new MlsSynchronizationContext()
            {
                MlsId = "txctmls-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["txdallas"] = new MlsSynchronizationContext()
            {
                MlsId = "txdallas",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["txgepaor"] = new MlsSynchronizationContext()
            {
                MlsId = "txgepaor",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["txhlar-y"] = new MlsSynchronizationContext()
            {
                MlsId = "txhlar-y",
                DataProviderMlsId = "highland",
                DataProviderListingPrefix = "HLM",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
            };
            ContextDictionary["txlub"] = new MlsSynchronizationContext()
            {
                MlsId = "txlub",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["txsabor"] = new MlsSynchronizationContext()
            {
                MlsId = "txsabor",
                ParticipatesInRemoveDataJobs = false,
            };
            ContextDictionary["txwar-y"] = new MlsSynchronizationContext()
            {
                MlsId = "txwar-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["uticmls-a"] = new MlsSynchronizationContext()
            {
                MlsId = "uticmls-a",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["utwcbr"] = new MlsSynchronizationContext()
            {
                MlsId = "utwcbr",
                DataProvider = DataProviders.Spark,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["varar-y"] = new MlsSynchronizationContext()
            {
                MlsId = "varar-y",
                DataProvider = DataProviders.Trestle,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                ProvidesListOnInternet = false,
                RateLimitingConfiguration = new RateLimitingConfiguration()
                {
                    Enabled = true,
                    HttpResponseCode = 429,
                    ResetTimeHttpHeaderName = "Hour-Quota-ResetTime",
                }
            };
            ContextDictionary["utwfrmls-b"] = new MlsSynchronizationContext()
            {
                MlsId = "utwfrmls-b",
                DataProvider = DataProviders.UtahRealEstate,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
            ContextDictionary["wanwmls"] = new MlsSynchronizationContext()
            {
                MlsId = "wanwmls",
                DataProviderMlsId = "nwmls",
                DataProviderListingPrefix = "NWM",
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi,
                UseCumulativeDaysOnMarket = true
            };
            ContextDictionary["wasar-v"] = new MlsSynchronizationContext()
            {
                MlsId = "wasar-v",
                DataProviderMlsId = "sarmls",
                DataProviderListingPrefix = "SAR_U",
                ProvidesListOnInternet = false,
                DataProvider = DataProviders.MlsGrid,
                ProtocolType = DataFeedProtocol.RESOWebApi
            };
        }

        private static readonly MlsSynchronizationContextProvider instance = new MlsSynchronizationContextProvider();
        public static MlsSynchronizationContextProvider Instance => instance;

        /// <summary>
        /// Gets the Mls synchronization context.  Any settings from the database are updated once per hour.
        /// </summary>
        /// <param name="mlsId"></param>
        /// <returns></returns>
        public MlsSynchronizationContext Get(string mlsId)
        {
            MlsSynchronizationContext context;

            // Once the context exists, we will keep it in the dictionary
            if (ContextDictionary.ContainsKey(mlsId))
            {
                context = ContextDictionary[mlsId];
            }
            else
            {
                context = new MlsSynchronizationContext()
                {
                    MlsId = mlsId,
                    ProtocolType = DataFeedProtocol.RETS,
                    ImageLocationType = ImageLocationType.RemoteLocation,
                    RateLimitingConfiguration = new RateLimitingConfiguration(),
                };
            }

            // Always set the database settings because they can change when configured via internal MLS configuration tools.
            // Since these processes can be long-lived, we need to make sure the settings are updated when they are changed with the config tools.
            ConfigureDatabaseSettings(context);
            ConfigureOverrides(context);
            ContextDictionary[mlsId] = context;

            return context;
        }

        private ImageLocationType? GetImageLocationType(string mlsId)
        {
            if (new string[] {
                        "descar-y",
                        "flmfr",
                        "gamgmls-a",
                        "ilmlsni-r",
                        "njgsmls",
                        "orrmls",
                        "palvmls-y",
                        "txctmls-y",
                        "txsabor",
                    }.Contains(mlsId))
            {
                return ImageLocationType.RETSServer;
            }

            return null;
        }

        private void ConfigureOverrides(MlsSynchronizationContext context)
        {
            var imageLocationType = GetImageLocationType(context.MlsId);
            if (imageLocationType.HasValue)
                context.ImageLocationType = imageLocationType.Value;
        }

        private void ConfigureDatabaseSettings(MlsSynchronizationContext context)
        {
            // Not all processes even need to get settings from the database, so we will only act if there is an instance of MLS Repository set
            if (MLSCache != null)
            {
                MultipleListingService mls = MLSCache.Get(context.MlsId);

                // All database related context settings can be set within this block.
                if (mls != null)
                {
                    if (mls.AllowAgentListingUpdateNotifications)
                    {
                        // Turn off the IDXWebsiteOnly flag because these notifications are allowed
                        context.DataRestrictions &= ~MlsDataRestrictions.IDXWebsiteOnly;
                    }
                    else
                    {
                        // Turn on the IDXWebsiteOnly flag
                        context.DataRestrictions |= MlsDataRestrictions.IDXWebsiteOnly;
                    }

                    var defaultCredentials = mls.RETSCredentials.FirstOrDefault(c => c.IsDefault);
                    if (defaultCredentials != null)
                    {
                        if (defaultCredentials.LoginUrl != null && (defaultCredentials.LoginUrl.Contains("matrix") || defaultCredentials.LoginUrl.Contains("navica")) && defaultCredentials.MLSID != "txhar")
                        {
                            context.ImageLocationType = ImageLocationType.RETSServer;
                        }

                        // Set the data provider to Trestle where applicable:
                        if (defaultCredentials.LoginUrl.ToLower().Contains("corelogic") || defaultCredentials.LoginUrl.ToLower().Contains("trestle"))
                        {
                            context.DataProvider = DataProviders.Trestle;
                        }
                    }

                    context.ShowFeaturedListingsForIDXOnly = mls.ShowFeaturedListingsForIDXOnly;
                }
            }
        }
    }
}
