﻿using MLS.Synchronization.Abstract;
using MLS.Synchronization.Images;
using MLS.Synchronization.Models;
using MLS.Synchronization.Models.Exceptions;
using NPlay;
using NPlay.Common.Abstract;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Contextual;
using NPlay.Common.Messaging.Events;
using NPlay.Common.Messaging.Payloads;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Services.Abstract;
using RESO.Connector.Abstract;
using RESO.Models;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace RESO.Synchronization.RESOData
{
    public class GetRESOData : IGetRESOFeedData
    {
        ILogger Logger;
        IRESOQueryConnectorFactory RESOQueryConnectorFactory;
        IMLSRepository MLSRepository;
        IMlsSynchronizationContextProvider MlsSynchronizationContextProvider;
        IDataFeedManager FeedManager;
        ISearchRepository SearchRepository;
        IContextualEventBroker ContextualEventBroker;
        IProcessingContext ProcessingContext;

        private List<DataProviders> SupportedDataProviders = new List<DataProviders>()
        {
            DataProviders.Bridge,
            DataProviders.MlsGrid,
            DataProviders.Paragon,
            DataProviders.Rapattoni,
            DataProviders.Spark,
            DataProviders.Trestle,
            DataProviders.UtahRealEstate,
            DataProviders.RMLS,
            DataProviders.Perchwell,
            DataProviders.OpenMLS,
        };

        public GetRESOData(ILogger logger,
            IRESOQueryConnectorFactory resoQueryConnectorFactory,
            IMLSRepository mlsRepo,
            IMlsSynchronizationContextProvider mlsSyncContextProvider,
            IDataFeedManager feedManager,
            ISearchRepository searchRepo,
            IContextualEventBroker eventBroker,
            IProcessingContext processingContext)
        {
            Logger = logger;
            RESOQueryConnectorFactory = resoQueryConnectorFactory;
            MLSRepository = mlsRepo;
            MlsSynchronizationContextProvider = mlsSyncContextProvider;
            FeedManager = feedManager;
            SearchRepository = searchRepo;
            ContextualEventBroker = eventBroker;
            ProcessingContext = processingContext;
        }

        public void Process(MLSFeedSchedulePayload payload, String query = null, int count = 0, bool forceImages = false, bool forceGeoCode = false)
        {
            ProcessingContext.IsReindexing = false;
            forceImages = payload.ForceImageDownloads;
            forceGeoCode = payload.ForceGeocoding;
            ProcessInternal(payload, query, count, forceImages, forceGeoCode);
        }

        public void Reindex(MLSFeedSchedulePayload payload, String query = null, int count = 0, bool forceImages = false, bool forceGeoCode = false)
        {
            ProcessingContext.IsReindexing = true;
            forceImages = payload.ForceImageDownloads;
            forceGeoCode = payload.ForceGeocoding;
            ProcessInternal(payload, query, count, forceImages, forceGeoCode);
        }

        public void ProcessInternal(MLSFeedSchedulePayload payload, String query = null, int count = 0, bool forceImages = false, bool forceGeoCode = false)
        {
            Logger.Info(String.Format("Received Message to process MLSID: {0} and DataFeedID: {1}", payload.MlsId, payload.DataFeedId), null, this, "Process");
            Logger.Info("Loading the data feed");
            DataFeed feed = FeedManager.GetFeedById(payload.DataFeedId);

            try
            {
                if (feed.ProtocolType != DataFeedProtocol.RESOWebApi)
                {
                    Logger.Error($"Unable to process this feed.  Incorrect Protocol {feed.ProtocolType.ToString()}", null, this, "Process");
                    throw new InvalidProtocolException($"Cannot process feeds using protocol {feed.ProtocolType.ToString()}");
                }

                MlsSynchronizationContext mlsContext = MlsSynchronizationContextProvider.Get(payload.MlsId);
                if (mlsContext == null)
                {
                    Logger.Error($"Unable to process this feed.  Missing MlsSynchronizationContext", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, payload.MlsId), new LoggingPair(LoggingKey.DataFeedID, payload.DataFeedId) }, this, "Process");
                    throw new InvalidMlsContextException($"No context is defined for RESO Synchronization for this MLS: {payload.MlsId}");
                }
                else if (!SupportedDataProviders.Contains(mlsContext.DataProvider))
                {
                    Logger.Error($"Unable to process this feed.  DataProvider not supported - {mlsContext.DataProvider.ToString()}.", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, payload.MlsId), new LoggingPair(LoggingKey.DataFeedID, payload.DataFeedId) }, this, "Process");
                    throw new InvalidMlsContextException($"No context is defined for RESO Synchronization for this MLS: {payload.MlsId}");
                }

                if (feed.State == DataFeedState.Running && !ProcessingContext.IsReprocessing)
                {
                    Logger.Warning("Feed is already running, so aborting this attempt to process.", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, payload.MlsId), new LoggingPair(LoggingKey.DataFeedID, payload.DataFeedId) }, this, "Process");
                    return;
                }

                IQueryConnector queryConnector = RESOQueryConnectorFactory.GetQueryConnector(mlsContext, feed);
                FeedManager.StartFeedRun(feed);

                RESOResults results = null;
                string nextQuery = InitializeQuery(feed, payload, query);

                while (nextQuery != null)
                {
                    Logger.Debug($"NextQuery: {nextQuery}", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, feed.MLSID), new LoggingPair(LoggingKey.RetsResource, feed.Resource), new LoggingPair(LoggingKey.RetsClass, feed.Class) }, this, "Process");
                    try
                    {
                        results = queryConnector.Get(nextQuery, feed.MLSID, feed.Resource, feed.Class);
                        nextQuery = results.NextQuery;
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("Error getting data from RESO Server", null, ex, this, "Process");
                        throw ex;
                    }

                    ProcessListingResults(payload, feed, results, forceImages, forceGeoCode);
                }

                FeedManager.EndFeedRun(feed);
                MLSRepository.SetLatestUpdate2(payload.MlsId);
            }
            catch (Exception ex)
            {
                Logger.Error("Error processing feed.", new LoggingPair[] { new LoggingPair(LoggingKey.MlsId, payload.MlsId) }, ex, this, "Process");
                if (feed != null)
                {
                    throw new DataFeedException(feed, "Error processing feed.", ex);
                }
                else
                {
                    Logger.Error("Feed was null", null, this, "Process");
                    throw new DataFeedException(feed, "Feed was null", ex);
                }

                throw ex;
            }
        }

        private void ProcessListingResults(MLSFeedSchedulePayload payload, DataFeed feed, RESOResults results, bool forceImages = false, bool forceGeoCode = false)
        {
            if (results.ResultCount > 0)
            {
                var activeResults = results.QueryResults.Where(l => l.CanView == true);
                var removedResults = results.QueryResults.Where(l => l.CanView == false);
                var unknownResults = results.QueryResults.Where(l => !l.CanView.HasValue);

                if (activeResults?.Count() > 0)
                {
                    try
                    {
                        Logger.Info("Saving Active Results to Index", null, this, "Process");
                        CleanseListingData(activeResults);
                        SaveActiveResults(activeResults);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("Saving raw rows to elastic faile for mls id " + payload.MlsId, null, ex, this, "Process");
                        throw ex;
                    }

                    Logger.Info("Publishing RAW Data Events...", null, this, "Process");
                    string index = GetIndex(feed);
                    PublishRawDataEvents(activeResults, index, forceImages: forceImages, forceGeoCode: forceGeoCode);
                }

                if (removedResults?.Count() > 0)
                {
                    Logger.Info($"Removing {removedResults.Count()} listings from this feed", new LoggingPair[] {
                                new LoggingPair(LoggingKey.MlsId, payload.MlsId),
                                new LoggingPair(LoggingKey.DataFeedID, payload.DataFeedId),
                            }, this, "Process");

                    foreach (var removedResult in removedResults)
                    {
                        Logger.Debug("Publishing remove listing event.", new LoggingPair[]
                        {
                                    new LoggingPair(LoggingKey.MlsId, payload.MlsId),
                                    new LoggingPair(LoggingKey.RawRowId, removedResult.Id),
                                    new LoggingPair(LoggingKey.PropertyListingId, removedResult.PropertyListingId),
                        }, this, "Process");

                        ContextualEventBroker.Publish<RemoveListingEvent, RemoveListingPayLoad>(new RemoveListingEvent()
                        {
                            Payload = new RemoveListingPayLoad()
                            {
                                MlsId = removedResult.MlsId,
                                PropertyListingId = removedResult.PropertyListingId
                            }
                        });
                    }
                }

                if (unknownResults?.Count() > 0)
                {
                    Logger.Error($"MLS Feed returned {unknownResults.Count()} results that do not have a field 'MlgCanView' to indicate whether or not they can be displayed.", new LoggingPair[] {
                                new LoggingPair(LoggingKey.MlsId, payload.MlsId),
                                new LoggingPair(LoggingKey.DataFeedID, payload.DataFeedId)
                            }, this, "Process");
                }
            }
            else
            {
                Logger.Info("No results returned from the RESO Server", null, this, "Process");
            }
        }

        private void CleanseListingData(IEnumerable<RESORecord> activeResults)
        {
            foreach (var record in activeResults)
            {
                // Date normalization
                if (record.Record?.ContainsKey("DaysOnMarketReplicationDate") == true)
                {
                    var val = record.Record["DaysOnMarketReplicationDate"] as string;
                    if (DateTime.TryParse(val, out var dt))
                        record.Record["DaysOnMarketReplicationDate"] = dt;
                }
                if (record.Record?.ContainsKey("SubLeaseExpirationDate") == true)
                {
                    var val = record.Record["SubLeaseExpirationDate"] as string;
                    if (DateTime.TryParse(val, out var dt))
                        record.Record["SubLeaseExpirationDate"] = dt;
                }

                // Boolean-to-int for fields mapped as long
                var boolToLongFields = new[]
                {
                    "AllowInternetAVM",
                    "AllowInternetBlogging",
                    "ShowAddressonInternet",
                    "Validated"
                };
                foreach (var key in boolToLongFields)
                {
                    if (record.Record?.ContainsKey(key) == true)
                    {
                        var value = record.Record[key];
                        if (value is bool b)
                        {
                            record.Record[key] = Convert.ToInt64(b);
                        }
                    }
                }

                // Boolean-to-string for fields mapped as string
                var boolToStringFields = new[]
                {
                    "BoardLoaded",
                    "ZeroSellingPrice"
                };
                foreach (var key in boolToStringFields)
                {
                    if (record.Record?.ContainsKey(key) == true)
                    {
                        var value = record.Record[key];
                        if (value is bool bv)
                        {
                            record.Record[key] = bv.ToString().ToLower();
                        }
                    }
                }

                // Numeric conversion for string fields
                if (record.Record?.ContainsKey("BedroomsPossible") == true)
                {
                    var value = record.Record["BedroomsPossible"];
                    if (value is string bp)
                    {
                        int bi;
                        if (int.TryParse(bp, out bi))
                        {
                            record.Record["BedroomsPossible"] = bi;
                        }
                        else
                        {
                            record.Record["BedroomsPossible"] = null;
                        }
                    }
                }

                // Double-to-int for long fields
                if (record.Record?.ContainsKey("GarageSpaces") == true)
                {
                    var value = record.Record["GarageSpaces"];
                    if (value is double gs)
                    {
                        record.Record["GarageSpaces"] = Convert.ToInt64(gs);
                    }
                }

                // Double-to-string for string fields
                if (record.Record?.ContainsKey("AssociationFee") == true)
                {
                    var value = record.Record["AssociationFee"];
                    if (value is double af)
                    {
                        record.Record["AssociationFee"] = af.ToString();
                    }
                }

                // Existing FrontageLength logic
                if (record.Record?.ContainsKey("FrontageLength") == true)
                {
                    var frontageLength = record.Record["FrontageLength"];
                    if (frontageLength is string fl)
                    {
                        if (double.TryParse(fl, out var dfl))
                        {
                            record.Record["FrontageLength"] = (int)dfl;
                        }
                        else
                        {
                            var digits = Regex.Match(fl, @"\d+").Value;
                            int ifl;
                            if (int.TryParse(digits, out ifl))
                            {
                                record.Record["FrontageLength"] = ifl;
                            }
                            else
                            {
                                record.Record["FrontageLength"] = null;
                            }
                        }
                    }
                }

                // Flatten nested objects
                Flatten("ZoningDescription", record);
                Flatten("Topography", record);
                Flatten("BusinessName", record);
                Flatten("Inclusions", record);
                Flatten("Exclusions", record);
                Flatten("Zoning", record);
            }
        }

        private void Flatten(string key, RESORecord record)
        {
            if (record.Record?.ContainsKey(key) == true)
            {
                var rawObject = record.Record[key];
                if (rawObject is Dictionary<string, object>)
                {
                    Dictionary<string, object> keyValuePairs = rawObject as Dictionary<string, object>;
                    record.Record[key] = string.Join(",", keyValuePairs.Keys.ToArray());
                }
            }
        }

        private string InitializeQuery(DataFeed feed, MLSFeedSchedulePayload payload, string query = null)
        {
            if (!String.IsNullOrWhiteSpace(query))
                return query;

            string initialQuery = feed.Query;
            DateTime lastSyncPointDateTime = DateTime.MinValue;
            string queryStart = null;
            if (payload.StartDate.HasValue)
            {
                queryStart = payload.StartDate.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");
            }
            else if (DateTime.TryParse(feed.LastSyncPoint, out lastSyncPointDateTime))
            {
                queryStart = lastSyncPointDateTime.ToString("yyyy-MM-ddTHH:mm:ssZ");
            }

            var context = MlsSynchronizationContextProvider.Get(feed.MLSID);
            string modificationDateLabel = context.ModificationTimestampFieldName;
            if (initialQuery?.Contains(context.ModificationTimestampFieldName) == false && !String.IsNullOrWhiteSpace(queryStart))
            {
                string queryStartTimeClause = $"{context.ModificationTimestampFieldName} gt {queryStart}";

                // Include the 'and' operator if this is not the beginning of the query filter
                if (!initialQuery.TrimEnd().EndsWith("="))
                    queryStartTimeClause = $" and {queryStartTimeClause}";
                initialQuery += queryStartTimeClause;
            }

            if (feed.MaxQueryBatchSize.HasValue)
            {
                initialQuery += $"&$top={feed.MaxQueryBatchSize.Value}";
            }

            return initialQuery;
        }

        private string GetIndex(DataFeed feed)
        {
            if (feed.Resource == "Property")
                return SearchIndex.RESOListings;

            if (feed.Resource == "Member")
                return SearchIndex.RESOMembers;

            if (feed.Resource == "Office")
                return SearchIndex.RESOOffices;

            return null;
        }

        private void PublishRawDataEvents(IEnumerable<RESORecord> results, string index, bool forceImages = false, bool forceGeoCode = false)
        {
            foreach (var result in results)
            {
                ContextualEventBroker.Publish<RESORawDataEvent, RESORawDataPayload>(new RESORawDataEvent()
                {
                    Payload = new RESORawDataPayload()
                    {
                        ID = result.Id,
                        MLSID = result.MlsId,
                        Index = index,
                        PropertyListingID = result.PropertyListingId,
                        ForceImageDownload = forceImages,
                        ForceGeoCode = forceGeoCode
                    }
                });
            }
        }

        protected void SaveActiveResults(IEnumerable<RESORecord> results)
        {
            SearchRepository.Save(results, SearchIndex.RESOListings);
        }
    }
}
