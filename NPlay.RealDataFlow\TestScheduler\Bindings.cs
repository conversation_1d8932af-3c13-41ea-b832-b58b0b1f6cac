﻿using AutoMapper;
using MLS.Synchronization;
using MLS.Synchronization.Abstract;
using MLS.Synchronization.GeoCoding;
using MLS.Synchronization.Images;
using MLS.Synchronization.Listings;
using MLS.Synchronization.Models;
using Ninject;
using Ninject.Extensions.Factory;
using Ninject.Modules;
using Nplay.Services.Data;
using NPlay.BusinessLogic.AgentListingWebsite;
using NPlay.Common.Abstract;
using NPlay.Common.BaseRepository;
using NPlay.Common.Compression;
using NPlay.Common.Compression.Abstract;
using NPlay.Common.Logging;
using NPlay.Common.Messaging;
using NPlay.Common.Messaging.Abstract;
using NPlay.Common.Messaging.Contextual;
using NPlay.Common.Models;
using NPlay.Common.Models.Abstract.Mls;
using NPlay.Common.Models.Mapping;
using NPlay.Common.Models.MLS;
using NPlay.Common.Queues;
using NPlay.Common.Repository;
using NPlay.Common.Search;
using NPlay.Common.Search.Abstract;
using NPlay.Common.ServiceAgents.ListingReprocessor;
using NPlay.Common.ServiceAgents.Messaging;
using NPlay.Common.ServiceAgents.MLSQuery;
using NPlay.Common.ServiceAgents.NPlayApi;
using NPlay.Common.ServiceAgents.RETSConnections;
using NPlay.Common.ServiceAgents.RETSMetadata;
using NPlay.Common.Services.Abstract;
using NPlay.Common.Services.Abstract.NPlayAPI;
using NPlay.ReadDataFlow.Data;
using NPlay.RealDataFlow.Data;
using NPlay.RealDataFlow.Mapping;
using NPlay.RealDataFlow.Mapping.Abstract;
using NPlay.RealDataFlow.Mapping.Maps;
using NPlay.RealDataFlow.Mapping.Models.Maps;
using NPlay.RealDataFlow.RETSConnector;
using NPlay.RealDataFlow.RETSConnector.Abstract;
using Quartz;
using Quartz.Spi;
using RealData.Simulation;
using RESO.Connector;
using RESO.Connector.Abstract;
using RESO.Synchronization;
using RESO.Synchronization.RESOData;
using RETS.Parsing;
using RETS.Parsing.Abstract;
using RETS.Web;
using RETS.Web.Abstract;
using RETSDataSchedulerService;
using RETSDataSchedulerService.Abstract;
using RETSDataSchedulerService.Jobs;
using RETSDataSynchronization;
using RETSDataSynchronization.Images;
using RETSDataSynchronization.MapExecution;
using RETSDataSynchronization.MapPromotion;
using RETSDataSynchronization.RETSData;

namespace TestScheduler
{
    public class Bindings : NinjectModule
    {
        public override void Load()
        {
            Bind<IUnitOfWorkFactory>().ToFactory(() => new UnitOfWorkFactory());

            Bind<IDataFeedScheduler>().To<DataFeedScheduler>();

            //Setting context
            Bind<IDBFactory<SettingsContext>>().To<DBFactory<SettingsContext>>().InThreadScope();
            Bind<IUnitOfWork>().To<UnitOfWork<SettingsContext>>();
            Bind<IRepository<Setting>>().To<EntityRepositoryBase<Setting, SettingsContext>>();

            Bind<IDBFactory<MapContext>>().To<DBFactory<MapContext>>().InTransientScope();
            Bind<IUnitOfWork>().To<UnitOfWork<MapContext>>().InTransientScope().Named("Map");
            Bind<IRepository<Map>>().To<EntityRepositoryBase<Map, MapContext>>();
            Bind<IRepository<MapField>>().To<EntityRepositoryBase<MapField, MapContext>>();
            Bind<IRepository<MapNote>>().To<EntityRepositoryBase<MapNote, MapContext>>();
            Bind<IRepository<MapMatch>>().To<EntityRepositoryBase<MapMatch, MapContext>>();
            Bind<IRepository<MapPropertyType>>().To<EntityRepositoryBase<MapPropertyType, MapContext>>();
            Bind<IRepository<Related>>().To<EntityRepositoryBase<Related, MapContext>>();
            Bind<IRepository<MappingListingStatus>>().To<EntityRepositoryBase<MappingListingStatus, MapContext>>();

            //Mls Context
            Bind<IDBFactory<MlsContext>>().To<DBFactory<MlsContext>>().InTransientScope();
            Bind<IUnitOfWork>().To<UnitOfWork<MlsContext>>().InTransientScope().Named("Mls");
            Bind<IRepository<MultipleListingService>>().To<EntityRepositoryBase<MultipleListingService, MlsContext>>();
            Bind<IRepository<RETSCredential>>().To<EntityRepositoryBase<RETSCredential, MlsContext>>();
            Bind<IRepository<RESOCredential>>().To<EntityRepositoryBase<RESOCredential, MlsContext>>();
            Bind<IRepository<MLSParameter>>().To<EntityRepositoryBase<MLSParameter, MlsContext>>();
            Bind<IRepository<DataFeed>>().To<EntityRepositoryBase<DataFeed, MlsContext>>();
            Bind<IRepository<MLSActiveStatus>>().To<EntityRepositoryBase<MLSActiveStatus, MlsContext>>();
            Bind<IRepository<MLSMaster>>().To<EntityRepositoryBase<MLSMaster, MlsContext>>();
            Bind<IRepository<State>>().To<EntityRepositoryBase<State, MlsContext>>();
            Bind<IMLSRepository>().To<MLSRepository>();
            Bind<ICachedMLSRepository>().To<MLSCache>().InSingletonScope();
            Bind<IListingStatusCacher>().To<ListingStatusCacher>();
            Bind<IListingStatusProvider>().To<ListingStatusProvider>();

            // Listing reprocessor
            Bind<IListingProcessorClient>().To<ListingProcessorClient>();
            Bind<IListingIndexConfigurator>().To<ListingIndexConfigurator>();

            Bind<ICanonAgentSaveToSQL>().To<CanonAgentSaveToSQL>();

            //MLS_Agents context
            Bind<IDBFactory<MLS_AgentContext>>().To<DBFactory<MLS_AgentContext>>();
            Bind<IUnitOfWork>().To<UnitOfWork<MLS_AgentContext>>().Named("MlsAgent");
            Bind<IRepository<MLSAgent>>().To<EntityRepositoryBase<MLSAgent, MLS_AgentContext>>();

            //MLS Synchronization Context
            Bind<IMlsSynchronizationContextProvider>().ToMethod(p =>
            {
                MlsSynchronizationContextProvider.Instance.MLSCache = MlsSynchronizationContextProvider.Instance.MLSCache ?? this.Kernel.Get<ICachedMLSRepository>();
                return MlsSynchronizationContextProvider.Instance;
            }).InSingletonScope();

            Bind<IJobFactory>().To<JobFactory>();

            Bind<IRETSQueryHandler>().To<RETSQueryHandler>();

            Bind<IJob>().To<DataFeedJob>();

            Bind<IRETSSearchConnector>().To<RETSSearchConnector>().InThreadScope();
            Bind<IRETSMetadataConnector>().To<RETSMetadataConnector>().InThreadScope();
            Bind<IFTPConnector>().To<FTPConnector>();
            Bind<IRETSParser>().To<RETSParser>();
            Bind<IRETSWebClientFactory>().To<RETSWebClientFactory>();
            Bind<IRETSConnectionManagerClient>().To<RETSConnectionManagerClient>();

            Bind<ISearchRepository>().To<SearchRepository>().InThreadScope();

            //Logging
            Bind<ILogger>().To<Log4NetLogger>();

            //event broker
            Bind<IEventBroker>().To<TrackingEventBroker>().InSingletonScope();
            Bind<IEventPrismFactory>().To<RabbitEventPrismFactory>().InSingletonScope();
            Bind<IQueueFactory>().To<RabbitQueueFactory>().InSingletonScope();
            Bind<IContextualEventBroker>().To<ContextualEventBroker>().InSingletonScope();
            Bind<IProcessingContext>().ToMethod(c => new ProcessingContext() { IsReprocessing = true }).InThreadScope();

            Bind<IRealDataMapper>().To<RealDataMapper>();

            Bind<IPromoteMap>().To<PromoteMap>();

            Bind<IMetadataHandler>().To<MetadataHandler>();
            Bind<IMetadataFileHandler>().To<MetadataFileHandler>();
            Bind<IMetadataCacheHandler>().To<MetadataCacheHandler>();
            Bind<ICompressor>().To<GZipCompressor>();
            Bind<IRETSMetadataManagerClient>().To<RETSMetadataManagerClient>();

            // Remove listing service:
            Bind<IRemoveListingService>().To<RemoveListingService>();
            Bind<IDBFactory<AgentContext>>().To<DBFactory<AgentContext>>();
            Bind<IRepository<Agent>>().To<EntityRepositoryBase<Agent, AgentContext>>();
            Bind<IRepository<ZipCode>>().To<EntityRepositoryBase<ZipCode, AgentContext>>();
            Bind<IAgentListingSiteManager>().To<AgentListingSiteManager>();
            Bind<IListingImageLazyLoadManager>().To<ListingImageLazyLoadManager>().InSingletonScope();

            // Listing Translation to Active
            Bind<IActiveListingService>().To<ActiveListingService>();
            Bind<IListingImageManager>().To<ListingImageManager>();
            Bind<IActiveListingRepository>().To<ActiveListingRepository>();
            Bind<IListingTypeAheadManager>().To<ListingTypeAheadManager>();
            Bind<IListingChangeNotifier>().To<ListingChangeNotifier>();
            Bind<IListingChangeConfigurationProvider>().To<ListingChangeConfigurationProvider>().InSingletonScope();
            Bind<ICanonTranslator>().To<CanonTranslator>();
            Bind<ICanonImageTranslator>().To<CanonImageTranslator>();
            Bind<IMlsSynchronizationClient>().To<MlsSynchronizationClient>();

            // Simulations
            Bind<IDataFeedSynchronizationSimulator>().To<DataFeedSynchronizationSimulator>();

            // RETS Data Feeds
            Bind<IGetMLSFeedData>().To<GetRETSData>().InThreadScope();
            Bind<IDataFeedManager>().To<DataFeedManager>().InThreadScope();
            Bind<IRealDataClient>().To<RealDataClient>().InThreadScope();
            Bind<IMapRETSData>().To<MapRETSData>().InThreadScope();
            Bind<IRETSImageDownloader>().To<RETSImageDownloader>().InThreadScope();
            Bind<IRETSImagesConnector>().To<RETSImagesConnector>().InThreadScope();
            Bind<IImageStreamDownloader>().To<ImageStreamDownloader>().InThreadScope();

            // RESO Data Feeds
            Bind<IGetRESOFeedData>().To<GetRESOData>().InThreadScope();
            Bind<IMapRESOData>().To<MapRESOData>().InThreadScope();
            Bind<IRESOQueryConnectorFactory>().To<RESOQueryConnectorFactory>();
            Bind<IBridgeQueryConnector>().To<BridgeQueryConnector>().InThreadScope();
            Bind<ITrestleQueryConnector>().To<ThrottledTrestleQueryConnector>().InThreadScope();
            Bind<IMlsGridListingQueryConnector>().To<ThrottledMlsGridListingQueryConnector>().InThreadScope();
            Bind<IMLSQueryClient>().To<MLSQueryClient>().InThreadScope();
            Bind<IBridgeMediaConnector>().To<BridgeMediaConnector>().InThreadScope();
            Bind<IRapattoniMediaConnector>().To<RapattoniMediaConnector>().InThreadScope();
            Bind<IRapattoniQueryConnector>().To<RapattoniQueryConnector>().InThreadScope();
            Bind<ITrestleMediaConnector>().To<TrestleMediaConnector>().InThreadScope();
            Bind<IMlsGridMediaConnector>().To<MlsGridMediaConnector>().InThreadScope();
            Bind<IUtahRealEstateQueryConnector>().To<UtahRealEstateQueryConnector>();
            Bind<IUtahRealEstateMediaConnector>().To<UtahRealEstateMediaConnector>();
            Bind<IRMLSQueryConnector>().To<RMLSQueryConnector>();
            Bind<IRMLSMediaConnector>().To<RMLSMediaConnector>();
            Bind<ISparkQueryConnector>().To<SparkQueryConnector>();
            Bind<ISparkMediaConnector>().To<SparkMediaConnector>();
            Bind<IParagonQueryConnector>().To<ParagonQueryConnector>();
            Bind<IParagonMediaConnector>().To<ParagonMediaConnector>();
            Bind<IPerchwellQueryConnector>().To<PerchwellQueryConnector>();
            Bind<IPerchwellMediaConnector>().To<PerchwellMediaConnector>();
            Bind<IOpenMLSQueryConnector>().To<OpenMLSQueryConnector>();
            Bind<IOpenMLSMediaConnector>().To<OpenMLSMediaConnector>();
            Bind<IRESOImageDownloader>().To<RESOImageDownloader>().InThreadScope();
            Bind<IGeoCoder>().To<GeoCoder>();
            Bind<IRawListingRepository>().To<RawListingRepository>();

            Bind<IDelayedEventClient>().To<DelayedEventClient>();

            Bind<IImageUploader>().To<CIFSImageUploader>();

            Bind<IRepository<Membership>>().To<EntityRepositoryBase<Membership, PaymentContext>>();

            //AutoMapper
            Bind<IAutoMapperConfigurationProvider>().ToMethod(context => {
                var provider = new AutoMapperConfigurationProvider();

                provider.AddProfile<AgentMappingProfile>();
                provider.AddProfile<MatchedAgentMappingProfile>();
                provider.AddProfile<EsignMappingProfile>();
                provider.AddProfile<LeadMappingProfile>();
                provider.AddProfile<MLSPropertyMappingProfile>();
                provider.AddProfile<ServiceMappingProfile>();
                provider.AddProfile<SweepstakesMappingProfile>();
                provider.AddProfile<TransactionLogMappingProfile>();
                provider.AddProfile<MLSPropertyListingMappingProfile>();

                return provider;
            }).InSingletonScope();

            Bind<MapperConfiguration>().ToProvider<IAutoMapperConfigurationProvider>().InSingletonScope();
            Bind<IMapper>().ToMethod(context => context.Kernel.Get<MapperConfiguration>().CreateMapper());
        }
    }
}
