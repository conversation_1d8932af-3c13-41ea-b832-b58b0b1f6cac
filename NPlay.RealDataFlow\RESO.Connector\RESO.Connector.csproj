﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D31C3837-E7A3-4E3A-8057-088986AA5EF1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RESO.Connector</RootNamespace>
    <AssemblyName>RESO.Connector</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <OutputPath>bin\Test\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|x64'">
    <OutputPath>bin\x64\Test\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=8.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.8.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=105.2.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.105.2.3\lib\net46\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Abstract\IPerchwellMediaConnector.cs" />
    <Compile Include="Abstract\IPerchwellQueryConnector.cs" />
    <Compile Include="Abstract\IRESOQueryConnectorFactory.cs" />
    <Compile Include="Abstract\IRapattoniMediaConnector.cs" />
    <Compile Include="Abstract\IRapattoniQueryConnector.cs" />
    <Compile Include="Abstract\IParagonMediaConnector.cs" />
    <Compile Include="Abstract\IOpenMLSMediaConnector.cs" />
    <Compile Include="Abstract\ISparkMediaConnector.cs" />
    <Compile Include="Abstract\IParagonQueryConnector.cs" />
    <Compile Include="Abstract\IOpenMLSQueryConnector.cs" />
    <Compile Include="Abstract\IRMLSMediaConnector.cs" />
    <Compile Include="Abstract\IUtahRealEstateMediaConnector.cs" />
    <Compile Include="Abstract\ITrestleMediaConnector.cs" />
    <Compile Include="Abstract\IBridgeMediaConnector.cs" />
    <Compile Include="Abstract\ISparkQueryConnector.cs" />
    <Compile Include="Abstract\IRMLSQueryConnector.cs" />
    <Compile Include="Abstract\IUtahRealEstateQueryConnector.cs" />
    <Compile Include="Abstract\ITrestleQueryConnector.cs" />
    <Compile Include="Abstract\IMlsGridListingQueryConnector.cs" />
    <Compile Include="Abstract\IMlsGridMediaConnector.cs" />
    <Compile Include="Abstract\IBridgeQueryConnector.cs" />
    <Compile Include="Abstract\IMediaConnector.cs" />
    <Compile Include="CanonicalMediaConnector.cs" />
    <Compile Include="MediaDownload\MultipartImage.cs" />
    <Compile Include="MediaDownload\MultipartParser.cs" />
    <Compile Include="MlsGridListingQueryConnector.cs" />
    <Compile Include="PerchwellMediaConnector.cs" />
    <Compile Include="PerchwellQueryConnector.cs" />
    <Compile Include="RESOQueryConnectorFactory.cs" />
    <Compile Include="RapattoniQueryConnector.cs" />
    <Compile Include="RapattoniMediaConnector.cs" />
    <Compile Include="ParagonMediaConnector.cs" />
    <Compile Include="ParagonQueryConnector.cs" />
    <Compile Include="OpenMLSMediaConnector.cs" />
    <Compile Include="OpenMLSQueryConnector.cs" />
    <Compile Include="ThrottledTrestleQueryConnector.cs" />
    <Compile Include="SparkMediaConnector.cs" />
    <Compile Include="RMLSMediaConnector.cs" />
    <Compile Include="UtahRealEstateMediaConnector.cs" />
    <Compile Include="TrestleMediaConnector.cs" />
    <Compile Include="BridgeMediaConnector.cs" />
    <Compile Include="SparkQueryConnector.cs" />
    <Compile Include="RMLSQueryConnector.cs" />
    <Compile Include="UtahRealEstateQueryConnector.cs" />
    <Compile Include="TrestleQueryConnector.cs" />
    <Compile Include="Exceptions\RESOException.cs" />
    <Compile Include="Abstract\IQueryConnector.cs" />
    <Compile Include="ThrottledMlsGridListingQueryConnector.cs" />
    <Compile Include="MlsGridMediaConnector.cs" />
    <Compile Include="MediaConnector.cs" />
    <Compile Include="BridgeQueryConnector.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QueryConnector.cs" />
    <Compile Include="RESOConnectorBase.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\MLS.Synchronization.Images\MLS.Synchronization.Images.csproj">
      <Project>{af6000a4-0b3a-461d-a9b6-fd098b54a9bc}</Project>
      <Name>MLS.Synchronization.Images</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\MLS.Synchronization.Models\MLS.Synchronization.Models.csproj">
      <Project>{b851fde4-b17c-4256-b296-9d8fece6a3bc}</Project>
      <Name>MLS.Synchronization.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Helpers\NPlay.Common.Helpers.csproj">
      <Project>{76637126-033b-4277-b677-c74b8c3b2e5d}</Project>
      <Name>NPlay.Common.Helpers</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Models\NPlay.Common.Models.csproj">
      <Project>{ABE52228-12D2-4C13-A8D8-9CA9BDA61B7D}</Project>
      <Name>NPlay.Common.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\NPlay.Common.Services.Abstract\NPlay.Common.Services.Abstract.csproj">
      <Project>{f06efa62-ef3a-4e42-8bf0-e6ce50d23907}</Project>
      <Name>NPlay.Common.Services.Abstract</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\RESO.Models\RESO.Models.csproj">
      <Project>{463e74fd-e6ce-49c4-8b5d-a879d7238f32}</Project>
      <Name>RESO.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\BusinessLogic\MLS.Synchronization.Abstract\MLS.Synchronization.Abstract.csproj">
      <Project>{3312698A-5134-4E58-B0B6-D76272F27A90}</Project>
      <Name>MLS.Synchronization.Abstract</Name>
    </ProjectReference>
    <ProjectReference Include="..\BusinessLogic\MLSAccounts\MLSAccounts.csproj">
      <Project>{a418175c-396e-4432-81f9-f340c3bebfec}</Project>
      <Name>MLSAccounts</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>